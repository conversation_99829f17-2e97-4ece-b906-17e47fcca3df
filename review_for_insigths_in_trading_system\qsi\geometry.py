import numpy as np
import scipy.sparse
import scipy.sparse.linalg as spla
from scipy.linalg import expm
from functools import lru_cache
from pathlib import Path
from typing import Optional
import os

from qsi.utils import is_unitary
from qsi.validation import validate_quantum_state

@lru_cache(maxsize=None)
def _compute_phi_unitary(dim: int, phi: float) -> np.ndarray:
    """Construção pesada da matriz unitária baseada em ``dim`` e ``phi``."""
    diag_vals = np.arange(dim, dtype=np.float64)
    off_diag_vals = phi ** (-np.arange(1, dim))

    H = scipy.sparse.diags(
        [off_diag_vals, diag_vals, off_diag_vals],
        [-1, 0, 1],
        shape=(dim, dim),
        dtype=np.complex128,
    )

    norm_factor = scipy.sparse.linalg.norm(H, "fro")
    if norm_factor > 0:
        H = H / norm_factor

    U = expm(-1j * H.toarray())
    assert is_unitary(U), "A matriz gerada não é unitária."
    return U


def _cached_phi_unitary(
    dim: int, phi: float, cache_dir: Optional[str] = None
) -> np.ndarray:
    """Recupera matriz unitária do cache em memória/disco ou a constrói."""

    file_path: Optional[Path] = None
    if cache_dir:
        phi_str = f"{phi:.8f}".replace(".", "_")
        file_path = Path(cache_dir) / f"phi_unitary_{dim}_{phi_str}.npy"
        if file_path.exists():
            try:
                loaded = np.load(file_path, allow_pickle=False)
                if loaded.shape == (dim, dim):
                    return loaded
            except Exception:
                pass

    U = _compute_phi_unitary(dim, phi)

    if file_path is not None:
        try:
            os.makedirs(file_path.parent, exist_ok=True)
            np.save(file_path, U)
        except Exception:
            pass

    return U


def build_phi_unitary(
    dim: int, phi: float = (1 + 5**0.5) / 2, *, cache_dir: Optional[str] = None
) -> np.ndarray:
    """
    Gera uma matriz de transformação unitária ``U`` inspirada na proporção áurea
    (φ). Resultados são armazenados em cache para acelerar chamadas futuras.
    """
    if not isinstance(dim, int) or dim < 2:
        raise ValueError("'dim' deve ser um inteiro maior ou igual a 2")
    if phi <= 0:
        raise ValueError("'phi' deve ser positivo")

    # Recupera a matriz do cache (ou gera se necessário)
    return _cached_phi_unitary(dim, float(phi), cache_dir=cache_dir).copy()
