import sys
import os
import json

# Adiciona o diretório raiz do projeto ao path para permitir a importação do qsi
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from qsi.system import QSI

def main():
    """
    Ponto de entrada principal para a execução do Quantum Synapse Integrator.

    Este script demonstra o uso da classe QSI unificada para avaliar um
    repositório externo com um único comando.
    """
    # URL de um repositório mock para demonstração
    mock_repo_url = "https://github.com/qiskit-community/prototype-quantum-kernel-machine-learning"
    
    # A dimensionalidade do espaço de Hilbert para a cognição do sistema
    hilbert_space_dims = 8

    # 1. Instancia o sistema QSI completo
    system = QSI(dimensions=hilbert_space_dims, phi1=1.618, phi2=2.618)

    # 2. Executa a avaliação com um único comando
    result = system.evaluate(repo_url=mock_repo_url)

    # 3. Exibe o resultado final de forma clara
    print("\n" + "="*50)
    print("            RESULTADO FINAL DA AVALIAÇÃO DO QSI")
    print("="*50)
    print(f"  Repositório: {mock_repo_url}")
    print(f"  Veredito Final: {result.get('decision', 'INDETERMINADO')}")
    print("\n  Métricas de Ressonância:")
    
    metrics = result.get('metrics', {})
    for key, value in metrics.items():
        print(f"    - {key.replace('_', ' ').capitalize():<25}: {value:.4f}")
    
    if result.get('reason'):
        print(f"\n  Justificativa: {result['reason']}")
        
    print("="*50)


if __name__ == "__main__":
    main()
