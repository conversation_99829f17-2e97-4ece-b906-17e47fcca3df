import os
import tempfile
from dataclasses import dataclass, field
from typing import Dict, List

from qsi.utils import load_json, save_json
from qualia.utils.logger import get_logger

logger = get_logger(__name__)

@dataclass
class QuantumMemoryBank:
    """Armazena e agrega métricas de avaliações anteriores."""

    file_path: str | None = None
    records: List[Dict[str, float]] = field(default_factory=list)

    def __post_init__(self) -> None:
        if self.file_path is None:
            self.file_path = os.path.join(tempfile.gettempdir(), "qsi_memory.json")
        self._load()

    def _load(self) -> None:
        data = load_json(self.file_path, default=[])
        if isinstance(data, list):
            self.records = [dict(item) for item in data]

    def _save(self) -> None:
        save_json(self.records, self.file_path)

    def add_metrics(self, metrics: Dict[str, float]) -> None:
        self.records.append(metrics)
        self._save()

    def aggregate_metrics(self) -> Dict[str, float]:
        if not self.records:
            return {}
        # Considera todos os tipos de métricas existentes no histórico
        keys = set().union(*(rec.keys() for rec in self.records))
        sums = {k: 0.0 for k in keys}
        for rec in self.records:
            for k in keys:
                sums[k] += rec.get(k, 0.0)
        return {k: sums[k] / len(self.records) for k in keys}
