"""QUALIA Farsight analytical engine.

Fetches open-data (currently arXiv only) and detect latent innovation clusters.
This is an MVP: quick, dependency-light and runs fully in memory so it can be
invoked on demand by the Flask endpoint.

Roadmap (not implemented here):
1. Persist incremental history to estimate true velocity/acceleration.
2. Bring additional feeds (GitHub, patents, Twitter).
3. Replace TF-IDF + K-Means with semantic embeddings + HDBSCAN.

``ENABLE_V2`` (defined in :mod:`qualia.farsight`) switches the implementation
to a modular pipeline where fetching and embedding are handled by dedicated
components. Set ``FAR_V2=1`` in the environment to activate it.
"""

from __future__ import annotations

from qualia.utils.logger import get_logger
from qualia.farsight import ENABLE_V2
from qualia.farsight.embedder import Embedder

try:  # Analyzer may be stubbed out during unit tests
    from qualia.farsight.analyzer import Analyzer
except Exception:  # pragma: no cover - fallback when module missing
    Analyzer = None  # type: ignore[misc]
from qualia.farsight.fetcher import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>F<PERSON>cher
from qualia.persistence.farsight_history import FarsightHistory
from datetime import datetime, timedelta, timezone
from collections import defaultdict
import numpy as np
from typing import Any, Callable, Dict, List, Optional

import os

from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.cluster import KMeans
from sklearn.metrics import pairwise_distances

try:
    import hdbscan  # type: ignore
except ModuleNotFoundError:  # pragma: no cover
    hdbscan = None  # type: ignore
from datadog import DogStatsd
from qualia.events import CognitiveAccelerationEvent
from ..memory.event_bus import SimpleEventBus

try:  # Optional dependency for fetching papers
    import arxiv  # type: ignore
except ModuleNotFoundError:  # pragma: no cover - best-effort fallback
    arxiv = None  # type: ignore[misc]

logger = get_logger(__name__)


class FarsightEngine:
    """Lightweight innovation-discovery engine.

    When ``ENABLE_V2`` is ``True`` the engine uses the modular pipeline from
    :mod:`qualia.farsight`, which relies on :class:`PaperFetcher` to obtain
    papers and on :class:`Analyzer`/:class:`Embedder` to generate embeddings.
    Custom instances of these components can be injected via ``__init__``.

    Parameters
    ----------
    days_back : int, optional
        Window of days to search. Defaults to ``7``.
    max_results : int, optional
        Maximum number of papers to fetch. Defaults to ``100``.
    categories : str, optional
        Space-separated arXiv categories (without ``cat:`` prefix). Defaults to
        ``"cs.AI OR cs.LG"`` or ``FAR_CATEGORIES`` environment variable.
    """

    def __init__(
        self,
        days_back: int = 7,
        max_results: int = 100,
        *,
        statsd_client: Optional[DogStatsd] = None,
        event_bus: Optional[SimpleEventBus] = None,
        acceleration_callback: Optional[Callable[[Dict[str, Any]], None]] = None,
        cognitive_acceleration_threshold: float = 0.2,
        embedder: Embedder | None = None,
        analyzer: Analyzer | None = None,
        paper_fetcher: PaperFetcher | None = None,
        github_fetcher: GitHubFetcher | None = None,
        twitter_fetcher: TwitterFetcher | None = None,
        history_store: FarsightHistory | None = None,
        categories: Optional[str] = None,
    ) -> None:
        self.days_back = days_back
        self.max_results = max_results
        self.categories = categories or os.getenv("FAR_CATEGORIES", "cs.AI OR cs.LG")
        self.statsd = statsd_client
        self.event_bus = event_bus
        self.acceleration_callback = acceleration_callback
        self.cognitive_accel_threshold = cognitive_acceleration_threshold
        self.previous_burst_eta: Optional[float] = None
        self._papers: List[Any] = []
        self._clusters: List[Dict[str, object]] = []
        self.embedder = embedder
        self.analyzer = analyzer
        self.paper_fetcher = paper_fetcher
        self.github_fetcher = github_fetcher
        self.twitter_fetcher = twitter_fetcher
        self.history_store = history_store or FarsightHistory()
        if ENABLE_V2:
            self.embedder = self.embedder or Embedder()
            if self.analyzer is None and Analyzer is not None:
                self.analyzer = Analyzer(embedder=self.embedder)
            self.paper_fetcher = self.paper_fetcher or PaperFetcher(
                days_back=self.days_back,
                max_results=self.max_results,
                categories=self.categories,
            )
            self.github_fetcher = self.github_fetcher or GitHubFetcher(
                max_results=self.max_results
            )
            self.twitter_fetcher = self.twitter_fetcher or TwitterFetcher(
                max_results=min(20, self.max_results)
            )

    def run(self) -> List[Dict[str, object]]:
        """Execute full pipeline and return insights."""
        if ENABLE_V2:
            self._fetch_papers_v2()
        else:
            self._fetch_papers()
        if not self._papers:
            logger.warning("Nenhum paper arXiv obtido – retornando vazio")
            return []
        if ENABLE_V2:
            self._cluster_papers_v2()
        else:
            self._cluster_papers()
        if self.history_store is not None and self._clusters:
            self.history_store.append_batch(self._clusters)
        curvature_values = [c["curvature"] for c in self._clusters]
        velocity_values = [c["velocity"] for c in self._clusters]
        curv_avg = float(np.mean(curvature_values)) if curvature_values else 0.0
        burst_eta = float(np.mean(velocity_values)) if velocity_values else 0.0
        if self.statsd is not None:
            confidence = min(1.0, len(self._clusters) / float(self.max_results))
            self.statsd.gauge("metacog.curvature_avg", curv_avg)
            self.statsd.gauge("metacog.burst_eta", burst_eta)
            self.statsd.gauge("metacog.confidence", confidence)

        if self.previous_burst_eta is not None:
            acceleration = burst_eta - self.previous_burst_eta
            if acceleration > self.cognitive_accel_threshold:
                payload = CognitiveAccelerationEvent(
                    acceleration=float(acceleration),
                    timestamp=datetime.now(timezone.utc),
                )
                payload_dict = {
                    "acceleration": payload.acceleration,
                    "timestamp": payload.timestamp,
                }
                if self.event_bus is not None:
                    self.event_bus.publish(
                        "farsight.cognitive_acceleration", payload_dict
                    )
                if self.acceleration_callback is not None:
                    self.acceleration_callback(payload_dict)
        self.previous_burst_eta = burst_eta

        return self._clusters

    # ---------------------------------------------------------------------
    # Internal helpers
    # ---------------------------------------------------------------------

    def _fetch_papers(self) -> None:
        """Fetch recent papers from arXiv AI-related categories."""
        if arxiv is None:  # dependency not available
            logger.warning("arxiv package ausente; _fetch_papers() ignorado")
            self._papers = []
            return

        # arXiv retorna datas *naive* (sem tz). Para evitar TypeError ao comparar
        # com datetimes `timezone.utc`, convertemos ambas para naive UTC.
        start_time = datetime.utcnow() - timedelta(days=self.days_back)
        query = f"cat:{self.categories}"
        try:
            search = arxiv.Search(
                query=query,
                max_results=self.max_results,
                sort_by=arxiv.SortCriterion.SubmittedDate,
            )
            papers: List[arxiv.Result] = []
            for result in search.results():
                pub_date = result.published
                if pub_date.tzinfo is not None:
                    pub_date = pub_date.replace(tzinfo=None)
                if pub_date > start_time:
                    papers.append(result)
                else:
                    break  # results are sorted by date desc
            self._papers = papers
            logger.info("FarsightEngine: obtidos %d papers recentes", len(papers))
        except Exception as exc:  # pragma: no cover - best-effort logging
            logger.error("FarsightEngine: erro ao buscar papers: %s", exc)
            self._papers = []

    def _cluster_papers(self) -> None:
        """TF-IDF + k-means clustering to group topics."""
        docs = [p.title + " " + (p.summary or "") for p in self._papers]
        vectorizer = TfidfVectorizer(stop_words="english", max_features=4096)
        X = vectorizer.fit_transform(docs)
        # Escolha robusta de k: pelo menos 1 e nunca mais que o número de amostras
        n_clusters = max(1, min(8, len(self._papers), max(1, len(self._papers) // 10)))
        km = KMeans(n_clusters=n_clusters, n_init="auto", random_state=42)
        labels = km.fit_predict(X)
        terms = vectorizer.get_feature_names_out()

        clusters: Dict[int, Dict[str, object]] = defaultdict(
            lambda: {
                "topic_terms": defaultdict(float),
                "indices": [],
            }
        )
        for idx, label in enumerate(labels):
            clusters[label]["indices"].append(idx)
            # accumulate top tf-idf terms
            row = X[idx]
            for col in row.nonzero()[1]:
                clusters[label]["topic_terms"][terms[col]] += row[0, col]

        insights: List[Dict[str, object]] = []
        for cluster in clusters.values():
            idxs = cluster["indices"]
            # topic: top 4 terms
            term_scores = cluster["topic_terms"]
            top_terms = sorted(term_scores.items(), key=lambda t: t[1], reverse=True)[
                :4
            ]
            topic = " ".join(t for t, _ in top_terms)

            # velocity: paper count ratio
            velocity = len(idxs) / len(self._papers)

            # curvature: mean pairwise dist to centroid
            sub_X = X[idxs]
            centroid_vec = sub_X.mean(axis=0)
            # Converte de numpy.matrix ou matriz esparsa para ndarray 1-D
            if hasattr(centroid_vec, "A1"):
                centroid_vec = centroid_vec.A1  # sparse -> 1-D ndarray
            else:
                centroid_vec = np.asarray(centroid_vec).ravel()
            curvature = float(
                pairwise_distances(sub_X, centroid_vec.reshape(1, -1)).mean()
            )

            sources = [
                {
                    "title": self._papers[i].title,
                    "link": self._papers[i].entry_id,
                }
                for i in idxs[:5]
            ]
            insights.append(
                {
                    "topic": topic.title(),
                    "curvature": round(curvature, 3),
                    "velocity": round(velocity, 3),
                    "prediction_window": "TBD",
                    "sources": sources,
                }
            )
        # sort by curvature descending then velocity ascending (idea nascente)
        insights.sort(key=lambda x: (-x["curvature"], x["velocity"]))
        self._clusters = insights

    def _fetch_papers_v2(self) -> None:
        """Fetch data from multiple sources."""
        self._papers = (
            self.paper_fetcher.run() if self.paper_fetcher is not None else []
        )
        self._repos = (
            self.github_fetcher.run() if self.github_fetcher is not None else []
        )
        self._tweets = (
            self.twitter_fetcher.run() if self.twitter_fetcher is not None else []
        )
        self._items: List[Dict[str, Any]] = []
        for p in self._papers:
            self._items.append(
                {
                    "title": p.title,
                    "summary": p.summary or "",
                    "link": p.entry_id,
                }
            )
        for r in self._repos:
            self._items.append(
                {
                    "title": r.get("full_name", r.get("name", "")),
                    "summary": r.get("description", ""),
                    "link": r.get("html_url"),
                }
            )
        for t in self._tweets:
            self._items.append(
                {
                    "title": t.get("text", ""),
                    "summary": "",
                    "link": f"https://twitter.com/i/web/status/{t.get('id')}",
                }
            )

    def _cluster_papers_v2(self) -> None:
        """Cluster items using embeddings and HDBSCAN."""
        if self.analyzer is None or hdbscan is None:
            self._clusters = []
            return

        docs = [item["title"] + " " + item.get("summary", "") for item in self._items]
        if not docs:
            self._clusters = []
            return

        embeddings = np.vstack([self.analyzer.embed_text(doc) for doc in docs])
        vectorizer = TfidfVectorizer(stop_words="english", max_features=4096)
        tfidf_matrix = vectorizer.fit_transform(docs)
        terms = vectorizer.get_feature_names_out()

        clusterer = hdbscan.HDBSCAN(min_cluster_size=3)
        labels = clusterer.fit_predict(embeddings)

        clusters: Dict[int, Dict[str, object]] = defaultdict(
            lambda: {"topic_terms": defaultdict(float), "indices": []}
        )
        for idx, label in enumerate(labels):
            if label == -1:
                continue
            clusters[label]["indices"].append(idx)
            row = tfidf_matrix[idx]
            for col in row.nonzero()[1]:
                clusters[label]["topic_terms"][terms[col]] += row[0, col]

        insights: List[Dict[str, object]] = []
        for cluster in clusters.values():
            idxs = cluster["indices"]
            term_scores = cluster["topic_terms"]
            top_terms = sorted(term_scores.items(), key=lambda t: t[1], reverse=True)[
                :4
            ]
            topic = " ".join(t for t, _ in top_terms)

            velocity = len(idxs) / len(self._items)
            sub_emb = embeddings[idxs]
            centroid_vec = sub_emb.mean(axis=0)
            curvature = float(
                pairwise_distances(sub_emb, centroid_vec.reshape(1, -1)).mean()
            )

            sources = [
                {
                    "title": self._items[i]["title"],
                    "link": self._items[i]["link"],
                }
                for i in idxs[:5]
            ]
            insights.append(
                {
                    "topic": topic.title(),
                    "curvature": round(curvature, 3),
                    "velocity": round(velocity, 3),
                    "prediction_window": "TBD",
                    "sources": sources,
                }
            )
        insights.sort(key=lambda x: (-x["curvature"], x["velocity"]))
        self._clusters = insights
