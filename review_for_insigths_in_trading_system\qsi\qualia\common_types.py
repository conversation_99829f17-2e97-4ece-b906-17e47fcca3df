# coding: utf-8
"""
Tipos comuns utilizados em múltiplos módulos do QUALIA.
Definições centralizadas para evitar importações circulares.
"""

from __future__ import annotations

import time
import uuid
from typing import Dict, List, Any, Optional

from pydantic import BaseModel, Field
from dataclasses import dataclass


class SnapshotPayload(BaseModel):
    """Payload enviado ao frontend a cada ``on_tick``."""

    timestamp: str
    hue: float
    brightness: float
    embedding_norm: float
    liquidity_buckets: Optional[List[float]] | None = None
    trend_strength: Optional[float] = None
    delta_entropy: Optional[float] = None


class QuantumSignaturePacket(BaseModel):
    """
    Pacote de assinatura quântica que contém o vetor de assinatura, métricas associadas,
    detalhes da fonte e metadados de identificação.

    Utilizado para representar padrões quânticos reconhecíveis e para intercâmbio
    entre QUALIAQuantumUniverse e QuantumMetacognitionLayer.
    """

    id: str = Field(default_factory=lambda: f"qsp_{uuid.uuid4().hex[:8]}")
    vector: List[float]  # O vetor de assinatura em si
    metrics: Dict[str, Any] = Field(
        default_factory=dict
    )  # Métricas pré-calculadas (entropia, otoc, coerência, etc.)
    per_encoder_metrics: Optional[Dict[str, Dict[str, float]]] = Field(
        default_factory=dict
    )  # Métricas específicas por encoder (ex: p0, p1, expected_value)
    source_details: Dict[str, Any] = Field(
        default_factory=dict
    )  # Detalhes de origem (n_qubits, method, vector_type)
    label: Optional[str] = None  # Rótulo opcional (pode ser definido pela metacognição)
    timestamp: float = Field(default_factory=time.time)  # Momento de criação

    def __post_init__(self):
        if not isinstance(self.vector, list):
            try:
                # Tentar converter se for np.array por exemplo
                self.vector = list(self.vector)
            except TypeError:
                raise ValueError(
                    "QuantumSignaturePacket.vector deve ser uma lista ou conversível para lista."
                )
        if not isinstance(self.metrics, dict):
            raise ValueError("QuantumSignaturePacket.metrics deve ser um dicionário.")

    def to_dict(self) -> Dict[str, Any]:
        """Retorna a representação em dicionário do pacote."""
        try:
            return self.model_dump()
        except AttributeError:
            return self.dict()


@dataclass
class Position:
    """Representa uma posição de trading aberta."""

    symbol: str
    side: str  # "buy" ou "sell"
    quantity: float
    entry_price: float
    entry_time: str
    position_size: float
    fees_paid: float = 0.0
    current_price: float = 0.0
    target_price: Optional[float] = None
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    unrealized_pnl: float = 0.0
    unrealized_pnl_pct: float = 0.0
    duration: float = 0.0
    order_id: Optional[str] = None
    signal_confidence: float = 0.0
    reasons: Optional[List[str]] = None
    metacognition_decision_id: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

    def __post_init__(self) -> None:
        if self.reasons is None:
            self.reasons = []
        if self.metadata is None:
            self.metadata = {}

    def update_price(self, new_price: float) -> None:
        """Atualiza o preço atual e recalcula P&L."""

        self.current_price = new_price
        if self.side == "buy":
            self.unrealized_pnl = (new_price - self.entry_price) * self.quantity
            self.unrealized_pnl_pct = (new_price / self.entry_price - 1) * 100
        else:  # sell
            self.unrealized_pnl = (self.entry_price - new_price) * self.quantity
            self.unrealized_pnl_pct = (self.entry_price / new_price - 1) * 100

    def __getitem__(self, item: str) -> Any:
        return getattr(self, item)

    def __setitem__(self, key: str, value: Any) -> None:
        setattr(self, key, value)

    def get(self, key: str, default: Any = None) -> Any:
        """Return attribute ``key`` or ``default`` when missing."""

        return getattr(self, key, default)
