from __future__ import annotations

import os
from typing import Any, Dict, Optional

try:
    import yaml
except ModuleNotFoundError:
    class YAMLError(Exception):
        pass

    class DummyYAML:
        def safe_load(self, stream):
            return {}
        @property
        def YAMLError(self):
            return YAMLError

    yaml = DummyYAML()

from ..utils.logger import get_logger
from .settings import get_config_file_path

logger = get_logger(__name__)

_DEFAULT_PATH = get_config_file_path("adaptive_evolution.yaml")


def load_ace_config(path: Optional[str] = None) -> Dict[str, Any]:
    """Load AdaptiveConsciousnessEvolution parameters from YAML file.

    Parameters
    ----------
    path:
        Optional path to the YAML file. When ``None`` the function looks for the
        ``QUALIA_ACE_CONFIG`` environment variable falling back to the default
        ``config/adaptive_evolution.yaml`` file.

    Returns
    -------
    Dict[str, Any]
        Mapping of parameter names to values. Returns an empty dict on error.
    """

    config_path = path or os.getenv("QUALIA_ACE_CONFIG", str(_DEFAULT_PATH))
    try:
        with open(config_path, "r", encoding="utf-8") as fh:
            data = yaml.safe_load(fh) or {}
        if not isinstance(data, dict):
            logger.error("Arquivo de configuração ACE inválido: %s", config_path)
            return {}
        # Apply environment variable overrides
        for key in list(data.keys()):
            env_var = f"QUALIA_ACE_{key.upper()}"
            if env_value := os.getenv(env_var):
                try:
                    data[key] = yaml.safe_load(env_value)
                except Exception:  # pragma: no cover - unexpected parsing error
                    data[key] = env_value
        return data
    except FileNotFoundError:
        logger.warning("Arquivo de configuração ACE não encontrado em %s", config_path)
    except Exception as exc:  # pragma: no cover - unexpected errors
        logger.error("Falha ao carregar configuração ACE %s: %s", config_path, exc)
    return {}


__all__ = ["load_ace_config"]
