from __future__ import annotations

"""Registry for QuantumEncoder plugin classes."""

from typing import Dict, Type, Any

from ..core.encoders import QuantumEncoder
from ..utils.logger import get_logger

logger = get_logger(__name__)

_ENCODER_REGISTRY: Dict[str, Type[QuantumEncoder]] = {}


def register_encoder(alias: str, cls: Type[QuantumEncoder]) -> None:
    """Register a QuantumEncoder subclass under ``alias``.

    Parameters
    ----------
    alias:
        Name used to reference the encoder in configuration files.
    cls:
        Encoder class to register. Must subclass :class:`QuantumEncoder`.

    Raises
    ------
    TypeError
        If ``cls`` is not a subclass of :class:`QuantumEncoder`.
    """
    if not issubclass(cls, QuantumEncoder):
        raise TypeError("cls must be subclass of QuantumEncoder")
    if alias in _ENCODER_REGISTRY:
        logger.warning("Encoder '%s' overwritten", alias)
    _ENCODER_REGISTRY[alias] = cls


def unregister_encoder(alias: str) -> None:
    """Remove ``alias`` from the registry if present."""
    _ENCODER_REGISTRY.pop(alias, None)


def get_encoder_class(alias: str) -> Type[QuantumEncoder]:
    """Return the registered class for ``alias``."""
    return _ENCODER_REGISTRY[alias]


def create_encoder(alias: str, **kwargs: Any) -> QuantumEncoder:
    """Instantiate the encoder registered under ``alias`` with ``kwargs``."""
    cls = get_encoder_class(alias)
    return cls(**kwargs)


def get_registered_encoders() -> Dict[str, Type[QuantumEncoder]]:
    """Return a copy of the internal encoder mapping."""
    return dict(_ENCODER_REGISTRY)


__all__ = [
    "register_encoder",
    "unregister_encoder",
    "get_encoder_class",
    "create_encoder",
    "get_registered_encoders",
]
