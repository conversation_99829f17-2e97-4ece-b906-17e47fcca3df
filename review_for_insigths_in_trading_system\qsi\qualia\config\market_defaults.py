"""Load market module defaults from YAML configuration.

The path ``config/market.yaml`` is used unless ``QUALIA_MARKET_DEFAULTS``
points to a custom file.
"""

from __future__ import annotations

from typing import Any, Dict

from ..utils.logger import get_logger
from .yaml_loader import load_yaml_config
from .settings import get_config_file_path

logger = get_logger(__name__)

_DEFAULT_PATH = get_config_file_path("market.yaml")


def load_market_defaults() -> Dict[str, Any]:
    """Load market parameters from YAML configuration.

    The ``QUALIA_MARKET_DEFAULTS`` environment variable can override the bundled
    ``config/market.yaml`` file.
    """
    return load_yaml_config(
        "QUALIA_MARKET_DEFAULTS",
        _DEFAULT_PATH,
        logger=logger,
    )


__all__ = ["load_market_defaults"]
