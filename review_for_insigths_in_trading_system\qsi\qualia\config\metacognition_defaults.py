"""Load metacognition parameters required by QUALIA.

By default ``config/metacognition_defaults.yaml`` is used, but you can
set ``QUALIA_METACOGNITION_DEFAULTS`` to a custom configuration file.
"""

from __future__ import annotations

from typing import Any, Dict

from ..utils.logger import get_logger
from .yaml_loader import load_yaml_config
from .settings import get_config_file_path

logger = get_logger(__name__)

_DEFAULT_PATH = get_config_file_path("metacognition_defaults.yaml")


def load_metacognition_defaults() -> Dict[str, Any]:
    """Load metacognition default configuration from YAML.

    The ``QUALIA_METACOGNITION_DEFAULTS`` environment variable can override the
    default path.

    Returns
    -------
    Dict[str, Any]
        Mapping with the configuration values. Returns an empty dict on error.
    """

    return load_yaml_config(
        "QUALIA_METACOGNITION_DEFAULTS",
        _DEFAULT_PATH,
        logger=logger,
    )


__all__ = ["load_metacognition_defaults"]
