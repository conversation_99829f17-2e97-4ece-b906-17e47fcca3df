"""Pydantic models para validação de configurações QUALIA (SIM-06)."""

from __future__ import annotations

from typing import Optional

from pydantic import BaseModel, Field, PositiveFloat, validator


class SymbolicModulationConfig(BaseModel):
    """Configurações para OperatorSIM & Tokenizer."""

    enabled: bool = Field(False, description="Ativa a modulação simbólica via OperatorSIM")
    zscore_threshold: PositiveFloat = Field(2.5, description="Desvio-padrão para detectar hotspots")

    @validator("zscore_threshold")
    def _reasonable_threshold(cls, v: float) -> float:  # noqa: D401 simple
        if not 0.5 <= v <= 10.0:
            raise ValueError("zscore_threshold deve estar entre 0.5 e 10")
        return v


class QUALIAConsciousnessConfig(BaseModel):
    """Modelo resumido apenas para chaves usadas por SIM-06."""

    symbolic_modulation: Optional[SymbolicModulationConfig] = Field(
        default_factory=SymbolicModulationConfig,
        description="Bloco de configurações para modulação simbólica",
    )
