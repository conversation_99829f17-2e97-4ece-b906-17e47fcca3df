"""Load defaults for :class:`~qualia.risk.QuantumRiskAnalyzer` from YAML.

The ``QUALIA_QUANTUM_RISK_DEFAULTS`` environment variable can be used to
override the default ``config/quantum_risk_defaults.yaml`` file.
"""

from __future__ import annotations

from typing import Any, Dict

from ..utils.logger import get_logger
from .yaml_loader import load_yaml_config
from .settings import get_config_file_path

logger = get_logger(__name__)

_DEFAULT_PATH = get_config_file_path("quantum_risk_defaults.yaml")


def load_quantum_risk_defaults() -> Dict[str, Any]:
    """Load default parameters for :class:`QuantumRiskAnalyzer` from YAML.

    The ``QUALIA_QUANTUM_RISK_DEFAULTS`` environment variable can override the
    bundled path.

    Returns
    -------
    Dict[str, Any]
        Mapping of configuration keys to values. Returns an empty mapping on
        error.
    """

    return load_yaml_config(
        "QUALIA_QUANTUM_RISK_DEFAULTS",
        _DEFAULT_PATH,
        logger=logger,
    )


__all__ = ["load_quantum_risk_defaults"]
