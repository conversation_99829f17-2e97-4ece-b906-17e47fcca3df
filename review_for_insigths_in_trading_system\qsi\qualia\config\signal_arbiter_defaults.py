from __future__ import annotations

from typing import Any, Dict

from ..utils.logger import get_logger
from .yaml_loader import load_yaml_config
from .settings import get_config_file_path

logger = get_logger(__name__)

_DEFAULT_PATH = get_config_file_path("signal_arbiter_defaults.yaml")


def load_signal_arbiter_defaults() -> Dict[str, Any]:
    """Load default values for :class:`~src.qualia.market.signal_arbiter.SignalArbiter`.

    The ``QUALIA_SIGNAL_ARBITER_DEFAULTS`` environment variable can override the
    bundled configuration file.

    Returns
    -------
    Dict[str, Any]
        Mapping of configuration keys to values. Returns an empty mapping on error.
    """

    return load_yaml_config(
        "QUALIA_SIGNAL_ARBITER_DEFAULTS",
        _DEFAULT_PATH,
        logger=logger,
    )


__all__ = ["load_signal_arbiter_defaults"]
