"""Enumeration of supported quantum vector serialization formats.

The list is used by ``qualia.config.settings`` where environment
variables ``QUALIA_ALLOWED_VECTOR_TYPES`` or ``QUALIA_VECTOR_TYPES_FILE``
restrict which entries are permitted.
"""

from enum import Enum


class VectorType(str, Enum):
    """Enumeration of supported quantum signature vector formats."""

    AMPLITUDE_VECTOR_INTERLEAVED_FLOAT = "amplitude_vector_interleaved_float"
    PROBABILITY_DISTRIBUTION = "probability_distribution"
    FIRST_N_PROBABILITIES = "first_n_probabilities"
    SELECTED_METRICS_VECTOR = "selected_metrics_vector"
    DENSITY_MATRIX_VECTOR = "density_matrix_vector"
    SECOND_ORDER_MOMENTS_VECTOR = "second_order_moments_vector"
    PHASE_VECTOR = "phase_vector"
    CUMULATIVE_PROBABILITIES = "cumulative_probabilities"
