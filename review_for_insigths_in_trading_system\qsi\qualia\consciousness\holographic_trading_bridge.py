"""
QUALIA Holographic Trading Bridge

Ponte inteligente entre sinais holográficos e sistema de execução de trading.
Integra validação de risco, anti-spam e coordenação de execução.
"""

from __future__ import annotations

import asyncio
import time
import logging
from typing import Dict, List, Any, Optional, Deque
from dataclasses import dataclass
from collections import deque

from .holographic_universe import TradingSignal, HolographicPattern
from ..trader.execution_engine import open_position, close_position
from ..risk_management.advanced_risk_manager import AdvancedRiskManager
from ..exchanges.kucoin_client import KuCoinClient
from ..utils.logger import get_logger

logger = get_logger(__name__)

@dataclass
class SignalExecutionResult:
    """Resultado da execução de um sinal holográfico."""
    signal: TradingSignal
    executed: bool
    order_id: Optional[str]
    execution_price: Optional[float]
    position_size: float
    reason: str
    timestamp: float
    risk_params: Dict[str, Any]

class HolographicTradingBridge:
    """
    Ponte entre sinais holográficos e sistema de execução.
    
    Responsabilidades:
    - Validação de sinais com risk management
    - Anti-spam e rate limiting
    - Coordenação de execução
    - Tracking de posições ativas
    - Performance monitoring
    """
    
    def __init__(
        self,
        risk_manager: AdvancedRiskManager,
        kucoin_client: KuCoinClient,
        config: Dict[str, Any]
    ):
        self.risk_manager = risk_manager
        self.kucoin_client = kucoin_client
        self.config = config
        
        # Signal processing
        self.signal_queue: asyncio.Queue = asyncio.Queue()
        self.signal_history: Deque[TradingSignal] = deque(maxlen=1000)
        
        # Position tracking
        self.active_positions: Dict[str, Dict[str, Any]] = {}
        self.execution_results: List[SignalExecutionResult] = []
        
        # Anti-spam controls
        self.symbol_last_signal: Dict[str, float] = {}
        self.symbol_signal_count: Dict[str, int] = {}
        
        # Configuration
        self.min_confidence = config.get('min_confidence', 0.7)
        self.allowed_timeframes = config.get('allowed_timeframes', ['5m', '15m', '1h'])
        self.trading_symbols = config.get('trading_symbols', [])
        self.max_positions = config.get('max_positions', 5)
        self.signal_cooldown = config.get('signal_cooldown', 300)  # 5 minutes
        self.max_signals_per_symbol_per_hour = config.get('max_signals_per_hour', 6)
        
        logger.info(
            f"🌉 HolographicTradingBridge inicializada: "
            f"min_confidence={self.min_confidence}, "
            f"symbols={len(self.trading_symbols)}, "
            f"max_positions={self.max_positions}"
        )
    
    async def process_holographic_signal(self, signal: TradingSignal) -> Optional[SignalExecutionResult]:
        """
        Processa sinal holográfico com validação completa.
        
        Args:
            signal: Sinal gerado pelo universo holográfico
            
        Returns:
            Resultado da execução ou None se rejeitado
        """
        
        try:
            # 1. Validação básica
            validation_result = self._validate_signal(signal)
            if not validation_result['valid']:
                logger.debug(
                    f"Sinal {signal.symbol} rejeitado: {validation_result['reason']}"
                )
                return SignalExecutionResult(
                    signal=signal,
                    executed=False,
                    order_id=None,
                    execution_price=None,
                    position_size=0.0,
                    reason=validation_result['reason'],
                    timestamp=time.time(),
                    risk_params={}
                )
            
            # 2. Enriquecimento com dados de mercado
            enhanced_signal = await self._enhance_signal_with_market_data(signal)
            
            # 3. Validação de risco
            risk_result = self.risk_manager.calculate_position_size(
                symbol=enhanced_signal.symbol,
                current_price=enhanced_signal.metadata.get('current_price'),
                stop_loss_price=enhanced_signal.metadata.get('stop_loss'),
                confidence=enhanced_signal.confidence,
                volatility=enhanced_signal.metadata.get('volatility')
            )
            
            if not risk_result['position_allowed']:
                logger.info(
                    f"Sinal {enhanced_signal.symbol} bloqueado pelo risk manager: "
                    f"{risk_result['reason']}"
                )
                return SignalExecutionResult(
                    signal=enhanced_signal,
                    executed=False,
                    order_id=None,
                    execution_price=None,
                    position_size=0.0,
                    reason=f"risk_manager_{risk_result['reason']}",
                    timestamp=time.time(),
                    risk_params=risk_result
                )
            
            # 4. Verificação de capacidade de posições
            if len(self.active_positions) >= self.max_positions:
                logger.info(
                    f"Máximo de posições atingido ({self.max_positions}). "
                    f"Sinal {enhanced_signal.symbol} rejeitado."
                )
                return SignalExecutionResult(
                    signal=enhanced_signal,
                    executed=False,
                    order_id=None,
                    execution_price=None,
                    position_size=0.0,
                    reason="max_positions_reached",
                    timestamp=time.time(),
                    risk_params=risk_result
                )
            
            # 5. Execução
            execution_result = await self._execute_signal(enhanced_signal, risk_result)
            
            # 6. Tracking
            self.signal_history.append(enhanced_signal)
            self.execution_results.append(execution_result)
            self._update_anti_spam_counters(enhanced_signal)
            
            return execution_result
            
        except Exception as e:
            logger.error(f"Erro ao processar sinal holográfico: {e}", exc_info=True)
            return SignalExecutionResult(
                signal=signal,
                executed=False,
                order_id=None,
                execution_price=None,
                position_size=0.0,
                reason=f"execution_error_{str(e)[:50]}",
                timestamp=time.time(),
                risk_params={}
            )
    
    def _validate_signal(self, signal: TradingSignal) -> Dict[str, Any]:
        """Validação multi-camada de sinais holográficos."""
        
        # Confidence threshold
        if signal.confidence < self.min_confidence:
            return {
                'valid': False,
                'reason': f'low_confidence_{signal.confidence:.2f}_min_{self.min_confidence}'
            }
        
        # Timeframe alignment
        if signal.timeframe not in self.allowed_timeframes:
            return {
                'valid': False,
                'reason': f'invalid_timeframe_{signal.timeframe}'
            }
        
        # Symbol whitelist
        if signal.symbol not in self.trading_symbols:
            return {
                'valid': False,
                'reason': f'symbol_not_allowed_{signal.symbol}'
            }
        
        # Anti-spam: cooldown period
        last_signal_time = self.symbol_last_signal.get(signal.symbol, 0)
        if time.time() - last_signal_time < self.signal_cooldown:
            return {
                'valid': False,
                'reason': f'signal_cooldown_{int(time.time() - last_signal_time)}s'
            }
        
        # Anti-spam: frequency limit
        current_hour = int(time.time() // 3600)
        signal_count_key = f"{signal.symbol}_{current_hour}"
        current_count = self.symbol_signal_count.get(signal_count_key, 0)
        
        if current_count >= self.max_signals_per_symbol_per_hour:
            return {
                'valid': False,
                'reason': f'hourly_limit_exceeded_{current_count}'
            }
        
        # Action validation
        if signal.action not in ['BUY', 'SELL', 'HOLD']:
            return {
                'valid': False,
                'reason': f'invalid_action_{signal.action}'
            }
        
        # Skip HOLD signals for execution
        if signal.action == 'HOLD':
            return {
                'valid': False,
                'reason': 'hold_signal_skipped'
            }
        
        return {'valid': True, 'reason': 'all_validations_passed'}
    
    async def _enhance_signal_with_market_data(self, signal: TradingSignal) -> TradingSignal:
        """Enriquece sinal com dados de mercado em tempo real."""
        
        try:
            # Buscar preço atual
            current_price = await self._get_current_price(signal.symbol)
            
            # Calcular volatility (simplificado)
            volatility = await self._calculate_volatility(signal.symbol)
            
            # Calcular stop loss baseado em volatility e action
            stop_loss = self._calculate_stop_loss(current_price, signal.action, volatility)
            
            # Calcular take profit (risk:reward 1:2)
            take_profit = self._calculate_take_profit(current_price, signal.action, stop_loss)
            
            # Atualizar metadata
            signal.metadata.update({
                'current_price': current_price,
                'volatility': volatility,
                'stop_loss': stop_loss,
                'take_profit': take_profit,
                'timestamp_execution': time.time(),
                'enhanced': True
            })
            
            return signal
            
        except Exception as e:
            logger.warning(f"Falha ao enriquecer sinal {signal.symbol}: {e}")
            # Retorna sinal original se falhar
            return signal
    
    async def _get_current_price(self, symbol: str) -> float:
        """Busca preço atual do símbolo."""
        
        try:
            # Converte formato QUALIA para KuCoin
            kucoin_symbol = symbol.replace('/', '-')
            
            # Busca ticker via KuCoin client
            ticker = await self.kucoin_client.fetch_ticker(kucoin_symbol)
            
            return float(ticker.get('last', 0))
            
        except Exception as e:
            logger.warning(f"Falha ao buscar preço para {symbol}: {e}")
            # Fallback para preço do metadata se disponível
            return signal.metadata.get('price', 0.0)
    
    async def _calculate_volatility(self, symbol: str) -> float:
        """Calcula volatility simplificada baseada em variação de preço."""
        
        try:
            # Implementação simplificada - em produção usar dados históricos
            kucoin_symbol = symbol.replace('/', '-')
            ticker = await self.kucoin_client.fetch_ticker(kucoin_symbol)
            
            high = float(ticker.get('high', 0))
            low = float(ticker.get('low', 0))
            last = float(ticker.get('last', 0))
            
            if last > 0:
                daily_range = (high - low) / last
                return daily_range
            
            return 0.02  # 2% default
            
        except Exception as e:
            logger.warning(f"Falha ao calcular volatility para {symbol}: {e}")
            return 0.02  # 2% default
    
    def _calculate_stop_loss(self, current_price: float, action: str, volatility: float) -> float:
        """Calcula stop loss baseado em volatility."""
        
        # Stop loss a 2x volatility
        stop_distance = current_price * volatility * 2
        
        if action == 'BUY':
            return current_price - stop_distance
        else:  # SELL
            return current_price + stop_distance
    
    def _calculate_take_profit(self, current_price: float, action: str, stop_loss: float) -> float:
        """Calcula take profit com risk:reward 1:2."""
        
        risk = abs(current_price - stop_loss)
        reward = risk * 2  # 1:2 risk:reward
        
        if action == 'BUY':
            return current_price + reward
        else:  # SELL
            return current_price - reward
    
    async def _execute_signal(
        self,
        signal: TradingSignal,
        risk_result: Dict[str, Any]
    ) -> SignalExecutionResult:
        """Executa sinal via execution engine existente."""
        
        try:
            # Preparação dos parâmetros
            trade_params = {
                'symbol': signal.symbol,
                'side': signal.action.lower(),
                'entry_price': signal.metadata['current_price'],
                'size': risk_result['quantity'],
                'stop_loss': signal.metadata.get('stop_loss'),
                'take_profit': signal.metadata.get('take_profit'),
                'limit_price': None  # Market order
            }
            
            # Contexto para logging
            decision_context = {
                'signal_source': 'holographic_universe',
                'pattern_type': signal.metadata.get('pattern_type', 'unknown'),
                'confidence': signal.confidence,
                'strength': signal.strength,
                'timeframe': signal.timeframe,
                'rationale': signal.rationale,
                'holographic_enhanced': signal.metadata.get('enhanced', False)
            }
            
            # Execução via engine existente
            position = await open_position(
                trader=self.kucoin_client,
                symbol=trade_params['symbol'],
                side=trade_params['side'],
                entry_price=trade_params['entry_price'],
                size=trade_params['size'],
                stop_loss=trade_params['stop_loss'],
                take_profit=trade_params['take_profit'],
                q_signature_packet_arg=None,
                market_snapshot=signal.metadata,
                decision_context=decision_context,
                limit_price=trade_params['limit_price']
            )
            
            if position:
                # Tracking da posição
                self.active_positions[position.order_id] = {
                    'signal': signal,
                    'position': position,
                    'opened_at': time.time(),
                    'risk_params': risk_result,
                    'trade_params': trade_params
                }
                
                logger.info(
                    f"🎯 HOLOGRAPHIC TRADE EXECUTED: {signal.symbol} {signal.action} "
                    f"size={risk_result['quantity']:.6f} confidence={signal.confidence:.2f} "
                    f"pattern={signal.metadata.get('pattern_type', 'unknown')} "
                    f"order_id={position.order_id}"
                )
                
                return SignalExecutionResult(
                    signal=signal,
                    executed=True,
                    order_id=position.order_id,
                    execution_price=position.entry_price,
                    position_size=risk_result['quantity'],
                    reason="executed_successfully",
                    timestamp=time.time(),
                    risk_params=risk_result
                )
            
            else:
                logger.warning(f"❌ Falha na execução - position None: {signal.symbol}")
                return SignalExecutionResult(
                    signal=signal,
                    executed=False,
                    order_id=None,
                    execution_price=None,
                    position_size=0.0,
                    reason="execution_failed_position_none",
                    timestamp=time.time(),
                    risk_params=risk_result
                )
                
        except Exception as e:
            logger.error(f"Erro na execução do sinal: {e}", exc_info=True)
            return SignalExecutionResult(
                signal=signal,
                executed=False,
                order_id=None,
                execution_price=None,
                position_size=0.0,
                reason=f"execution_exception_{str(e)[:50]}",
                timestamp=time.time(),
                risk_params=risk_result
            )
    
    def _update_anti_spam_counters(self, signal: TradingSignal) -> None:
        """Atualiza contadores anti-spam."""
        
        # Update last signal time
        self.symbol_last_signal[signal.symbol] = time.time()
        
        # Update hourly counter
        current_hour = int(time.time() // 3600)
        signal_count_key = f"{signal.symbol}_{current_hour}"
        self.symbol_signal_count[signal_count_key] = (
            self.symbol_signal_count.get(signal_count_key, 0) + 1
        )
        
        # Cleanup old hourly counters (keep last 24 hours)
        cutoff_hour = current_hour - 24
        old_keys = [
            key for key in self.symbol_signal_count.keys()
            if int(key.split('_')[-1]) < cutoff_hour
        ]
        for key in old_keys:
            del self.symbol_signal_count[key]
    
    async def monitor_active_positions(self) -> None:
        """Monitora posições ativas e aplica regras de saída."""
        
        if not self.active_positions:
            return
        
        current_time = time.time()
        positions_to_close = []
        
        for order_id, position_data in self.active_positions.items():
            try:
                signal = position_data['signal']
                position = position_data['position']
                opened_at = position_data['opened_at']
                
                # Timeout check (close after max time)
                max_position_time = self.config.get('position_timeout', 3600)  # 1 hour
                if current_time - opened_at > max_position_time:
                    positions_to_close.append((order_id, 'timeout'))
                    continue
                
                # Check if stop loss or take profit hit
                current_price = await self._get_current_price(signal.symbol)
                stop_loss = signal.metadata.get('stop_loss')
                take_profit = signal.metadata.get('take_profit')
                
                should_close = False
                close_reason = ""
                
                if signal.action == 'BUY':
                    if stop_loss and current_price <= stop_loss:
                        should_close = True
                        close_reason = 'stop_loss_hit'
                    elif take_profit and current_price >= take_profit:
                        should_close = True
                        close_reason = 'take_profit_hit'
                else:  # SELL
                    if stop_loss and current_price >= stop_loss:
                        should_close = True
                        close_reason = 'stop_loss_hit'
                    elif take_profit and current_price <= take_profit:
                        should_close = True
                        close_reason = 'take_profit_hit'
                
                if should_close:
                    positions_to_close.append((order_id, close_reason))
                
            except Exception as e:
                logger.error(f"Erro ao monitorar posição {order_id}: {e}")
        
        # Close positions
        for order_id, reason in positions_to_close:
            await self._close_position(order_id, reason)
    
    async def _close_position(self, order_id: str, reason: str) -> None:
        """Fecha posição ativa."""
        
        if order_id not in self.active_positions:
            return
        
        try:
            position_data = self.active_positions[order_id]
            signal = position_data['signal']
            
            # Get current price for close
            current_price = await self._get_current_price(signal.symbol)
            
            # Close via execution engine
            result = await close_position(
                trader=self.kucoin_client,
                symbol=signal.symbol,
                order_id=order_id,
                trigger_price=current_price,
                reason=reason
            )
            
            if result:
                logger.info(
                    f"🔄 HOLOGRAPHIC POSITION CLOSED: {signal.symbol} {order_id} "
                    f"reason={reason} pnl={result.get('pnl', 'unknown')}"
                )
                
                # Remove from active positions
                del self.active_positions[order_id]
                
            else:
                logger.warning(f"❌ Falha ao fechar posição {order_id}")
                
        except Exception as e:
            logger.error(f"Erro ao fechar posição {order_id}: {e}", exc_info=True)
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Retorna resumo de performance."""
        
        total_signals = len(self.execution_results)
        executed_signals = sum(1 for r in self.execution_results if r.executed)
        
        if total_signals == 0:
            return {
                'total_signals': 0,
                'executed_signals': 0,
                'execution_rate': 0.0,
                'active_positions': 0,
                'avg_confidence': 0.0
            }
        
        execution_rate = executed_signals / total_signals
        avg_confidence = sum(r.signal.confidence for r in self.execution_results) / total_signals
        
        return {
            'total_signals': total_signals,
            'executed_signals': executed_signals,
            'execution_rate': execution_rate,
            'active_positions': len(self.active_positions),
            'avg_confidence': avg_confidence,
            'symbols_traded': len(set(r.signal.symbol for r in self.execution_results)),
            'last_signal_time': max(r.timestamp for r in self.execution_results) if self.execution_results else 0
        }
    
    async def shutdown(self) -> None:
        """Shutdown graceful do bridge."""
        
        logger.info("🔄 Fechando posições ativas antes do shutdown...")
        
        # Close all active positions
        for order_id in list(self.active_positions.keys()):
            await self._close_position(order_id, 'system_shutdown')
        
        logger.info("🌉 HolographicTradingBridge shutdown completo") 