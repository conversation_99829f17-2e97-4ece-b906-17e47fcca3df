"""
QUALIA Real Data Collectors - Coletores de dados reais para o universo holográfico.

Este módulo implementa coletores que obtêm dados reais de:
- Exchanges (preços, volume, orderbook)
- News feeds (RSS, APIs de notícias)
- Social sentiment (Reddit, Twitter, Fear & Greed)
- Technical indicators (em tempo real)
"""

from __future__ import annotations

import asyncio
import aiohttp
import random
import time
import hashlib
import os
from typing import Dict, List, Any, Optional, AsyncGenerator
from dataclasses import dataclass
from datetime import datetime, timedelta
import json
import feedparser
import numpy as np

from pathlib import Path

from ..config.settings import settings
from ..utils.persistence import load_json_safe, save_json_safe

from .holographic_universe import HolographicEvent
from ..core.rss_encoder import RSSTextSentimentEncoder
from .amplification_calibrator import AmplificationCalibrator
from ..utils.logger import get_logger
from ..memory.event_bus import (
    SimpleEventBus,
    SOCIAL_SENTIMENT_STREAM,
    ONCHAIN_SIGNAL_STREAM,
)
from ..event_bus import MarketDataUpdated
from ..config.data_sources import load_data_sources_defaults

logger = get_logger(__name__)


@dataclass
class MarketDataPoint:
    """Ponto de dados de mercado."""

    symbol: str
    price: float
    volume: float
    timestamp: float
    change_24h: float
    source: str

    def to_dict(self) -> Dict[str, Any]:
        """Converte o objeto para um dicionário."""
        return {
            "symbol": self.symbol,
            "price": self.price,
            "volume": self.volume,
            "timestamp": self.timestamp,
            "change_24h": self.change_24h,
            "source": self.source,
        }


@dataclass
class NewsEvent:
    """Evento de notícia."""

    title: str
    content: str
    timestamp: float
    sentiment_score: float
    quantum_sentiment_state: Optional[np.ndarray] = None  # Estado quântico do sentiment
    source: str = ""
    url: str = ""
    guid: str = ""


@dataclass
class SocialSentiment:
    """Dados de sentiment social."""

    platform: str
    symbol: str
    sentiment_score: float
    volume_mentions: int
    timestamp: float
    keywords: List[str]


@dataclass
class OnChainSignal:
    """Sinal derivado de métricas on-chain."""

    metric: str
    value: float
    timestamp: float
    source: str


class RealDataCollector:
    """
    Coletor principal de dados reais para o universo holográfico.

    Integra múltiplas fontes de dados em tempo real para alimentar
    o sistema holográfico com informações do mundo real.
    """

    def __init__(
        self,
        data_sources_config: Optional[Dict[str, Any]] = None,
        event_bus: Optional[SimpleEventBus] = None,
    ) -> None:
        """Inicializa o coletor de dados reais."""
        self.session = None
        self.is_running = False
        self.event_bus = event_bus
        self.collection_interval = 60.0
        self.state_file = Path(settings.collector_state_file)
        state = load_json_safe(self.state_file.as_posix(), {})
        if isinstance(state, dict):
            interval = state.get("collection_interval")
            if isinstance(interval, (int, float)):
                self.collection_interval = max(1.0, float(interval))
        os.makedirs(self.state_file.parent, exist_ok=True)
        self._persist_state()
        self.data_sources = data_sources_config or {}
        social_cfg = self.data_sources.get("social", {})
        self.twitter_enabled = social_cfg.get("twitter", {}).get("enabled", False)
        self.reddit_enabled = social_cfg.get("reddit", {}).get("enabled", False)
        self.santiment_enabled = social_cfg.get("santiment", {}).get("enabled", False)
        self.onchain_enabled = self.data_sources.get("onchain", {}).get(
            "enabled", False
        )
        onchain_cfg = self.data_sources.get("onchain", {})
        self.cryptoquant_enabled = onchain_cfg.get("cryptoquant", {}).get(
            "enabled", False
        )
        self.glassnode_enabled = onchain_cfg.get("glassnode", {}).get("enabled", False)

        # URLs das APIs
        self.binance_base = "https://api.binance.com/api/v3"
        self.fear_greed_api = "https://api.alternative.me/fng/?limit=1"

        # Inicializa o encoder de sentiment quântico
        self.rss_sentiment_encoder = RSSTextSentimentEncoder()

        # Inicializa o calibrador de amplificação
        self.amplification_calibrator = AmplificationCalibrator(
            initial_price_amp=5.0, initial_news_amp=4.0, learning_rate=0.1
        )

        # Controle de avisos de feed RSS
        self.feed_warning_timestamps: Dict[str, float] = {}

        # URLs de feeds RSS para notícias de crypto
        self.news_feeds = [
            "https://cointelegraph.com/rss",
            "https://decrypt.co/feed",
            "https://cryptonews.com/news/feed/",
            "https://www.coindesk.com/arc/outboundfeeds/rss/",
        ]

        # Símbolos para coleta
        self.symbols = ["BTCUSDT", "ETHUSDT", "ADAUSDT", "SOLUSDT", "MATICUSDT"]

        logger.info(
            "RealDataCollector inicializado com encoder quântico de sentiment e calibrador adaptativo"
        )

    async def __aenter__(self):
        """Context manager entry."""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=10),
            headers={"User-Agent": "QUALIA-Holographic-System/1.0"},
        )
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        if self.session:
            await self.session.close()

    async def collect_market_data(self) -> List[MarketDataPoint]:
        """
        Coleta dados de mercado em tempo real.

        Returns:
            Lista de pontos de dados de mercado
        """
        if not self.session:
            return []

        market_data = []

        try:
            # Coleta dados da Binance
            binance_data = await self._fetch_binance_data()
            market_data.extend(binance_data)

            logger.debug(f"Coletados {len(market_data)} pontos de dados de mercado")

        except Exception as e:
            logger.error(f"Erro coletando dados de mercado: {e}")

        return market_data

    async def _fetch_binance_data(self) -> List[MarketDataPoint]:
        """Coleta dados da API da Binance."""
        try:
            # Ticker 24h para todos os símbolos
            url = f"{self.binance_base}/ticker/24hr"
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()

                    market_data = []
                    for ticker in data:
                        if ticker["symbol"] in self.symbols:
                            market_data.append(
                                MarketDataPoint(
                                    symbol=ticker["symbol"],
                                    price=float(ticker["lastPrice"]),
                                    volume=float(ticker["volume"]),
                                    timestamp=time.time(),
                                    change_24h=float(ticker["priceChangePercent"]),
                                    source="binance",
                                )
                            )

                    return market_data
                else:
                    logger.warning(f"Binance API error: {response.status}")

        except Exception as e:
            logger.error(f"Erro na API Binance: {e}")

        return []

    async def _fetch_news_feed(
        self, feed_url: str, attempts: int = 3, initial_backoff: float = 1.0
    ) -> Optional[str]:
        """Busca RSS com retentativa exponencial e jitter aleatório."""

        backoff = initial_backoff
        for attempt in range(1, attempts + 1):
            try:
                async with self.session.get(feed_url) as response:
                    if response.status == 200:
                        return await response.text()
                    raise RuntimeError(f"HTTP {response.status}")
            except Exception as exc:  # noqa: PERF203
                reason = str(exc)
                if attempt == attempts:
                    self._log_feed_failure(feed_url, reason)
                    return None

                jitter = random.uniform(0.8, 1.2)
                await asyncio.sleep(backoff * jitter)
                backoff *= 2

        return None

    def _log_feed_failure(self, feed_url: str, reason: str) -> None:
        """Registra aviso limitado para feed com falha."""

        now = time.time()
        last_time = self.feed_warning_timestamps.get(feed_url, 0.0)
        if now - last_time > 3600:  # Limita avisos por feed a cada hora
            logger.warning(f"Erro coletando feed {feed_url}: {reason}")
            self.feed_warning_timestamps[feed_url] = now
        else:
            logger.debug(f"Erro repetido para feed {feed_url}: {reason}")

    async def collect_news_events(self) -> List[NewsEvent]:
        """
        Coleta eventos de notícias de feeds RSS.

        Returns:
            Lista de eventos de notícias
        """
        # YAA- Log detalhado para diagnosticar problema RSS
        logger.info(f"🔍 Iniciando coleta RSS de {len(self.news_feeds)} feeds")

        news_events = []

        for feed_url in self.news_feeds:
            logger.info(f"🔍 Buscando feed: {feed_url}")

            content = await self._fetch_news_feed(feed_url)
            if not content:
                logger.warning(f"🔍 Falha ao buscar conteúdo de {feed_url}")
                continue

            logger.info(
                f"🔍 Conteúdo obtido de {feed_url}, analisando com feedparser..."
            )

            try:
                feed = feedparser.parse(content)
                logger.info(
                    f"🔍 Feed parseado - {len(feed.entries)} entradas encontradas"
                )

                for i, entry in enumerate(feed.entries[:3]):
                    text_content = entry.title + " " + entry.get("summary", "")
                    sentiment_snap = {"text": text_content}

                    logger.debug(f"🔍 Processando entrada {i+1}: {entry.title[:50]}...")

                    sentiment_score = self.rss_sentiment_encoder._get_sentiment(
                        sentiment_snap
                    )
                    quantum_state = self.rss_sentiment_encoder._encode_single(
                        sentiment_snap
                    )

                    unique_str = f"{entry.link}|{entry.title}"
                    guid = hashlib.sha1(unique_str.encode()).hexdigest()
                    news_events.append(
                        NewsEvent(
                            title=entry.title,
                            content=entry.get("summary", "")[:300],
                            timestamp=time.time(),
                            sentiment_score=sentiment_score,
                            quantum_sentiment_state=quantum_state,
                            source=getattr(feed, "title", feed_url),
                            url=entry.link,
                            guid=guid,
                        )
                    )

                    logger.info(
                        f"✅ RSS Quantum Sentiment: '{entry.title[:40]}...' "
                        f"score={sentiment_score:.3f} "
                        f"quantum_state=[{quantum_state[0]:.3f}, {quantum_state[1]:.3f}]"
                    )

                await asyncio.sleep(0.5)

            except Exception as e:
                logger.error(f"❌ Erro processando feed {feed_url}: {e}", exc_info=True)

        logger.info(f"✅ Coletados {len(news_events)} eventos de notícias RSS")
        return news_events

    async def collect_fear_greed_index(self) -> Optional[float]:
        """
        Coleta o Fear & Greed Index.

        Returns:
            Valor do índice (0-100) ou None se erro
        """
        try:
            async with self.session.get(self.fear_greed_api) as response:
                if response.status == 200:
                    data = await response.json()
                    if data["data"]:
                        value = float(data["data"][0]["value"])
                        logger.debug(f"Fear & Greed Index: {value}")
                        return value

        except Exception as e:
            logger.warning(f"Erro coletando Fear & Greed Index: {e}")

        return None

    # ------------------------- Social Sentiment -------------------------
    async def _fetch_twitter_sentiment(self, keyword: str) -> List[SocialSentiment]:
        if not self.session or not self.twitter_enabled:
            return []
        url = (
            "https://api.twitter.com/2/tweets/search/recent?query="
            f"{keyword}&max_results=10"
        )
        headers = {}
        try:
            async with self.session.get(url, headers=headers) as resp:
                if resp.status == 200:
                    data = await resp.json()
                    sentiments = []
                    for entry in data.get("data", []):
                        text = entry.get("text", "")
                        score = self.rss_sentiment_encoder._get_sentiment(
                            {"text": text}
                        )
                        sentiments.append(
                            SocialSentiment(
                                platform="twitter",
                                symbol=keyword,
                                sentiment_score=score,
                                volume_mentions=1,
                                timestamp=time.time(),
                                keywords=[keyword],
                            )
                        )
                    return sentiments
        except Exception as exc:  # pragma: no cover - network errors not in tests
            logger.debug("Twitter sentiment error: %s", exc)
        return []

    async def _fetch_reddit_sentiment(self, keyword: str) -> List[SocialSentiment]:
        if not self.session or not self.reddit_enabled:
            return []
        url = (
            "https://www.reddit.com/search.json?q="
            f"{keyword}&restrict_sr=on&limit=10&sort=new"
        )
        headers = {"User-Agent": "QUALIA-Reddit/1.0"}
        try:
            async with self.session.get(url, headers=headers) as resp:
                if resp.status == 200:
                    data = await resp.json()
                    sentiments = []
                    for child in data.get("data", {}).get("children", []):
                        text = child.get("data", {}).get("title", "")
                        score = self.rss_sentiment_encoder._get_sentiment(
                            {"text": text}
                        )
                        sentiments.append(
                            SocialSentiment(
                                platform="reddit",
                                symbol=keyword,
                                sentiment_score=score,
                                volume_mentions=1,
                                timestamp=time.time(),
                                keywords=[keyword],
                            )
                        )
                    return sentiments
        except Exception as exc:  # pragma: no cover - network errors not in tests
            logger.debug("Reddit sentiment error: %s", exc)
        return []

    async def _fetch_santiment_social(self, keyword: str) -> List[SocialSentiment]:
        """Coleta sentimento social via Santiment."""
        if not self.session or not self.santiment_enabled:
            return []
        cfg = self.data_sources.get("social", {}).get("santiment", {})
        url = cfg.get("api_endpoint", "")
        headers = {"Authorization": f"Bearer {cfg.get('api_key', '')}"}
        try:
            async with self.session.get(
                f"{url}?keyword={keyword}", headers=headers
            ) as resp:
                if resp.status == 200:
                    data = await resp.json()
                    sentiments = []
                    for entry in data.get("data", []):
                        sentiments.append(
                            SocialSentiment(
                                platform="santiment",
                                symbol=keyword,
                                sentiment_score=float(entry.get("score", 0.0)),
                                volume_mentions=int(entry.get("mentions", 0)),
                                timestamp=time.time(),
                                keywords=entry.get("keywords", [keyword]),
                            )
                        )
                    return sentiments
        except Exception as exc:  # pragma: no cover - network errors not in tests
            logger.debug("Santiment sentiment error: %s", exc)
        return []

    async def collect_social_sentiment(self, keyword: str) -> List[SocialSentiment]:
        sentiments: List[SocialSentiment] = []
        sentiments.extend(await self._fetch_twitter_sentiment(keyword))
        sentiments.extend(await self._fetch_reddit_sentiment(keyword))
        sentiments.extend(await self._fetch_santiment_social(keyword))
        if self.event_bus:
            for s in sentiments:
                self.event_bus.publish(SOCIAL_SENTIMENT_STREAM, s)
        logger.debug("Coletados %d sentimentos sociais", len(sentiments))
        return sentiments

    # ----------------------------- On-chain -----------------------------
    async def _fetch_onchain_metrics(self) -> Dict[str, Any]:
        if not self.session or not self.onchain_enabled:
            return {}
        url = "https://api.blockchain.info/stats"
        try:
            async with self.session.get(url) as resp:
                if resp.status == 200:
                    return await resp.json()
        except Exception as exc:  # pragma: no cover - network errors not in tests
            logger.debug("On-chain metrics error: %s", exc)
        return {}

    async def _fetch_cryptoquant_metrics(self) -> Dict[str, Any]:
        """Coleta métricas via CryptoQuant."""
        if not self.session or not self.cryptoquant_enabled:
            return {}
        cfg = self.data_sources.get("onchain", {}).get("cryptoquant", {})
        url = cfg.get("api_endpoint", "")
        headers = {"X-API-KEY": cfg.get("api_key", "")}
        try:
            async with self.session.get(url, headers=headers) as resp:
                if resp.status == 200:
                    return await resp.json()
        except Exception as exc:  # pragma: no cover - network errors not in tests
            logger.debug("CryptoQuant metrics error: %s", exc)
        return {}

    async def _fetch_glassnode_metrics(self) -> Dict[str, Any]:
        """Coleta métricas via Glassnode."""
        if not self.session or not self.glassnode_enabled:
            return {}
        cfg = self.data_sources.get("onchain", {}).get("glassnode", {})
        url = cfg.get("api_endpoint", "")
        params = {"api_key": cfg.get("api_key", "")}
        try:
            async with self.session.get(url, params=params) as resp:
                if resp.status == 200:
                    return await resp.json()
        except Exception as exc:  # pragma: no cover - network errors not in tests
            logger.debug("Glassnode metrics error: %s", exc)
        return {}

    async def collect_onchain_data(self) -> List[OnChainSignal]:
        metrics = {}
        metrics.update(await self._fetch_onchain_metrics())
        metrics.update(await self._fetch_cryptoquant_metrics())
        metrics.update(await self._fetch_glassnode_metrics())
        signals = []
        ts = time.time()
        for metric, value in metrics.items():
            if isinstance(value, (int, float)):
                signals.append(
                    OnChainSignal(
                        metric=metric,
                        value=float(value),
                        timestamp=ts,
                        source="onchain",
                    )
                )
        if self.event_bus:
            for sig in signals:
                self.event_bus.publish(ONCHAIN_SIGNAL_STREAM, sig)
        logger.debug("Coletados %d sinais on-chain", len(signals))
        return signals

    def convert_to_holographic_events(
        self,
        market_data: List[MarketDataPoint],
        news_events: List[NewsEvent],
        universe_field_size: tuple = (200, 200),
        current_time: Optional[float] = None,
    ) -> List[HolographicEvent]:
        """
        Converte dados reais em eventos holográficos.

        Args:
            market_data: Dados de mercado
            news_events: Eventos de notícias
            universe_field_size: Tamanho do campo holográfico
            current_time: Timestamp atual

        Returns:
            Lista de eventos holográficos
        """

        if current_time is None:
            current_time = time.time()

        events = []
        news_events = self._deduplicate_news_events(news_events)

        # Obtém amplificações calibradas
        price_amp = self.amplification_calibrator.get_calibrated_amplification("price")
        news_amp = self.amplification_calibrator.get_calibrated_amplification("news")

        # Converte dados de mercado
        for data in market_data:
            # Calcula posição baseada no símbolo
            symbol_hash = hash(data.symbol) % (
                universe_field_size[0] * universe_field_size[1]
            )
            x = symbol_hash % universe_field_size[0]
            y = symbol_hash // universe_field_size[0]

            # Calcula amplitude baseada na mudança de preço (COM CALIBRAÇÃO)
            amplitude = min(abs(data.change_24h) / 2.0, 5.0) * (
                price_amp / 5.0
            )  # Normaliza pela calibração
            if data.change_24h < 0:
                amplitude = -amplitude

            events.append(
                HolographicEvent(
                    position=(x, y),
                    time=current_time,
                    amplitude=amplitude,
                    spatial_sigma=10.0,
                    temporal_sigma=5.0,
                    event_type="market_price",
                    source_data={
                        "symbol": data.symbol,
                        "price": data.price,
                        "volume": data.volume,
                        "change_24h": data.change_24h,
                        "source": data.source,
                    },
                    confidence=0.8,
                )
            )

        # Converte eventos de notícias com dados quânticos
        for news in news_events:
            # Mapeia para região de notícias (150, 150)
            news_hash = hash(news.title) % 2500
            x = 150 + (news_hash % 50) - 25
            y = 150 + (news_hash // 50) - 25

            # Garante dentro dos limites
            x = max(0, min(universe_field_size[0] - 1, x))
            y = max(0, min(universe_field_size[1] - 1, y))

            # Amplitude enriquecida com dados quânticos (COM CALIBRAÇÃO)
            base_amplitude = (news.sentiment_score * 4.0 + 1.0) * (
                news_amp / 4.0
            )  # Normaliza pela calibração

            # Se tem estado quântico, usa a magnitude do vetor
            if news.quantum_sentiment_state is not None:
                quantum_magnitude = np.linalg.norm(news.quantum_sentiment_state)
                base_amplitude *= quantum_magnitude

            events.append(
                HolographicEvent(
                    position=(x, y),
                    time=current_time,
                    amplitude=base_amplitude,
                    spatial_sigma=15.0,  # Maior para notícias
                    temporal_sigma=8.0,  # Maior duração
                    event_type="quantum_news_event",
                    source_data={
                        "title": news.title,
                        "sentiment": news.sentiment_score,
                        "quantum_encoded": news.quantum_sentiment_state is not None,
                        "quantum_state": (
                            news.quantum_sentiment_state.tolist()
                            if news.quantum_sentiment_state is not None
                            else None
                        ),
                        "source": news.source,
                        "url": news.url,
                    },
                    confidence=0.85,  # Maior confiança com dados quânticos
                )
            )

        logger.debug(
            f"Convertidos {len(events)} eventos holográficos "
            f"(price_amp={price_amp:.2f}, news_amp={news_amp:.2f})"
        )
        return events

    def _deduplicate_news_events(self, news_events: List[NewsEvent]) -> List[NewsEvent]:
        """Remove eventos duplicados e aplica limiar de score."""

        unique: Dict[str, NewsEvent] = {}
        for event in news_events:
            if abs(event.sentiment_score) < 0.05:
                continue
            guid = (
                event.guid
                or hashlib.sha1(f"{event.url}|{event.title}".encode()).hexdigest()
            )
            if guid not in unique:
                unique[guid] = event
        return list(unique.values())

    def set_collection_interval(self, interval: float) -> None:
        """Define o intervalo de coleta em segundos."""
        self.collection_interval = max(1.0, float(interval))
        self._persist_state()
        logger.info(
            "Intervalo de coleta ajustado para %.1f s", self.collection_interval
        )

    def _persist_state(self) -> None:
        save_json_safe(
            {"collection_interval": self.collection_interval},
            self.state_file.as_posix(),
        )

    async def start_real_time_collection(
        self,
        universe,
        collection_interval: Optional[float] = None,
    ) -> AsyncGenerator[List[HolographicEvent], None]:
        """Inicia coleta de dados em tempo real.

        Parameters
        ----------
        universe
            Instância do :class:`HolographicMarketUniverse`.
        collection_interval
            Intervalo entre coletas (segundos).

        Yields
        ------
        list[HolographicEvent]
            Listas de eventos holográficos em tempo real.
        """
        if collection_interval is not None:
            self.collection_interval = collection_interval
            self._persist_state()
        self.is_running = True
        logger.info(
            f"Iniciando coleta de dados em tempo real (intervalo: {collection_interval}s)"
        )

        while self.is_running:
            try:
                # Coleta todos os tipos de dados
                market_data = await self.collect_market_data()
                news_events = await self.collect_news_events()

                # Converte para eventos holográficos
                holographic_events = self.convert_to_holographic_events(
                    market_data,
                    news_events,
                    universe.field_size,
                )

                if self.event_bus:
                    self.event_bus.publish(
                        "market.data.updated",
                        MarketDataUpdated(
                            market_data=market_data,
                            news_events=news_events,
                        ),
                    )

                yield holographic_events

                # Aguarda próxima coleta
                await asyncio.sleep(self.collection_interval)

            except Exception as e:
                logger.error(f"Erro na coleta em tempo real: {e}")
                await asyncio.sleep(10)

    def stop_collection(self):
        """Para a coleta de dados."""
        self.is_running = False
        logger.info("Coleta de dados parada")


# Factory function
def create_real_data_collector(
    data_sources_config: Optional[Dict[str, Any]] = None,
    *,
    event_bus: Optional[SimpleEventBus] = None,
) -> RealDataCollector:
    """
    Cria instância do coletor de dados reais.

    Returns:
        Instância configurada do RealDataCollector
    """
    cfg = data_sources_config or load_data_sources_defaults()
    return RealDataCollector(data_sources_config=cfg, event_bus=event_bus)
