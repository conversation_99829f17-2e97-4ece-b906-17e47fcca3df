# src/qualia/consciousness/social_simulation_universe.py

from __future__ import annotations

from typing import Dict, Any, List

from .holographic_universe import HolographicMarketUniverse, HolographicEvent, HolographicPattern
from ..personas.base import BasePersona
from ..utils.logger import get_logger

logger = get_logger(__name__)

class SocialSimulationUniverse(HolographicMarketUniverse):
    """
    Uma especialização do HolographicMarketUniverse para simular a propagação de
    comportamento e influência social entre agentes (Personas).

    Esta classe utiliza a mecânica do universo holográfico, mas reinterpreta
    seus conceitos:
    - O "campo" representa um mapa de influência social ou de intenções.
    - "Eventos" são as ações prováveis das Personas.
    - "Padrões" são fenômenos comportamentais emergentes (ex: contágio, pânico).
    """

    def inject_persona_action_event(self, persona: BasePersona, probable_action: Dict[str, float], timestamp: float) -> None:
        """
        Converte a ação provável de uma persona em um evento holográfico e o injeta.

        Args:
            persona: A instância da persona que está agindo.
            probable_action: O DecisionVector com as probabilidades de ação (BUY, SELL, HOLD).
            timestamp: O timestamp do evento.
        """
        # Lógica para determinar a ação principal e a confiança
        action = max(probable_action, key=probable_action.get)
        confidence = probable_action[action]

        if action == "HOLD" or confidence < 0.1: # Ignorar ações de baixa confiança ou "HOLD"
            return

        # A amplitude do evento é a confiança da persona na sua ação
        # Ações de COMPRA são positivas, VENDAS são negativas
        amplitude = confidence if action == "BUY" else -confidence

        # A posição do evento é baseada no ID da persona (mapeamento simples)
        position_hash = hash(persona.get_persona_id())
        x = abs(position_hash) % self.field_size[0]
        y = abs(position_hash // self.field_size[0]) % self.field_size[1]

        event = HolographicEvent(
            position=(x, y),
            time=timestamp,
            amplitude=amplitude,
            spatial_sigma=10.0,  # Dispersão da influência da persona
            temporal_sigma=5.0, # Persistência da influência
            event_type=f"persona_action_{persona.__class__.__name__}_{action}",
            source_data=probable_action,
            confidence=confidence
        )

        # Usando o método da classe base para injetar o evento
        self.inject_holographic_event(event)
        logger.debug(f"Injetado evento de ação da persona {persona.get_persona_id()}: {action} com confiança {confidence:.2f}")

    def _classify_pattern(self, pattern_data: Dict[str, Any]) -> HolographicPattern | None:
        """
        Sobrepõe a classificação de padrões para identificar fenômenos comportamentais.

        Args:
            pattern_data: Dados brutos do padrão detectado pela análise wavelet.

        Returns:
            Um HolographicPattern com um tipo comportamental, ou None.
        """
        base_pattern = super()._classify_pattern(pattern_data)
        if base_pattern is None:
            return None

        # Reclassificar o tipo de padrão com base na semântica social
        # Esta é uma lógica de placeholder e pode ser muito mais sofisticada
        if base_pattern.pattern_type == "coherent_wave":
            if base_pattern.strength > 0.7:
                # Uma onda coerente e forte de amplitude positiva (compra) é um contágio otimista
                if pattern_data['avg_amplitude'] > 0:
                    base_pattern.pattern_type = "bullish_contagion"
                else:
                    base_pattern.pattern_type = "panic_cascade"
            else:
                # Ondas mais fracas podem ser apenas "tendências de opinião"
                base_pattern.pattern_type = "opinion_trend"
        
        elif base_pattern.pattern_type == "localized_pulse":
            # Pulsos localizados podem representar a absorção de liquidez por um Market Maker
            base_pattern.pattern_type = "liquidity_absorption"

        logger.info(f"Padrão comportamental classificado: {base_pattern.pattern_type}")
        return base_pattern
