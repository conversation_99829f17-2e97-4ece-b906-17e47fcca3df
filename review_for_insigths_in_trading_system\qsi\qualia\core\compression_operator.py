"""Quantum Compression Operator.

This module implements quantum-inspired data compression leveraging
simple PCA via SVD and wavelet transforms as fallbacks. The goal is to
reduce the entropy of input data while preserving relevant structure.
"""

from __future__ import annotations

import logging
from dataclasses import dataclass
from typing import Any, Dict

import numpy as np
from numpy.typing import ArrayLike

try:
    import pywt
except Exception:  # pragma: no cover - optional dependency
    pywt = None

logger = logging.getLogger(__name__)


@dataclass
class CompressionState:
    """Result of a compression step."""

    compressed_data: np.ndarray
    entropy_reduction: float
    timestamp: float


class QuantumCompressionOperator:
    """Operator that applies quantum-inspired compression techniques."""

    def __init__(self, config: Dict[str, Any] | None = None) -> None:
        cfg = config or {}
        self.target_dim = int(cfg.get("target_dim", 4))
        self.wavelet = cfg.get("wavelet", "db4")
        self.wavelet_levels = int(cfg.get("wavelet_levels", 2))

    def _entropy(self, data: np.ndarray) -> float:
        hist, _ = np.histogram(data, bins=256)
        prob = hist.astype(float)
        prob /= max(prob.sum(), 1.0)
        prob = prob[prob > 0]
        return float(-np.sum(prob * np.log(prob)))

    def _compress_pca(self, data: np.ndarray) -> np.ndarray:
        u, _, vt = np.linalg.svd(data - np.mean(data, axis=0), full_matrices=False)
        components = vt[: self.target_dim].T
        return np.dot(data, components)

    def _compress_wavelet(self, data: np.ndarray) -> np.ndarray:
        if pywt is None or not hasattr(pywt, "wavedec"):
            return data
        outputs = []
        for col in range(data.shape[1]):
            series = data[:, col]
            try:
                if hasattr(pywt, "dwt_max_level"):
                    max_lvl = pywt.dwt_max_level(len(series), self.wavelet)
                    lvl = min(self.wavelet_levels, max_lvl)
                else:
                    lvl = 1
                coeffs = pywt.wavedec(series, self.wavelet, level=lvl)
                outputs.append(coeffs[0])
            except Exception:
                outputs.append(series)
        result = np.vstack(outputs).T
        if result.shape[1] > self.target_dim:
            u, _, vt = np.linalg.svd(
                result - np.mean(result, axis=0), full_matrices=False
            )
            components = vt[: self.target_dim].T
            result = np.dot(result, components)
        return result

    async def compress(self, data: ArrayLike, timestamp: float) -> CompressionState:
        arr = np.asarray(data, dtype=float)
        if arr.ndim == 1:
            arr = arr.reshape(-1, 1)
        initial_entropy = self._entropy(arr)
        pca_result = self._compress_pca(arr)
        wavelet_result = self._compress_wavelet(arr)
        pca_entropy = self._entropy(pca_result)
        wavelet_entropy = self._entropy(wavelet_result)
        if pca_entropy <= wavelet_entropy:
            chosen = pca_result
            final_entropy = pca_entropy
        else:
            chosen = wavelet_result
            final_entropy = wavelet_entropy
        reduction = initial_entropy - final_entropy
        return CompressionState(chosen, reduction, timestamp)
