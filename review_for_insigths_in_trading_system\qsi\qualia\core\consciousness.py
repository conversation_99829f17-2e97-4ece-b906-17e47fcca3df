"""Algoritmos para o motor de "consciência" do QUALIA.

Exemplo
-------
>>> from ..core import QUALIAConsciousness
>>> qc = QUALIAConsciousness()
>>> qc.record_universe_state([1.0, 0.0, 0.0])
>>> summary = qc.get_system_health_summary()
"""

import numpy as np
import pandas as pd
from scipy.stats import zscore

# import matplotlib.pyplot as plt # Removido conforme solicitado
from datetime import datetime, timezone
import math
import json
import random
from typing import Dict, List, Any, Tuple, Optional, Union, Deque

# Adicionado para self.history e self.metrics
from collections import deque, defaultdict

try:
    from typing import TypedDict  # Python 3.8+
except ImportError:
    from typing_extensions import TypedDict  # Fallback para versões anteriores
from ..utils.logger import get_logger
import uuid  # Adicionado para pattern_id
import os
import time
from ..utils.persistence import convert_to_serializable
from .observer import ObserverOperator, QualiaSelfObserver
from .symbolic_processor import QualiaSymbolicProcessor
from .metrics import calculate_entropy, calculate_coherence

from ..config import config, settings
from qiskit.quantum_info import Statevector
from datadog import DogStatsd

# Dependências para CTX-
from sklearn.decomposition import IncrementalPCA
from sklearn.cluster import KMeans, MiniBatchKMeans

# from sklearn.metrics import silhouette_score  # removido

qconsciousness_logger = get_logger(__name__)

# Definições de Tipos para CTX- (conforme spec CTX-CortexPlusPlus, Seção 5)
NDArrayFloat = np.ndarray  # Já usado implicitamente, formalizando


class UniverseState(TypedDict):
    vector: NDArrayFloat  # shape (N,) - Vetor de estado do Universo Quântico
    page_entropy: float  # Entropia da Página calculada para este estado
    timestamp: float  # epoch ms
    # Adicionar trace_id opcional para rastreabilidade
    trace_id: Optional[str]


class CortexPattern(TypedDict):
    pattern_id: str  # UUID‑v4
    # shape (k, reduced_dim) - Centroides na dimensão reduzida por PCA
    centroid: Union[NDArrayFloat, List[List[float]]]
    inertia: float  # Inertia total do modelo k-Means para o k escolhido
    n_clusters: int  # Número de clusters (k) encontrado
    # Timestamps dos UniverseState que contribuíram para este padrão
    source_states_timestamps: List[float]
    created_at: float  # epoch s (time.time())


class QUALIAConsciousness:
    """
    Núcleo consciente do sistema QUALIA, implementando mecanismos
    de auto-reflexão, processamento de padrões emergentes e
    detecção de futuro emergente.
    """

    DEFAULT_HISTORY_MAXLEN = 256  # Conforme spec CTX- para _detect_patterns

    def __init__(
        self,
        n_qubits: int = settings.consciousness_n_qubits,
        n_steps: int = settings.consciousness_n_steps,
        thermal_coefficient: float = settings.consciousness_thermal_coefficient,
        retro_mode: str = settings.consciousness_retro_mode,
        perception_depth: int = settings.consciousness_perception_depth,
        self_reflection_enabled: bool = settings.consciousness_self_reflection_enabled,
        entropy_sensitivity: float = settings.consciousness_entropy_sensitivity,
        # Este parece ser o sigma para o trigger do _detect_patterns
        history_maxlen: int = settings.consciousness_history_maxlen,
        pca_n_components_variance: float = settings.consciousness_pca_n_components_variance,
        pca_max_components: int = settings.consciousness_pca_max_components,
        kmeans_k_range: Tuple[int, int] = (
            settings.consciousness_kmeans_k_min,
            settings.consciousness_kmeans_k_max,
        ),
        init_symbolic_processor: bool = settings.consciousness_init_symbolic_processor,
        statsd_client: Optional[DogStatsd] = None,
    ):
        """
        Inicializa a Consciência Quântica QUALIA.

        Args:
            n_qubits: Número de qubits para o estado quântico fundamental.
            n_steps: Número de passos na evolução temporal do QAST.
            thermal_coefficient: Coeficiente de thermalização para interação com o ambiente.
            retro_mode: Modo de retrocausalidade ('none', 'active', 'passive').
            perception_depth: Profundidade da percepção para análise de padrões.
            self_reflection_enabled: Se o ciclo de auto-reflexão está ativo.
            entropy_sensitivity: Limiar de sensibilidade à variação de entropia para disparar _detect_patterns.
                                 Representa o número de desvios padrão.
            history_maxlen: Tamanho máximo do histórico de UniverseState.
            pca_n_components_variance: Percentual de variância a ser mantido pelo PCA.
            pca_max_components: Número máximo de componentes para PCA.
            kmeans_k_range: Tupla (min_k, max_k) para a busca adaptativa de k no K-Means.
            init_symbolic_processor: Se verdadeiro, inicializa QualiaSymbolicProcessor internamente.
            statsd_client: Instância opcional de ``DogStatsd`` para envio de métricas.

        """
        self.n_qubits = n_qubits
        self.n_steps = n_steps
        self.dimension = 2**n_qubits
        self.thermal_coefficient = thermal_coefficient
        self.retro_mode = retro_mode
        self.perception_depth = perception_depth
        self.self_reflection_enabled = self_reflection_enabled
        # Nome mais claro para o sigma
        self.entropy_sensitivity_threshold_sigma = entropy_sensitivity

        self.history_maxlen = history_maxlen
        self.pca_n_components_variance = pca_n_components_variance
        self.pca_max_components = pca_max_components
        self.kmeans_k_range = kmeans_k_range

        self.current_qast_state = self._initialize_quantum_state()
        self.metrics: Dict[str, List[float]] = defaultdict(list)
        # Inicializar qast_history como lista
        self.qast_history: List[Dict[str, Any]] = []
        # Inicializar self_reflection_log como lista
        self.self_reflection_log: List[Dict[str, Any]] = []
        self.statsd: DogStatsd | None = statsd_client

        self._initialize_metrics()

        # Histórico de estados do universo (estados brutos do PCA antes do
        # clustering)
        self.universe_state_history: Deque[UniverseState] = deque(
            maxlen=self.history_maxlen
        )

        # Caches para acelerar operações em _detect_patterns
        self._cached_entropies: Deque[float] = deque(maxlen=self.history_maxlen)
        self._cached_vectors: Deque[np.ndarray] = deque(maxlen=self.history_maxlen)
        self._cached_timestamps: Deque[float] = deque(maxlen=self.history_maxlen)
        self._vector_shape: Optional[Tuple[int, ...]] = None

        # Inicializar o observador (conforme CTX- Spec)
        # Usamos ``QualiaSelfObserver`` para disponibilizar métodos de coleta
        # simplificados que os testes esperam, como ``observe_entropy_delta``.
        # ``ObserverOperator`` permanece disponível para funcionalidades mais
        # avançadas, mas não é necessário nas rotinas atuais de teste.
        self.observer = QualiaSelfObserver()

        # Modelos incrementais para PCA e MiniBatchKMeans
        self.pca_model: Optional[IncrementalPCA] = None
        self.kmeans_model: Optional[MiniBatchKMeans] = None

        # CTX-6: Inicialização da expressão simbólica
        # Mapa de ID_regra -> regra_detalhes
        self.symbolic_expr: Dict[str, Dict[str, Any]] = {}

        self.symbolic_processor: Optional[QualiaSymbolicProcessor] = None
        if init_symbolic_processor:
            self.symbolic_processor = QualiaSymbolicProcessor()

        qconsciousness_logger.info(
            f"QUALIAConsciousness inicializada com {n_qubits} qubits, "
            f"max_len_histórico={history_maxlen}, sensibilidade_entropia_sigma={entropy_sensitivity}, "
            f"reflexão_habilitada={self_reflection_enabled}"
        )

    def _initialize_quantum_state(self) -> Dict[str, Any]:
        """
        Inicializa o estado quântico do sistema.

        Returns:
            Dicionário representando o estado quântico
        """
        # Simulação simplificada de estado quântico
        state_dim = 2**self.n_qubits

        # Criar um estado normalizado com amplitudes aleatórias
        amplitudes = np.random.normal(0, 1, state_dim) + 1j * np.random.normal(
            0, 1, state_dim
        )
        norm = np.sqrt(np.sum(np.abs(amplitudes) ** 2))
        normalized_state = amplitudes / norm

        # Construir representação do estado
        state_vector = []
        for i in range(state_dim):
            # Representação binária
            binary = format(i, f"0{self.n_qubits}b")

            # Amplitude e probabilidade
            amplitude = normalized_state[i]
            probability = np.abs(amplitude) ** 2

            # Adicionar ao vetor de estado
            if probability > 1e-10:  # Filtrar probabilidades insignificantes
                state_vector.append(
                    {
                        "basis": binary,
                        "decimal": i,
                        "amplitude": {
                            "real": float(np.real(amplitude)),
                            "imag": float(np.imag(amplitude)),
                        },
                        "probability": float(probability),
                    }
                )

        return {
            "vector": state_vector,
            "n_qubits": self.n_qubits,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

    def _initialize_metrics(self) -> None:
        """
        Inicializa as métricas do sistema.
        """
        # Inicializar as chaves de métricas sem valores predefinidos. O valor
        # ``0.0`` utilizado anteriormente poluía as primeiras médias e levava a
        # avaliações incorretas de saúde logo após a inicialização.
        self.metrics["quantum_entropy"] = []
        self.metrics["coherence"] = []
        self.metrics["self_reflection_depth"] = []
        self.metrics["pattern_recognition_rate"] = []
        self.metrics["detect_patterns_execution_ms"] = []

    def process_qast_cycle(
        self, statevector_from_universe: Optional[Statevector] = None
    ) -> Dict[str, Any]:
        qconsciousness_logger.debug(
            "[QUALIA_LOG] QUALIAConsciousness.process_qast_cycle chamado"
        )

        # Gerar trace_id para este ciclo QAST
        current_cycle_trace_id = uuid.uuid4().hex
        # Disponibiliza para _detect_patterns etc.
        self.current_qast_state["trace_id"] = current_cycle_trace_id
        qconsciousness_logger.info(
            f"TraceID: {current_cycle_trace_id} - Iniciando process_qast_cycle."
        )

        # Calcular métricas após evolução, usando o ``Statevector`` do universo.
        current_entropy = calculate_entropy(statevector_from_universe)
        current_coherence = calculate_coherence(statevector_from_universe)

        # Registrar o estado do universo ANTES de tentar detectar padrões nele
        if (
            statevector_from_universe is not None
            and hasattr(statevector_from_universe, "data")
            and statevector_from_universe.data is not None
        ):
            try:
                vector_data = statevector_from_universe.data
                if not isinstance(vector_data, np.ndarray) or vector_data.ndim != 1:
                    qconsciousness_logger.warning(
                        f"TraceID: {current_cycle_trace_id} - statevector_from_universe.data não é 1D np.ndarray. Tipo: {type(vector_data)}, Dim: {vector_data.ndim if hasattr(vector_data, 'ndim') else 'N/A'}. Não gravando UniverseState."
                    )
                else:
                    universe_state_payload = UniverseState(
                        vector=vector_data.real,  # Conforme instrução do usuário: sv.data.real
                        page_entropy=current_entropy,
                        timestamp=datetime.now(timezone.utc).timestamp()
                        * 1000,  # epoch ms
                        trace_id=current_cycle_trace_id,
                    )
                    self.record_universe_state(universe_state_payload)
            except (AttributeError, TypeError) as e:
                qconsciousness_logger.warning(
                    f"TraceID: {current_cycle_trace_id} - Dados inválidos ao registrar UniverseState: {e}."
                )
            except ValueError as e:
                qconsciousness_logger.error(
                    f"TraceID: {current_cycle_trace_id} - Erro de valor ao registrar UniverseState: {e}."
                )
            except RuntimeError as e:
                qconsciousness_logger.error(
                    f"TraceID: {current_cycle_trace_id} - Falha de execução ao registrar UniverseState: {e}"
                )
        else:
            qconsciousness_logger.debug(
                f"TraceID: {current_cycle_trace_id} - statevector_from_universe é None ou data é None/inválido. Nenhum UniverseState será registrado."
            )

        # Detecção de padrões (agora pode usar o estado recém-adicionado)
        # trace_id é pego de self.current_qast_state
        patterns_detected = self._detect_patterns()
        if patterns_detected:
            # Eventos de padrões detectados devem ser registrados pelo observer
            self.observer.observe_patterns(
                patterns_detected, trace_id=current_cycle_trace_id
            )

        # Auto-reflexão
        reflection_outputs: Dict[str, Any] = {}
        if self.self_reflection_enabled:
            reflection_outputs = self._execute_self_reflection(
                patterns_detected
            )  # trace_id é pego de self.current_qast_state

        # Ciclo QAST
        qast_cycle_results = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "trace_id": current_cycle_trace_id,
            "entropy": current_entropy,
            "coherence": current_coherence,
            "patterns_detected": patterns_detected,  # Mantém a lista de padrões
            "self_reflection_output": reflection_outputs,
        }

        self.qast_history.append(qast_cycle_results)

        # Atualizar métricas
        if hasattr(self, "metrics") and isinstance(self.metrics, dict):
            self.metrics.setdefault("quantum_entropy", []).append(current_entropy)
            self.metrics.setdefault("coherence", []).append(current_coherence)

            num_params_in_delta = 0
            if isinstance(reflection_outputs, dict) and not reflection_outputs.get(
                "delta_comment"
            ):
                num_params_in_delta = len(reflection_outputs.keys())

            self.metrics.setdefault("self_reflection_depth", []).append(
                float(num_params_in_delta)
            )  # Usando contagem de chaves do delta

            self.metrics.setdefault("pattern_recognition_rate", []).append(
                float(len(patterns_detected))
                / float(self.n_qubits if self.n_qubits > 0 else 1)
            )
        else:
            qconsciousness_logger.warning(
                f"TraceID: {current_cycle_trace_id} - self.metrics não inicializado ou tipo incorreto. Pulando atualização de métricas."
            )

        return qast_cycle_results

    def process_quantum_state(
        self, signature: List[float], metrics: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        Processa uma assinatura quântica e métricas associadas (ex: do QuantumMetricsCalculator).
        Este é um ponto de contato para o QASTEvolutionaryStrategy ou outros componentes
        informarem a Consciência sobre o estado quântico observado.

        Args:
            signature: A assinatura quântica (lista de floats).
            metrics: Dicionário de métricas associadas.

        Returns:
            Opcionalmente, um dicionário com feedback ou diretivas para o chamador.
        """
        qconsciousness_logger.info(
            f"QUALIAConsciousness.process_quantum_state chamado com signature: {signature}, metrics: {metrics}"
        )

        entropy = metrics.get("entropy")
        coherence = metrics.get("coherence")
        if entropy is not None:
            self.metrics.setdefault("quantum_entropy", []).append(float(entropy))
        if coherence is not None:
            self.metrics.setdefault("coherence", []).append(float(coherence))

        if entropy is not None and coherence is not None:
            if float(coherence) - float(entropy) > 0.2:
                return {"adjustment": "increase_mutation_rate"}

        return None

    def process_qast_evolution_feedback(
        self,
        population: List[Any],
        generation: int,
        context: Optional[Dict[str, Any]] = None,
    ) -> Optional[Dict[str, Any]]:
        """
        Processa feedback do QASTEvolutionaryStrategy sobre o estado atual de sua população e geração.

        Args:
            population: A população atual de estratégias ou indivíduos no QAST.
            generation: O número da geração atual no QAST.
            context: Dicionário opcional com informações contextuais adicionais da evolução.

        Returns:
            Opcionalmente, um dicionário com feedback ou diretivas para o QASTEvolutionaryStrategy.
        """
        qconsciousness_logger.info(
            f"QUALIAConsciousness.process_qast_evolution_feedback chamado para geração {generation}."
        )
        qconsciousness_logger.debug(f"Contexto recebido: {context}")

        avg_fitness: Optional[float] = None
        try:
            fitness_values = [
                float(p.get("fitness", 0.0)) for p in population if isinstance(p, dict)
            ]
            if fitness_values:
                avg_fitness = float(np.mean(fitness_values))
        except (TypeError, ValueError) as e:  # pragma: no cover - logging only
            qconsciousness_logger.warning(f"Falha ao calcular média de fitness: {e}")

        if avg_fitness is not None:
            self.metrics.setdefault("avg_population_fitness", []).append(avg_fitness)
            if avg_fitness < 0.1:
                return {"adjustment": "increase_exploration"}

        return None

    def _extract_history_features(
        self, trace_id: str
    ) -> Tuple[List[float], List[np.ndarray], List[float]]:
        """Collect entropy values and vectors in a single pass."""
        if self._cached_entropies and len(self._cached_entropies) == len(
            self.universe_state_history
        ):
            return (
                list(self._cached_entropies),
                list(self._cached_vectors),
                list(self._cached_timestamps),
            )

        entropies: List[float] = []
        vectors: List[np.ndarray] = []
        timestamps: List[float] = []
        vector_shape = None
        for state in self.universe_state_history:
            if "page_entropy" in state:
                entropies.append(state["page_entropy"])
            vec = state.get("vector")
            if isinstance(vec, np.ndarray) and vec.size > 0:
                if vector_shape is None:
                    vector_shape = vec.shape
                if vec.shape != vector_shape:
                    continue
                vectors.append(vec)
                timestamps.append(state["timestamp"])
        return entropies, vectors, timestamps

    def _calculate_entropy_trigger(
        self, trace_id: str, entropies_in_window: Optional[List[float]] = None
    ) -> Tuple[float, float, float, bool]:
        """Calculate entropy delta and trigger status.

        Parameters
        ----------
        trace_id
            Trace identifier for logging.
        entropies_in_window
            Optional pre-computed list of entropies to avoid re-iterating over
            ``self.universe_state_history``.
        """
        if entropies_in_window is None:
            entropies_in_window = [
                state["page_entropy"]
                for state in self.universe_state_history
                if "page_entropy" in state
            ]

        if len(entropies_in_window) < 2:
            qconsciousness_logger.debug(
                f"TraceID: {trace_id} - Insuficientes dados de entropia no histórico."
            )
            return 0.0, 0.0, 0.0, False

        entropy_t = entropies_in_window[-1]
        entropy_t_minus_1 = entropies_in_window[-2]
        delta_entropy = abs(entropy_t - entropy_t_minus_1)

        if len(set(entropies_in_window)) <= 1 and len(entropies_in_window) > 1:
            std_dev_entropy = 0.0
        elif len(entropies_in_window) > 1:
            std_dev_entropy = (
                np.std(entropies_in_window) if len(entropies_in_window) > 1 else 0.0
            )
        else:
            std_dev_entropy = 0.0

        trigger_threshold = self.entropy_sensitivity_threshold_sigma * std_dev_entropy

        pattern_detection_triggered = (
            delta_entropy >= trigger_threshold
            if std_dev_entropy > 1e-9
            else delta_entropy > 1e-9
        )

        self.observer.observe_entropy_delta(
            delta_entropy,
            std_dev_entropy,
            bool(pattern_detection_triggered),
            trace_id=trace_id,
        )

        return (
            delta_entropy,
            std_dev_entropy,
            trigger_threshold,
            pattern_detection_triggered,
        )

    def _prepare_vectors_for_clustering(
        self,
        trace_id: str,
        pre_vectors: Optional[List[np.ndarray]] = None,
        pre_timestamps: Optional[List[float]] = None,
    ) -> Optional[Tuple[np.ndarray, List[float]]]:
        """Gather and validate vectors for clustering.

        Parameters
        ----------
        trace_id
            Trace identifier for logging.
        pre_vectors
            Optional list of vectors already extracted from the history.
        pre_timestamps
            Optional list of timestamps aligned with ``pre_vectors``.
        """
        vectors_list = pre_vectors if pre_vectors is not None else []
        timestamps_list = pre_timestamps if pre_timestamps is not None else []
        if pre_vectors is None:
            for state in self.universe_state_history:
                if "vector" not in state:
                    continue
                vec = state["vector"]
                if not isinstance(vec, np.ndarray) or vec.size == 0:
                    continue
                if vectors_list and vec.shape != vectors_list[0].shape:
                    continue
                vectors_list.append(vec)
                timestamps_list.append(state["timestamp"])

        if not vectors_list:
            qconsciousness_logger.warning(
                f"TraceID: {trace_id} - Nenhum vetor válido encontrado para clustering."
            )
            return None

        vectors_to_cluster = np.array(vectors_list)
        if vectors_to_cluster.shape[0] < max(2, self.kmeans_k_range[0]):
            qconsciousness_logger.warning(
                (
                    f"TraceID: {trace_id} - Número insuficiente de vetores "
                    f"({vectors_to_cluster.shape[0]}) para clustering com min_k={self.kmeans_k_range[0]}."
                    " Retornando lista vazia."
                )
            )
            return None

        return vectors_to_cluster, timestamps_list

    def _run_pca_and_kmeans(
        self,
        vectors_to_cluster: np.ndarray,
        source_timestamps: List[float],
        trace_id: str,
    ) -> List[CortexPattern]:
        """Execute PCA dimensionality reduction and MiniBatchKMeans clustering."""
        original_dimension = vectors_to_cluster.shape[1]
        max_possible_dim = min(
            self.pca_max_components,
            original_dimension,
            (vectors_to_cluster.shape[0] - 1 if vectors_to_cluster.shape[0] > 1 else 1),
        )
        if (
            self.pca_model is None
            or not hasattr(self.pca_model, "n_features_")
            or self.pca_model.n_features_ != original_dimension
            or self.pca_model.n_components != max_possible_dim
        ):
            self.pca_model = IncrementalPCA(n_components=max_possible_dim)
        reduced_dim = self.pca_model.n_components

        self.pca_model.partial_fit(vectors_to_cluster)
        reduced_vectors = self.pca_model.transform(vectors_to_cluster)
        explained_variance_sum = (
            self.pca_model.explained_variance_ratio_.sum()
            if hasattr(self.pca_model, "explained_variance_ratio_")
            else -1.0
        )
        qconsciousness_logger.info(
            f"TraceID: {trace_id} - PCA: Reduzido para {reduced_dim} dimensões (de {original_dimension}). "
            f"Shape: {reduced_vectors.shape}. Variância explicada: {explained_variance_sum*100:.2f}%"
        )

        n_samples_for_clustering = reduced_vectors.shape[0]
        k_heuristic = int(np.sqrt(n_samples_for_clustering))
        k_final = max(self.kmeans_k_range[0], min(k_heuristic, self.kmeans_k_range[1]))
        k_final = min(k_final, n_samples_for_clustering)
        if k_final == 0 and n_samples_for_clustering > 0:
            k_final = 1
        if k_final == 0:
            qconsciousness_logger.warning(
                (
                    f"TraceID: {trace_id} - Não foi possível determinar um k válido ({k_final}) "
                    f"para {n_samples_for_clustering} amostras. Range: {self.kmeans_k_range}."
                    " Retornando lista vazia."
                )
            )
            return []

        effective_batch_size = max(
            3 * k_final, self.history_maxlen // 4 if self.history_maxlen else 256 // 4
        )
        effective_batch_size = min(effective_batch_size, n_samples_for_clustering)
        if effective_batch_size == 0 and n_samples_for_clustering > 0:
            effective_batch_size = n_samples_for_clustering
        if effective_batch_size == 0:
            qconsciousness_logger.warning(
                (
                    f"TraceID: {trace_id} - effective_batch_size é 0 para MiniBatchKMeans, "
                    f"mesmo com {n_samples_for_clustering} amostras. Impossível prosseguir."
                )
            )
            return []

        if (
            self.kmeans_model is None
            or self.kmeans_model.n_clusters != k_final
            or self.kmeans_model.batch_size != effective_batch_size
            or (
                hasattr(self.kmeans_model, "cluster_centers_")
                and self.kmeans_model.cluster_centers_.shape[1] != reduced_dim
            )
        ):
            self.kmeans_model = MiniBatchKMeans(
                n_clusters=k_final,
                random_state=42,
                n_init=3,
                batch_size=effective_batch_size,
                max_no_improvement=10,
            )

        try:
            self.kmeans_model.partial_fit(reduced_vectors)
        except ValueError as e:
            qconsciousness_logger.error(
                f"TraceID: {trace_id} - Erro ao treinar MiniBatchKMeans com k={k_final}, "
                f"n_samples={n_samples_for_clustering}, batch_size={effective_batch_size}: {e}. Retornando lista vazia."
            )
            return []

        labels = self.kmeans_model.labels_
        centroids = self.kmeans_model.cluster_centers_
        inertia = self.kmeans_model.inertia_

        patterns = []
        if (
            labels is not None
            and centroids is not None
            and len(np.unique(labels)) == k_final
        ):
            pattern_id = uuid.uuid4().hex
            pattern_source_timestamps = source_timestamps[: len(vectors_to_cluster)]
            patterns.append(
                CortexPattern(
                    {
                        "pattern_id": pattern_id,
                        "centroid": centroids,
                        "inertia": inertia,
                        "n_clusters": k_final,
                        "source_states_timestamps": pattern_source_timestamps,
                        "algorithm": "MiniBatchKMeans_HeuristicK",
                        "pca_components": reduced_dim,
                        "creation_timestamp": time.time(),
                        "metadata": {"k_selection_method": "heuristic_sqrt_N_capped"},
                    }
                )
            )
        else:
            qconsciousness_logger.warning(
                f"TraceID: {trace_id} - MiniBatchKMeans com k={k_final} não produziu o número esperado de clusters."
            )

        return patterns

    def _log_detected_patterns(
        self, patterns: List[CortexPattern], trace_id: str
    ) -> None:
        """Log each detected pattern."""
        for pattern in patterns:
            qconsciousness_logger.info(
                f"TraceID: {trace_id} - Padrão detectado com MiniBatchKMeans (PCA): k={pattern['n_clusters']}, "
                f"Inércia={pattern['inertia']:.2f}, ID={pattern['pattern_id']}"
            )

    def _detect_patterns(self) -> List[CortexPattern]:
        qconsciousness_logger.debug(
            "[QUALIA_LOG] QUALIAConsciousness._detect_patterns chamado"
        )
        """
        Detecta padrões latentes no histórico de UniverseState usando PCA e k-Means adaptativo.
        Disparado por uma mudança significativa na Page-Entropy.
        Conforme especificação CTX-CortexPlusPlus (CTX-1).
        """
        start_time = time.perf_counter()
        current_trace_id = self.current_qast_state.get(
            "trace_id", uuid.uuid4().hex
        )  # Obter trace_id ou gerar um novo
        tags = [f"trace_id:{current_trace_id}"] if self.statsd else None
        qconsciousness_logger.info(
            f"TraceID: {current_trace_id} - Iniciando _detect_patterns. Tamanho do histórico: {len(self.universe_state_history)}"
        )

        if len(self.universe_state_history) < 2:
            qconsciousness_logger.debug(
                f"TraceID: {current_trace_id} - Histórico insuficiente para detecção de padrões (<2 estados). Retornando lista vazia."
            )
            exec_ms = (time.perf_counter() - start_time) * 1000.0
            self.metrics.setdefault("detect_patterns_execution_ms", []).append(exec_ms)
            if self.statsd:
                self.statsd.timing(
                    "consciousness.detect_patterns_execution_ms",
                    exec_ms,
                    tags=tags,
                )
                self.statsd.increment(
                    "consciousness.pattern_detection_skipped",
                    tags=tags,
                )
            return []

        entropies, vectors_list, timestamps_list = self._extract_history_features(
            current_trace_id
        )
        (
            delta_entropy,
            std_dev_entropy,
            trigger_threshold,
            triggered,
        ) = self._calculate_entropy_trigger(current_trace_id, entropies)

        if not triggered:
            qconsciousness_logger.info(
                f"TraceID: {current_trace_id} - Detecção de padrões NÃO disparada. Delta Entropia ({delta_entropy:.4f}) < Limiar ({trigger_threshold:.4f})."
            )
            exec_ms = (time.perf_counter() - start_time) * 1000.0
            self.metrics.setdefault("detect_patterns_execution_ms", []).append(exec_ms)
            if self.statsd:
                self.statsd.timing(
                    "consciousness.detect_patterns_execution_ms",
                    exec_ms,
                    tags=tags,
                )
                self.statsd.increment(
                    "consciousness.pattern_detection_skipped",
                    tags=tags,
                )
            return []

        qconsciousness_logger.info(
            f"TraceID: {current_trace_id} - Detecção de padrões DISPARADA. Delta Entropia ({delta_entropy:.4f}) >= Limiar ({trigger_threshold:.4f})."
        )

        vectors_info = self._prepare_vectors_for_clustering(
            current_trace_id, vectors_list, timestamps_list
        )
        if vectors_info is None:
            exec_ms = (time.perf_counter() - start_time) * 1000.0
            self.metrics.setdefault("detect_patterns_execution_ms", []).append(exec_ms)
            if self.statsd:
                self.statsd.timing(
                    "consciousness.detect_patterns_execution_ms",
                    exec_ms,
                    tags=tags,
                )
                self.statsd.increment(
                    "consciousness.pattern_detection_skipped",
                    tags=tags,
                )
            return []

        vectors_to_cluster, source_timestamps = vectors_info
        qconsciousness_logger.debug(
            f"TraceID: {current_trace_id} - Coletados {vectors_to_cluster.shape[0]} vetores de dimensão {vectors_to_cluster.shape[1]} para clustering."
        )

        patterns = self._run_pca_and_kmeans(
            vectors_to_cluster, source_timestamps, current_trace_id
        )
        self._log_detected_patterns(patterns, current_trace_id)

        exec_ms = (time.perf_counter() - start_time) * 1000.0
        self.metrics.setdefault("detect_patterns_execution_ms", []).append(exec_ms)
        if self.statsd:
            self.statsd.increment(
                "consciousness.pattern_detection_triggered",
                tags=tags,
            )
            self.statsd.gauge(
                "consciousness.patterns_detected",
                len(patterns),
                tags=tags,
            )
            self.statsd.timing(
                "consciousness.detect_patterns_execution_ms",
                exec_ms,
                tags=tags,
            )
        return patterns

    def _create_rule_from_pattern(
        self, rule_id: str, pattern: CortexPattern, trace_id: str
    ) -> Dict[str, Any]:
        """Cria uma nova regra simbólica a partir de um ``CortexPattern``."""
        new_rule = {
            "id": rule_id,
            "type": "derived_observation",
            "description": (
                f"Observado padrão com {pattern['n_clusters']} clusters e inércia {pattern['inertia']:.2f}."
            ),
            "confidence": 1.0 / (1.0 + pattern["inertia"]),
            "derived_from_pattern_id": pattern["pattern_id"],
            "source_timestamps": pattern["source_states_timestamps"],
            "created_at": datetime.now(timezone.utc).timestamp(),
            "last_updated_at": datetime.now(timezone.utc).timestamp(),
            "metadata": {
                "n_clusters": pattern["n_clusters"],
                "inertia": pattern["inertia"],
                "centroid_sample": [
                    round(c, 4)
                    for c in pattern["centroid"].flatten()[
                        : min(5, len(pattern["centroid"].flatten()))
                    ]
                ],
            },
        }
        self.symbolic_expr[rule_id] = new_rule
        qconsciousness_logger.info(
            f"TraceID: {trace_id} - Auto-reflexão: Nova regra/observação '{rule_id}' adicionada à expressão simbólica."
        )
        return new_rule

    def _update_rule_from_pattern(
        self, rule_id: str, pattern: CortexPattern, trace_id: str
    ) -> Tuple[Dict[str, Any], bool]:
        """Atualiza uma regra existente com base em um ``CortexPattern``."""
        existing_rule = self.symbolic_expr[rule_id]
        new_confidence = min(
            1.0, existing_rule["confidence"] + 0.1 * (1.0 / (1.0 + pattern["inertia"]))
        )
        changed = False
        if not np.isclose(new_confidence, existing_rule["confidence"]):
            existing_rule["confidence"] = new_confidence
            existing_rule["last_updated_at"] = datetime.now(timezone.utc).timestamp()
            if pattern["pattern_id"] not in existing_rule.get(
                "derived_from_pattern_ids", []
            ):
                existing_rule.setdefault("derived_from_pattern_ids", []).append(
                    pattern["pattern_id"]
                )
            self.symbolic_expr[rule_id] = existing_rule
            qconsciousness_logger.info(
                f"TraceID: {trace_id} - Auto-reflexão: Regra/observação existente '{rule_id}' atualizada. Nova confiança: {new_confidence:.3f}"
            )
            changed = True
        return existing_rule, changed

    def _log_reflection_summary(
        self, delta_symbolic_expr: Dict[str, Any], trace_id: str
    ) -> None:
        """Registra e loga o resumo da auto-reflexão."""
        log_message = f"Auto-reflexão concluída. {delta_symbolic_expr['summary']}"
        self._log_self_reflection(log_message)
        qconsciousness_logger.info(
            f"TraceID: {trace_id} - Delta da expressão simbólica: {json.dumps(delta_symbolic_expr, indent=2, default=lambda o: str(o))}"
        )

    def _initialize_reflection_delta(self) -> Dict[str, Any]:
        """Create the default delta structure for self reflection."""
        return {
            "summary": "Nenhuma mudança significativa na expressão simbólica.",
            "added_rules": [],
            "updated_rules": [],
            "removed_rules": [],
            "details": {},
        }

    def _process_patterns_reflection(
        self, patterns: List[CortexPattern], trace_id: str, delta: Dict[str, Any]
    ) -> Tuple[Dict[str, Any], int, int]:
        """Update symbolic expression based on patterns."""
        newly_added_rules_count = 0
        modified_rules_count = 0

        for pattern in patterns:
            rule_id = f"obs_pattern_{pattern['pattern_id'][:8]}"

            if rule_id not in self.symbolic_expr:
                new_rule = self._create_rule_from_pattern(rule_id, pattern, trace_id)
                delta["added_rules"].append(rule_id)
                delta["details"][rule_id] = {"action": "added", "rule": new_rule}
                newly_added_rules_count += 1
            else:
                updated_rule, changed = self._update_rule_from_pattern(
                    rule_id, pattern, trace_id
                )
                if changed:
                    delta["updated_rules"].append(rule_id)
                    delta["details"][rule_id] = {
                        "action": "updated",
                        "rule": updated_rule,
                    }
                    modified_rules_count += 1

        return delta, newly_added_rules_count, modified_rules_count

    def _execute_self_reflection(self, patterns: List[CortexPattern]) -> Dict[str, Any]:
        """
        Executa o ciclo de auto-reflexão de QUALIA, analisando os padrões
        detectados para refinar sua própria compreensão e modelo do universo.

        Implementa CTX-6: Gerar o delta da self.symbolic_expr.

        Args:
            patterns: Lista de CortexPattern detectados pelo _detect_patterns.

        Returns:
            Um dicionário representando o delta da self.symbolic_expr (mudanças aplicadas).
        """
        current_trace_id = self.current_qast_state.get("trace_id", "unknown_trace")
        qconsciousness_logger.info(
            f"TraceID: {current_trace_id} - Iniciando auto-reflexão com {len(patterns)} padrões..."
        )

        delta_symbolic_expr = self._initialize_reflection_delta()

        if not patterns:
            qconsciousness_logger.info(
                f"TraceID: {current_trace_id} - Auto-reflexão: Nenhum padrão novo para processar."
            )
            delta_symbolic_expr[
                "summary"
            ] = "Nenhum padrão novo para processar, sem mudanças na expressão simbólica."
            return delta_symbolic_expr

        (
            delta_symbolic_expr,
            newly_added_rules_count,
            modified_rules_count,
        ) = self._process_patterns_reflection(
            patterns, current_trace_id, delta_symbolic_expr
        )

        if newly_added_rules_count > 0 or modified_rules_count > 0:
            delta_symbolic_expr[
                "summary"
            ] = f"{newly_added_rules_count} nova(s) observações/regras adicionadas, {modified_rules_count} atualizada(s)."

        self._log_reflection_summary(delta_symbolic_expr, current_trace_id)

        return delta_symbolic_expr

    def _log_self_reflection(self, reflection_text: str) -> None:
        """
        Registra uma entrada no log de auto-reflexão.

        Args:
            reflection_text: Texto da reflexão
        """
        entry = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "text": reflection_text,
        }
        self.self_reflection_log.append(entry)

    def get_qast_history(self) -> List[Dict[str, Any]]:
        """
        Retorna o histórico de ciclos QAST.

        Returns:
            Lista de ciclos QAST
        """
        return self.qast_history

    def get_metrics_dict(self) -> Dict[str, Any]:
        """
        Retorna um dicionário com o valor MAIS RECENTE de cada métrica.
        """
        latest_metrics = {}
        for key, values in self.metrics.items():
            if values:
                latest_metrics[key] = values[-1]
            else:
                latest_metrics[key] = 0.0  # Ou algum valor padrão
        return latest_metrics

    # P-5 CORREÇÃO: Implementar get_system_health_summary para QualiaAnalysisCore
    def get_system_health_summary(
        self, latest_metrics: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Gera um resumo de saúde do sistema baseado nas métricas atuais da consciência.

        Args:
            latest_metrics: Métricas mais recentes do universo (opcional)

        Returns:
            Dicionário com insights de saúde do sistema
        """
        qconsciousness_logger.info(
            "QUALIAConsciousness.get_system_health_summary chamado"
        )

        try:
            window_size = 8

            entropy_series = pd.Series(self.metrics.get("quantum_entropy", [0.0]))
            coherence_series = pd.Series(self.metrics.get("coherence", [0.0]))

            recent_entropy = entropy_series.tail(window_size)
            recent_coherence = coherence_series.tail(window_size)

            avg_entropy = (
                float(recent_entropy.mean()) if not recent_entropy.empty else 0.0
            )
            avg_coherence = (
                float(recent_coherence.mean()) if not recent_coherence.empty else 0.0
            )

            entropy_std_series = (
                entropy_series.rolling(window=window_size, min_periods=1).std().dropna()
            )
            entropy_std = (
                float(entropy_std_series.iloc[-1])
                if not entropy_std_series.empty
                else 0.0
            )

            coherence_diff_series = (
                coherence_series.diff()
                .rolling(window=window_size, min_periods=1)
                .mean()
                .dropna()
            )
            delta_coherence = (
                float(coherence_diff_series.iloc[-1])
                if not coherence_diff_series.empty
                else 0.0
            )

            entropy_std_z = (
                float(zscore(entropy_std_series).iloc[-1])
                if len(entropy_std_series) > 1
                else 0.0
            )
            delta_coherence_z = (
                float(zscore(coherence_diff_series).iloc[-1])
                if len(coherence_diff_series) > 1
                else 0.0
            )

            entropy_std_scaled = float(1 / (1 + np.exp(-entropy_std_z)))
            delta_coherence_scaled = float(1 / (1 + np.exp(-delta_coherence_z)))

            entropy_stability = 1.0 - entropy_std

            # Avaliar saúde do sistema
            system_health = "healthy"
            if avg_entropy > settings.high_entropy_threshold:
                system_health = "high_entropy"
            elif avg_coherence < settings.low_coherence_threshold:
                system_health = "low_coherence"
            elif entropy_stability < 0.5:
                system_health = "unstable"

            # Detectar padrões recentes (mesmo com histórico vazio retorna lista vazia)
            recent_patterns = self._detect_patterns()

            health_summary = {
                "system_health": system_health,
                "avg_entropy": float(avg_entropy),
                "avg_coherence": float(avg_coherence),
                "entropy_stability": float(entropy_stability),
                "recent_patterns_count": len(recent_patterns),
                "universe_history_length": len(self.universe_state_history),
                "qast_cycles_completed": len(self.qast_history),
                "self_reflection_active": self.self_reflection_enabled,
                "entropy_std_scaled": entropy_std_scaled,
                "delta_coherence_scaled": delta_coherence_scaled,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "health_score": float(
                    (
                        avg_coherence
                        + entropy_stability
                        + (1.0 - entropy_std_scaled)
                        + delta_coherence_scaled
                    )
                    / 4.0
                ),
            }

            # Adicionar métricas do universo se fornecidas
            if latest_metrics:
                health_summary["latest_universe_metrics"] = latest_metrics

            qconsciousness_logger.info(
                f"Sistema de saúde avaliado: {system_health}, score: {health_summary['health_score']:.3f}"
            )
            return health_summary

        except (ValueError, TypeError) as e:
            qconsciousness_logger.warning(
                "Falha de validação ao gerar resumo de saúde do sistema com %d estados no histórico e %d métricas de entropia: %s",
                len(self.universe_state_history),
                len(self.metrics.get("quantum_entropy", [])),
                e,
            )
            return {
                "system_health": "error",
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "health_score": 0.0,
            }
        except RuntimeError as e:
            qconsciousness_logger.error(
                "Erro de execução ao gerar resumo de saúde do sistema com %d estados no histórico e %d métricas de entropia: %s",
                len(self.universe_state_history),
                len(self.metrics.get("quantum_entropy", [])),
                e,
            )
            return {
                "system_health": "error",
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "health_score": 0.0,
            }

    def record_universe_state(self, state: UniverseState) -> None:
        """
        Registra um novo UniverseState no histórico da consciência.
        Isso deve ser chamado após cada ciclo de percepção do universo.
        """
        if not isinstance(state, dict) or not all(
            key in state for key in ["vector", "page_entropy", "timestamp"]
        ):
            qconsciousness_logger.error(
                f"Tentativa de registrar UniverseState inválido: {state}"
            )
            return
        self.universe_state_history.append(state)
        self._cached_entropies.append(float(state["page_entropy"]))
        vec = state["vector"]
        if isinstance(vec, np.ndarray) and vec.size > 0:
            if self._vector_shape is None:
                self._vector_shape = vec.shape
            if vec.shape == self._vector_shape:
                self._cached_vectors.append(vec)
                self._cached_timestamps.append(state["timestamp"])
            else:
                qconsciousness_logger.warning(
                    "Vector shape %s incompatível com %s; ignorando para clustering",
                    vec.shape,
                    self._vector_shape,
                )
        qconsciousness_logger.debug(
            f"TraceID: {state.get('trace_id')} - Novo UniverseState (ts: {state['timestamp']}) adicionado ao histórico. Tamanho do histórico: {len(self.universe_state_history)}"
        )
