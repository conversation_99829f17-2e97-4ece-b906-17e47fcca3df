"""Funções utilitárias para algoritmos de controle clássico."""

from __future__ import annotations

import numpy as np


def _ensure_invertible(matrix: np.ndarray, name: str) -> None:
    """Raise ``LinAlgError`` if ``matrix`` is not invertible."""
    if matrix.shape[0] != matrix.shape[1]:
        raise np.linalg.LinAlgError(f"{name} não é quadrada e não pode ser invertida.")
    if np.linalg.matrix_rank(matrix) < matrix.shape[0]:
        raise np.linalg.LinAlgError(f"{name} não é invertível.")


def compute_discrete_lqr_gain(
    A: np.ndarray,
    B: np.ndarray,
    Q: np.ndarray,
    R: np.ndarray,
    *,
    max_iter: int = 100,
    tol: float = 1e-6,
) -> np.ndarray:
    """Calcula a matriz de ganho LQR em tempo discreto.

    Resolve a equação de Riccati algébrica discretizada por um método
    iterativo e devolve o ganho ótimo ``K``.

    Parameters
    ----------
    A, B : np.ndarray
        Matrizes do sistema com dimensões compatíveis.
    Q, R : np.ndarray
        Matrizes de ponderação para custos de estado e de entrada.
    max_iter : int, optional
        Número máximo de iterações para convergência.
    tol : float, optional
        Tolerância de convergência.

    Returns
    -------
    np.ndarray
        Matriz de ganho ``K`` com formato ``(B.shape[1], A.shape[0])``.
    """
    if A.ndim != 2 or A.shape[0] != A.shape[1]:
        raise ValueError("A deve ser uma matriz quadrada.")
    if B.ndim != 2 or B.shape[0] != A.shape[0]:
        raise ValueError("B deve ter o mesmo número de linhas que A.")
    if Q.shape != A.shape:
        raise ValueError("Q deve ter as mesmas dimensões que A.")
    if R.ndim != 2 or R.shape[0] != R.shape[1] or R.shape[0] != B.shape[1]:
        raise ValueError(
            "R deve ser quadrada com dimensão igual ao número de colunas de B."
        )

    P = Q.copy()
    for _ in range(max_iter):
        M = R + B.T @ P @ B
        _ensure_invertible(M, "R + B.T @ P @ B")
        P_next = A.T @ P @ A - A.T @ P @ B @ np.linalg.inv(M) @ B.T @ P @ A + Q
        if np.allclose(P, P_next, atol=tol):
            P = P_next
            break
        P = P_next
    M = R + B.T @ P @ B
    _ensure_invertible(M, "R + B.T @ P @ B")
    K = np.linalg.inv(M) @ B.T @ P @ A
    return K


class LQRController:
    """Controlador LQR discreto simples."""

    def __init__(
        self,
        A: np.ndarray,
        B: np.ndarray,
        Q: np.ndarray,
        R: np.ndarray,
        *,
        K: np.ndarray | None = None,
        max_iter: int = 100,
        tol: float = 1e-6,
    ) -> None:
        self.A = np.asarray(A, dtype=float)
        self.B = np.asarray(B, dtype=float)
        self.Q = np.asarray(Q, dtype=float)
        self.R = np.asarray(R, dtype=float)
        self.max_iter = max_iter
        self.tol = tol
        if K is None:
            self.update_gain()
        else:
            self.K = np.asarray(K, dtype=float)

    def update_gain(self) -> np.ndarray:
        """Recalcula ``K`` usando as matrizes atuais."""
        self.K = compute_discrete_lqr_gain(
            self.A, self.B, self.Q, self.R, max_iter=self.max_iter, tol=self.tol
        )
        return self.K

    def set_matrices(
        self,
        *,
        A: np.ndarray | None = None,
        B: np.ndarray | None = None,
        Q: np.ndarray | None = None,
        R: np.ndarray | None = None,
    ) -> None:
        """Atualiza as matrizes do sistema e recalcula ``K``."""
        if A is not None:
            self.A = np.asarray(A, dtype=float)
        if B is not None:
            self.B = np.asarray(B, dtype=float)
        if Q is not None:
            self.Q = np.asarray(Q, dtype=float)
        if R is not None:
            self.R = np.asarray(R, dtype=float)
        self.update_gain()

    def control(self, x_k: np.ndarray) -> np.ndarray:
        """Calcula a ação de controle ``u_k`` para o estado ``x_k``."""
        x_k = np.asarray(x_k, dtype=float).reshape(-1)
        return -self.K @ x_k


__all__ = ["compute_discrete_lqr_gain", "LQRController"]
