"""Emergence Operator - Complex pattern emergence and recognition.

Implements emergent behavior detection in market dynamics.

"""

import numpy as np
from typing import List, Dict, Any, Tuple, Optional, Set
from dataclasses import dataclass
import logging
from collections import deque
import os
from itertools import combinations
import sklearn.metrics

from qualia.config import config

logger = logging.getLogger(__name__)


@dataclass
class EmergentPattern:
    """Represents an emergent pattern in market data"""

    pattern_id: str
    complexity: float
    persistence: float
    influence: float
    components: List[int]
    emergence_time: float
    last_seen: float


@dataclass
class EmergenceState:
    """State representation for emergence operations"""

    active_patterns: List[EmergentPattern]
    complexity_measure: float
    emergence_threshold: float
    pattern_interactions: Dict[str, List[str]]
    timestamp: float


class EmergenceOperator:
    """
    Quantum-inspired emergence operator for pattern recognition

    Detects emergent patterns that arise from the interaction of
    simpler components, focusing on complexity thresholds and
    pattern persistence.
    """

    def __init__(self, config: Dict[str, Any], *, history_maxlen: int = 1000):
        self.complexity_threshold = config.get("complexity_threshold", 0.75)
        self.pattern_recognition_depth = config.get("pattern_recognition_depth", 5)
        self.feedback_strength = config.get("feedback_strength", 0.5)
        self.mean_window = config.get("mean_window")

        # Pattern tracking
        self.current_state: Optional[EmergenceState] = None
        self.emergence_history: List[EmergenceState] = []
        self.history_maxlen = int(history_maxlen)
        self.pattern_registry: Dict[str, EmergentPattern] = {}
        self.component_buffer = deque(maxlen=100)  # Store recent components
        self.pattern_history: List[List[str]] = []
        self.interaction_history: List[Dict[str, List[str]]] = []

        # Pattern analysis parameters
        self.min_pattern_persistence = 5
        self.max_patterns = 50
        self.interaction_threshold = 0.3

        logger.info(
            f"EmergenceOperator initialized with complexity threshold {self.complexity_threshold}"
        )

    async def detect_emergence(
        self, components: np.ndarray, timestamp: float
    ) -> EmergenceState:
        """Detect emergent patterns in ``components``.

        Parameters
        ----------
        components : np.ndarray
            Array of system components or features to analyse.
        timestamp : float
            Timestamp associated with the current observation.

        Returns
        -------
        EmergenceState
            State capturing the patterns detected at ``timestamp``.
        """
        try:
            # Store components in buffer
            self.component_buffer.append((components.copy(), timestamp))

            # Need sufficient history for pattern detection
            if len(self.component_buffer) < self.pattern_recognition_depth:
                return self._create_initial_state(timestamp)

            # Analyze patterns in recent history
            active_patterns = await self._analyze_patterns(timestamp)

            # Calculate overall complexity
            complexity_measure = self._calculate_system_complexity(components)

            # Detect pattern interactions
            pattern_interactions = self._detect_pattern_interactions(active_patterns)

            # Update pattern registry
            self._update_pattern_registry(active_patterns, timestamp)

            # Create emergence state
            state = EmergenceState(
                active_patterns=active_patterns,
                complexity_measure=complexity_measure,
                emergence_threshold=self.complexity_threshold,
                pattern_interactions=pattern_interactions,
                timestamp=timestamp,
            )

            self.current_state = state
            self.emergence_history.append(state)
            self.pattern_history.append([p.pattern_id for p in active_patterns])
            self.interaction_history.append(pattern_interactions)

            # Limit history size
            if len(self.emergence_history) > self.history_maxlen:
                self.emergence_history.pop(0)
            if len(self.pattern_history) > self.history_maxlen:
                self.pattern_history.pop(0)
            if len(self.interaction_history) > self.history_maxlen:
                self.interaction_history.pop(0)

            logger.debug(
                f"Emergence detection complete: {len(active_patterns)} patterns, "
                f"complexity={complexity_measure:.3f}"
            )

            if any(pattern_interactions.values()):
                logger.debug(f"Pattern interactions recorded: {pattern_interactions}")

            return state

        except Exception as e:
            logger.error(f"Error in emergence detection: {e}")
            return self._create_initial_state(timestamp)

    async def _analyze_patterns(self, timestamp: float) -> List[EmergentPattern]:
        """Analyze recent components for emergent patterns"""
        active_patterns = []

        try:
            # Extract component sequences
            component_sequence = [comp for comp, _ in self.component_buffer]
            time_sequence = [ts for _, ts in self.component_buffer]

            if len(component_sequence) < self.pattern_recognition_depth:
                return active_patterns

            # Analyze different window sizes
            for window_size in range(
                self.pattern_recognition_depth, min(len(component_sequence) + 1, 20)
            ):
                patterns = self._find_patterns_in_window(
                    component_sequence[-window_size:],
                    time_sequence[-window_size:],
                    timestamp,
                )
                active_patterns.extend(patterns)

            # Filter and rank patterns
            active_patterns = self._filter_and_rank_patterns(active_patterns)

            return active_patterns[: self.max_patterns]

        except Exception as e:
            logger.warning(f"Pattern analysis failed: {e}")
            return []

    def _find_patterns_in_window(
        self, components: List[np.ndarray], timestamps: List[float], current_time: float
    ) -> List[EmergentPattern]:
        """Find emergent patterns within a specific window"""
        patterns = []

        try:
            window_size = len(components)
            if window_size < 3:
                return patterns

            # Convert to numpy array for analysis
            component_matrix = np.array(components)

            # Find recurring subsequences
            for subseq_len in range(3, min(window_size // 2 + 1, 8)):
                subpatterns = self._find_subsequence_patterns(
                    component_matrix, subseq_len, timestamps, current_time
                )
                patterns.extend(subpatterns)

            # Find correlation patterns
            correlation_patterns = self._find_correlation_patterns(
                component_matrix, timestamps, current_time
            )
            patterns.extend(correlation_patterns)

            # Find emergence cascades
            cascade_patterns = self._find_cascade_patterns(
                component_matrix, timestamps, current_time
            )
            patterns.extend(cascade_patterns)

            return patterns

        except Exception as e:
            logger.warning(f"Window pattern search failed: {e}")
            return []

    def _find_subsequence_patterns(
        self,
        components: np.ndarray,
        subseq_len: int,
        timestamps: List[float],
        current_time: float,
    ) -> List[EmergentPattern]:
        """Find recurring subsequence patterns"""
        patterns = []

        try:
            if len(components) < subseq_len * 2:
                return patterns

            n = len(components)
            for pos1 in range(0, n - subseq_len + 1):
                seq1 = components[pos1 : pos1 + subseq_len]
                seq1_flat = seq1.reshape(-1).astype(float)
                seq1_flat = (seq1_flat - seq1_flat.mean()) / (seq1_flat.std() + 1e-8)

                for pos2 in range(pos1 + subseq_len, n - subseq_len + 1):
                    seq2 = components[pos2 : pos2 + subseq_len]
                    seq2_flat = seq2.reshape(-1).astype(float)
                    seq2_flat = (seq2_flat - seq2_flat.mean()) / (
                        seq2_flat.std() + 1e-8
                    )

                    denom = np.linalg.norm(seq1_flat) * np.linalg.norm(seq2_flat) + 1e-8
                    similarity = float(np.dot(seq1_flat, seq2_flat) / denom)
                    if similarity > 0.8:
                        complexity = self._calculate_pattern_complexity(seq1, seq2)
                        if complexity > self.complexity_threshold:
                            pattern = EmergentPattern(
                                pattern_id=f"subseq_{len(patterns)}_{current_time:.0f}",
                                complexity=complexity,
                                persistence=1.0,
                                influence=similarity * complexity,
                                components=[pos1, pos2],
                                emergence_time=timestamps[pos1],
                                last_seen=current_time,
                            )
                            patterns.append(pattern)

            return patterns

        except Exception as e:
            logger.warning(f"Subsequence pattern search failed: {e}")
            return []

    def _find_correlation_patterns(
        self, components: np.ndarray, timestamps: List[float], current_time: float
    ) -> List[EmergentPattern]:
        """Find correlation-based emergent patterns"""
        patterns = []

        try:
            if len(components) < 5:
                return patterns

            if components.shape[1] > 1:
                corr_matrix = np.corrcoef(components.T)
                idx_i, idx_j = np.triu_indices_from(corr_matrix, k=1)
                correlations = np.abs(corr_matrix[idx_i, idx_j])
                valid_mask = (correlations > 0.7) & ~np.isnan(correlations)
                for i, j, corr in zip(
                    idx_i[valid_mask], idx_j[valid_mask], correlations[valid_mask]
                ):
                    complexity = corr * self._calculate_feature_complexity(
                        components[:, i], components[:, j]
                    )
                    if complexity > self.complexity_threshold:
                        pattern = EmergentPattern(
                            pattern_id=f"corr_{i}_{j}_{current_time:.0f}",
                            complexity=complexity,
                            persistence=1.0,
                            influence=corr,
                            components=[i, j],
                            emergence_time=timestamps[0],
                            last_seen=current_time,
                        )
                        patterns.append(pattern)

            return patterns

        except Exception as e:
            logger.warning(f"Correlation pattern search failed: {e}")
            return []

    def _find_cascade_patterns(
        self, components: np.ndarray, timestamps: List[float], current_time: float
    ) -> List[EmergentPattern]:
        """Find cascade emergence patterns"""
        patterns = []

        try:
            if len(components) < 5:
                return patterns

            for idx in range(len(components) - 3):
                window = components[idx : idx + 4]
                c_strength = self._detect_cascade_strength(window)
                if c_strength <= 0.6:
                    continue
                complexity = c_strength * self._calculate_temporal_complexity(window)
                if complexity > self.complexity_threshold:
                    pattern = EmergentPattern(
                        pattern_id=f"cascade_{idx}_{current_time:.0f}",
                        complexity=complexity,
                        persistence=1.0,
                        influence=c_strength,
                        components=list(range(idx, idx + 4)),
                        emergence_time=timestamps[idx],
                        last_seen=current_time,
                    )
                    patterns.append(pattern)

            return patterns

        except Exception as e:
            logger.warning(f"Cascade pattern search failed: {e}")
            return []

    def _calculate_sequence_similarity(
        self, seq1: np.ndarray, seq2: np.ndarray
    ) -> float:
        """Calculate similarity between two sequences"""
        try:
            # Normalize sequences
            seq1_norm = (seq1 - np.mean(seq1)) / (np.std(seq1) + 1e-8)
            seq2_norm = (seq2 - np.mean(seq2)) / (np.std(seq2) + 1e-8)

            # Calculate correlation
            correlation = np.corrcoef(seq1_norm.flatten(), seq2_norm.flatten())[0, 1]

            if np.isnan(correlation):
                return 0.0

            return abs(correlation)

        except Exception:
            return 0.0

    def _hamming_similarity(self, arr1: np.ndarray, arr2: np.ndarray) -> float:
        """Return normalized Hamming similarity between two arrays."""
        try:
            a = arr1.flatten() > np.mean(arr1)
            b = arr2.flatten() > np.mean(arr2)
            return 1.0 - np.mean(a != b)
        except Exception:
            return 0.0

    def _cosine_similarity(self, arr1: np.ndarray, arr2: np.ndarray) -> float:
        """Return cosine similarity between two arrays."""
        try:
            a = arr1.flatten().reshape(1, -1)
            b = arr2.flatten().reshape(1, -1)
            sim = sklearn.metrics.pairwise.cosine_similarity(a, b)[0, 0]
            if np.isnan(sim):
                return 0.0
            return float(sim)
        except Exception:
            return 0.0

    def _calculate_pattern_complexity(
        self, seq1: np.ndarray, seq2: np.ndarray
    ) -> float:
        """Calculate complexity of a pattern using similarity metrics."""
        try:
            hamming = self._hamming_similarity(seq1, seq2)
            cosine = self._cosine_similarity(seq1, seq2)
            complexity = (hamming + cosine) / 2
            return float(np.clip(complexity, 0.0, 1.0))
        except Exception:
            return 0.0

    def _calculate_feature_complexity(
        self, feature1: np.ndarray, feature2: np.ndarray
    ) -> float:
        """Calculate complexity of feature interaction"""
        try:
            # Calculate mutual information as complexity measure
            hist_2d, _, _ = np.histogram2d(feature1, feature2, bins=5)
            hist_2d = hist_2d / np.sum(hist_2d)

            # Calculate marginal histograms
            hist_1 = np.sum(hist_2d, axis=1)
            hist_2 = np.sum(hist_2d, axis=0)

            prob_matrix = hist_2d
            joint = prob_matrix > 0
            if not np.any(joint):
                return 0.0
            denominator = np.outer(hist_1, hist_2)
            mi = np.sum(
                prob_matrix[joint] * np.log2(prob_matrix[joint] / denominator[joint])
            )
            return float(mi)

        except Exception:
            return 0.0

    def _detect_cascade_strength(self, window: np.ndarray) -> float:
        """Detect strength of cascade pattern in window"""
        try:
            # Calculate activation levels for each time step
            activations = np.mean(window, axis=1)

            # Check for increasing trend
            diffs = np.diff(activations)
            positive_trend = np.mean(diffs > 0)

            # Calculate trend strength
            if len(diffs) > 0:
                trend_strength = np.mean(diffs) / (np.std(activations) + 1e-8)
                trend_strength = max(0, trend_strength)  # Only positive trends
            else:
                trend_strength = 0.0

            # Combine trend indicators
            cascade_strength = positive_trend * min(trend_strength, 1.0)

            return cascade_strength

        except Exception:
            return 0.0

    def _calculate_temporal_complexity(self, window: np.ndarray) -> float:
        """Calculate temporal complexity of a window"""
        try:
            # Calculate temporal gradients
            temporal_grads = np.diff(window, axis=0)

            # Calculate complexity as variance of gradients
            complexity = np.var(temporal_grads)

            # Normalize
            max_complexity = np.var(window)
            if max_complexity > 0:
                complexity = complexity / max_complexity

            return min(complexity, 1.0)

        except Exception:
            return 0.0

    def _calculate_system_complexity(self, components: np.ndarray) -> float:
        """Calculate overall system complexity measure"""
        try:
            if len(components) == 0:
                return 0.0

            # Multiple complexity measures
            # 1. Statistical complexity (entropy)
            flat_components = components.flatten()
            hist, _ = np.histogram(flat_components, bins=20)
            hist = hist / np.sum(hist)
            hist = hist[hist > 0]
            entropy = -np.sum(hist * np.log2(hist)) if len(hist) > 0 else 0.0
            max_entropy = np.log2(20)
            statistical_complexity = entropy / max_entropy if max_entropy > 0 else 0.0

            # 2. Dimensional complexity (effective dimensionality)
            if len(components.shape) > 1 and components.shape[1] > 1:
                try:
                    _, singular_values, _ = np.linalg.svd(components)
                    # Calculate participation ratio
                    participation_ratio = (np.sum(singular_values) ** 2) / np.sum(
                        singular_values**2
                    )
                    dimensional_complexity = participation_ratio / len(singular_values)
                except:
                    dimensional_complexity = 1.0
            else:
                dimensional_complexity = 1.0

            # 3. Temporal complexity (if we have history)
            temporal_complexity = 0.0
            if len(self.component_buffer) > 1:
                recent_components = [
                    comp for comp, _ in list(self.component_buffer)[-5:]
                ]
                if len(recent_components) > 1:
                    temporal_diffs = np.diff(recent_components, axis=0)
                    temporal_complexity = np.mean(np.var(temporal_diffs, axis=0))
                    temporal_complexity = min(temporal_complexity, 1.0)

            # Combine complexity measures
            overall_complexity = (
                statistical_complexity * 0.4
                + dimensional_complexity * 0.4
                + temporal_complexity * 0.2
            )

            return np.clip(overall_complexity, 0.0, 1.0)

        except Exception as e:
            logger.warning(f"System complexity calculation failed: {e}")
            return 0.5

    def _detect_pattern_interactions(
        self, patterns: List[EmergentPattern]
    ) -> Dict[str, List[str]]:
        """Detect interactions between emergent patterns"""
        interactions = {}

        try:
            for i, pattern1 in enumerate(patterns):
                interactions[pattern1.pattern_id] = []

                for j, pattern2 in enumerate(patterns):
                    if i != j:
                        # Check for component overlap
                        overlap = set(pattern1.components) & set(pattern2.components)

                        # Check for temporal proximity
                        time_diff = abs(pattern1.last_seen - pattern2.last_seen)

                        # Check for influence correlation
                        influence_similarity = 1 - abs(
                            pattern1.influence - pattern2.influence
                        )

                        # Determine interaction strength
                        interaction_strength = (
                            (len(overlap) / max(len(pattern1.components), 1)) * 0.4
                            + (1 / (1 + time_diff)) * 0.3
                            + influence_similarity * 0.3
                        )

                        if interaction_strength > self.interaction_threshold:
                            interactions[pattern1.pattern_id].append(
                                pattern2.pattern_id
                            )

            return interactions

        except Exception as e:
            logger.warning(f"Pattern interaction detection failed: {e}")
            return {}

    def _update_pattern_registry(
        self, active_patterns: List[EmergentPattern], timestamp: float
    ):
        """Update the pattern registry with current patterns"""
        try:
            # Update existing patterns and add new ones
            current_pattern_ids = set()

            for pattern in active_patterns:
                current_pattern_ids.add(pattern.pattern_id)

                # Match against existing patterns using component similarity
                matched_id = None
                matched_sim = 0.0
                for pid, existing in self.pattern_registry.items():
                    sim = self._hamming_similarity(
                        np.array(existing.components), np.array(pattern.components)
                    )
                    if sim > 0.8 and sim > matched_sim:
                        matched_id = pid
                        matched_sim = sim

                if matched_id is not None:
                    existing = self.pattern_registry[matched_id]
                    existing.persistence += matched_sim
                    existing.last_seen = timestamp
                    existing.influence = (existing.influence + pattern.influence) / 2
                    existing.complexity = max(existing.complexity, pattern.complexity)
                else:
                    # Add new pattern
                    self.pattern_registry[pattern.pattern_id] = pattern

            # Decay patterns not seen recently
            patterns_to_remove = []
            for pattern_id, pattern in self.pattern_registry.items():
                if pattern_id not in current_pattern_ids:
                    if timestamp - pattern.last_seen > 300:  # 5 minutes
                        patterns_to_remove.append(pattern_id)
                    else:
                        pattern.persistence *= 0.95  # Decay persistence

            # Remove old patterns
            for pattern_id in patterns_to_remove:
                del self.pattern_registry[pattern_id]

        except Exception as e:
            logger.warning(f"Pattern registry update failed: {e}")

    def _filter_and_rank_patterns(
        self, patterns: List[EmergentPattern]
    ) -> List[EmergentPattern]:
        """Filter and rank patterns by significance"""
        try:
            # Filter by complexity threshold
            filtered_patterns = [
                p for p in patterns if p.complexity >= self.complexity_threshold
            ]

            # Calculate significance score
            for pattern in filtered_patterns:
                pattern.influence = (
                    pattern.complexity * 0.5
                    + pattern.influence * 0.3
                    + min(pattern.persistence / 10, 1.0) * 0.2
                )

            # Sort by significance
            filtered_patterns.sort(key=lambda p: p.influence, reverse=True)

            return filtered_patterns

        except Exception as e:
            logger.warning(f"Pattern filtering failed: {e}")
            return patterns

    def _create_initial_state(self, timestamp: float) -> EmergenceState:
        """Create initial empty emergence state"""
        return EmergenceState(
            active_patterns=[],
            complexity_measure=0.0,
            emergence_threshold=self.complexity_threshold,
            pattern_interactions={},
            timestamp=timestamp,
        )

    def is_emergent(self) -> bool:
        """Check if system is currently showing emergent behavior"""
        if self.current_state is None:
            return False

        return (
            self.current_state.complexity_measure >= self.complexity_threshold
            and len(self.current_state.active_patterns) > 0
        )

    def calculate_interaction_density(self) -> float:
        """Return ratio of interacting pattern pairs to total pairs."""
        if self.current_state is None or len(self.current_state.active_patterns) < 2:
            return 0.0
        n = len(self.current_state.active_patterns)
        total_pairs = n * (n - 1) / 2
        interactions = sum(
            len(v) for v in self.current_state.pattern_interactions.values()
        )
        return float(interactions / max(total_pairs, 1))

    def get_emergence_summary(self) -> Dict[str, Any]:
        """Get summary of current emergence state"""
        if self.current_state is None:
            return {}

        return {
            "is_emergent": self.is_emergent(),
            "complexity": float(self.current_state.complexity_measure),
            "active_patterns": len(self.current_state.active_patterns),
            "pattern_interactions": len(self.current_state.pattern_interactions),
            "total_registered_patterns": len(self.pattern_registry),
            "timestamp": self.current_state.timestamp,
        }

    def calculate_entropy(self) -> float:
        """Return normalized entropy of active pattern complexities."""

        if self.current_state is None or not self.current_state.active_patterns:
            return 0.0

        complexities = np.array(
            [p.complexity for p in self.current_state.active_patterns], dtype=float
        )
        probs = complexities / (np.sum(complexities) + 1e-12)
        probs = probs[probs > 0]
        ent = float(np.sum(-probs * np.log(probs)))
        max_ent = np.log(probs.size)
        return ent / max_ent if max_ent > 0 else 0.0

    def get_state_dict(self) -> Dict[str, Any]:
        """Get serializable state dictionary"""
        return {
            "complexity_threshold": self.complexity_threshold,
            "pattern_recognition_depth": self.pattern_recognition_depth,
            "feedback_strength": self.feedback_strength,
            "current_complexity": (
                float(self.current_state.complexity_measure)
                if self.current_state
                else 0.0
            ),
            "active_patterns": (
                len(self.current_state.active_patterns) if self.current_state else 0
            ),
            "registered_patterns": len(self.pattern_registry),
            "history_length": len(self.emergence_history),
        }

    def average_pattern_persistence(self) -> float:
        """Return average persistence value of registered patterns.

        Returns
        -------
        float
            Mean persistence stored in :attr:`pattern_registry` or ``0.0`` when
            empty.

        Examples
        --------
        >>> op = EmergenceOperator({})
        >>> op.pattern_registry = {}
        >>> op.average_pattern_persistence()
        0.0
        """

        if not self.pattern_registry:
            return 0.0

        values = [p.persistence for p in self.pattern_registry.values()]
        return float(np.mean(values))


def apply_emergence(
    field: np.ndarray,
    beta: float | None = 0.1,
    *,
    baseline: np.ndarray | float | None = None,
) -> np.ndarray:
    """Simple emergence amplification used in tests.

    Parameters
    ----------
    field
        Input array representing the field.
    beta
        Amplification coefficient. ``None`` uses ``QUALIA_EMERGENCE_BETA``.
    baseline
        Optional baseline array or scalar used in place of the field mean.
    """

    if not isinstance(field, np.ndarray):
        raise TypeError("field must be a numpy array")

    if beta is None:
        beta = config.emergence_beta
    elif not isinstance(beta, (int, float)):
        raise TypeError("beta must be a float")

    if baseline is None:
        baseline_val = float(np.mean(field))
    elif isinstance(baseline, np.ndarray):
        baseline_val = float(np.mean(baseline))
    elif isinstance(baseline, (int, float)):
        baseline_val = float(baseline)
    else:
        raise TypeError("baseline must be a numpy array or float")

    return field.astype(float, copy=False) + beta * (field - baseline_val)
