"""Information integration operator.

Provides :class:`IntegrationOperator` which approximates the
emergence of conscious experience from correlations among states as
described in the MICQI specifications.

Examples
--------
>>> op = IntegrationOperator({"alpha": 1.0, "beta": 1.0, "gamma": 0.5})
>>> await op.integrate_information(np.eye(2), timestamp=0.0)
"""

from __future__ import annotations

from dataclasses import dataclass
import logging
from typing import List, Any

import numpy as np

logger = logging.getLogger(__name__)


@dataclass
class IntegrationState:
    """Outcome of information integration."""

    integration_measure: float
    experience_intensity: float
    timestamp: float


class IntegrationOperator:
    """Operator implementing Ô_II and Ô_EC concepts."""

    def __init__(
        self, config: dict | None = None, *, history_maxlen: int = 1000
    ) -> None:
        cfg = config or {}
        self.alpha = float(cfg.get("alpha", 1.0))
        self.beta = float(cfg.get("beta", 1.0))
        self.gamma = float(cfg.get("gamma", 1.0))
        self._current_state: IntegrationState | None = None
        self.integration_history: List[IntegrationState] = []
        self.history_maxlen = int(history_maxlen)

    # ------------------------------------------------------------------
    def _effective_complexity(self, corr: np.ndarray) -> float:
        vals = np.linalg.eigvalsh(corr)
        vals = np.clip(vals.real, 0.0, None)
        if vals.sum() == 0:
            return 0.0
        probs = vals / vals.sum()
        probs = probs[probs > 0]
        if probs.size <= 1:
            return 0.0
        ent = -float(np.sum(probs * np.log(probs)))
        return ent / np.log(len(probs))

    async def integrate_information(
        self, glq_states: np.ndarray, timestamp: float
    ) -> IntegrationState:
        """Integrate correlations among ``glq_states``."""
        arr = np.atleast_2d(glq_states.astype(float))
        corr = np.corrcoef(arr)
        corr[np.isnan(corr)] = 0.0
        base_integration = float(np.mean(np.abs(corr)))
        complexity = self._effective_complexity(corr)
        experience = (
            self.alpha * base_integration
            + self.beta * complexity
            + self.gamma * (base_integration * complexity)
        )
        state = IntegrationState(base_integration, experience, timestamp)
        self._current_state = state
        self.integration_history.append(state)
        if len(self.integration_history) > self.history_maxlen:
            self.integration_history.pop(0)
        return state

    def integration_level(self) -> float:
        return self._current_state.experience_intensity if self._current_state else 0.0

    def get_state_dict(self) -> dict[str, Any]:
        """Return a serializable representation of the operator state."""
        return {
            "alpha": self.alpha,
            "beta": self.beta,
            "gamma": self.gamma,
            "history_length": len(self.integration_history),
            "current_intensity": (
                self._current_state.experience_intensity if self._current_state else 0.0
            ),
        }
