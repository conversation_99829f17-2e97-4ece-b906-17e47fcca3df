"""Utilities for exporting QUALIA circuits as QASM files.

This module centralizes helper functions used to persist quantum
circuits built by :class:`~qualia.core.universe.QUALIAQuantumUniverse`
in the OpenQASM 3 format. It also exposes a convenience function for
executing circuits on real hardware with readout-error mitigation.

Functions
---------
export_qasm3
    Gera o circuito do universo e o grava em um arquivo ``.qasm3``.
run_on_hardware
    Executa um ``QuantumCircuit`` em hardware real utilizando
    mitigação de leitura.
"""

from __future__ import annotations

from ..utils.logger import get_logger
from typing import Any, Dict, Optional, TYPE_CHECKING

try:
    from qiskit import qasm3
except Exception:  # pragma: no cover - optional
    qasm3 = None
from qiskit.circuit import QuantumCircuit

if TYPE_CHECKING:  # pragma: no cover - for type checking only
    from .universe import QUALIAQuantumUniverse

logger = get_logger(__name__)


def export_qasm3(
    universe: "QUALIAQuantumUniverse",
    filename: str = "qualia_circuit.qasm3",
    steps_to_export: Optional[int] = None,
) -> bool:
    """Exporta o circuito do universo para um arquivo QASM 3."""
    if qasm3 is None:  # lazily importa o módulo se ausente
        try:  # pragma: no cover - dependente de ambiente
            import importlib
            import sys

            if (
                "qiskit" in sys.modules
                and getattr(sys.modules["qiskit"], "__spec__", None) is None
            ):
                del sys.modules["qiskit"]

            from qiskit import qasm3 as _qasm3

            globals()["qasm3"] = _qasm3
        except Exception as exc:  # pragma: no cover - difficult to trigger
            logger.error(f"Falha ao importar qasm3: {exc}")
            return False
    if steps_to_export is None:
        if (
            hasattr(universe, "_last_run_steps")
            and universe._last_run_steps is not None
        ):
            steps_to_build = universe._last_run_steps
        else:
            logger.warning(
                "Número de steps para exportar não especificado e não há histórico. Exportando circuito base."
            )
            steps_to_build = 0
    else:
        steps_to_build = steps_to_export

    build_kwargs = {
        "thermal": getattr(universe, "thermal_active_on_last_run", False),
        "temperature": getattr(universe, "temperature_on_last_run", 0.01),
        "retro_mode": getattr(universe, "retro_mode_on_last_run", "none"),
        "measure_frequency": getattr(universe, "measure_frequency_on_last_run", 1),
        "retro_strength": getattr(
            universe,
            "retro_strength_on_last_run",
            getattr(universe, "retro_strength", 0.0),
        ),
    }

    if "scr_feedback_factor" in build_kwargs:
        del build_kwargs["scr_feedback_factor"]

    build_kwargs = {k: v for k, v in build_kwargs.items() if v is not None}

    try:
        temp_qc = universe.build_circuit(steps=steps_to_build, **build_kwargs)
        universe._built_circuit = temp_qc

        with open(filename, "w") as f:
            f.write("OPENQASM 3.0\n")
            f.write('include "qelib1.inc";\n')
            f.write(qasm3.dumps(temp_qc))

        logger.info(f"Circuito exportado para {filename}")
        return True
    except Exception as exc:  # pragma: no cover - difficult to trigger
        logger.error(f"Falha ao exportar para QASM3: {exc}")
        return False


def run_on_hardware(
    circuit: QuantumCircuit, backend: Any, shots: int = 1024
) -> Dict[str, int]:
    """Execute um circuito em hardware real com mitigação de leitura.

    O circuito é transpilado para o ``backend`` com ``optimization_level=3`` e
    as contagens retornadas são corrigidas utilizando ``CompleteMeasFitter``.

    Parameters
    ----------
    circuit:
        Circuito quântico a ser executado.
    backend:
        Backend de hardware real onde o circuito será enviado.
    shots:
        Número de execuções (shots) do circuito.

    Returns
    -------
    Dict[str, int]
        Contagens mitigadas do circuito executado.
    """

    from qiskit.compiler import transpile
    from qiskit.ignis.mitigation.measurement import (
        CompleteMeasFitter,
        complete_meas_cal,
    )

    logger.info(
        "Transpilando circuito para %s com optimization_level=3.",
        getattr(backend, "name", "backend"),
    )
    transpiled_circuit = transpile(circuit, backend=backend, optimization_level=3)

    qubits = list(range(transpiled_circuit.num_qubits))
    cal_circuits, state_labels = complete_meas_cal(qubit_list=qubits, circlabel="mcal")
    cal_circuits = transpile(cal_circuits, backend=backend, optimization_level=3)
    cal_results = backend.run(cal_circuits, shots=shots).result()
    meas_fitter = CompleteMeasFitter(cal_results, state_labels, circlabel="mcal")

    result = backend.run(transpiled_circuit, shots=shots).result()
    raw_counts = result.get_counts()
    mitigated_counts = meas_fitter.filter.apply(raw_counts)

    return mitigated_counts
