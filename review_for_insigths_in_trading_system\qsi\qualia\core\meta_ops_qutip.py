from __future__ import annotations

"""Advanced Meta-Qualia operators implemented with QuTiP and Dask."""

from dataclasses import dataclass
import logging
from typing import Iterable, List

import numpy as np

from qutip import Qobj, qeye

try:  # pragma: no cover - optional
    import dask
    from dask import delayed
except Exception:  # pragma: no cover - fallback
    dask = None
    delayed = None

from ..utils.numba_utils import optional_njit

logger = logging.getLogger(__name__)


@optional_njit(cache=True)
def _probability(amplitude: complex) -> float:
    return float(np.abs(amplitude) ** 2)


@dataclass
class OperatorHistory:
    """Simple history tracker for NNN operator."""

    states: List[Qobj]
    maxlen: int = 10

    def append(self, state: Qobj) -> None:
        self.states.append(state)
        if len(self.states) > self.maxlen:
            self.states.pop(0)

    def as_density(self) -> Qobj:
        if not self.states:
            return qeye(1)
        dm = sum(ket.proj() for ket in self.states) / len(self.states)
        return dm


def apply_CCC(
    state: Qobj, measurements: Iterable[Qobj] | None = None, *, parallel: bool = False
) -> Qobj:
    """Collapse ``state`` using ``measurements`` operators."""
    if measurements is None:
        measurements = [qeye(state.dims[0])]

    measurements = list(measurements)
    probs: List[float] = []

    if parallel and dask is not None:
        tasks = [
            delayed(lambda op: float((op * state).norm() ** 2))(m) for m in measurements
        ]
        probs = list(dask.compute(*tasks))
    else:
        for m in measurements:
            probs.append(float((m * state).norm() ** 2))

    if not probs:
        return state

    norm_probs = np.array(probs, dtype=float)
    norm_probs = norm_probs / norm_probs.sum()
    idx = int(np.random.choice(len(measurements), p=norm_probs))
    result = measurements[idx] * state
    return result.unit()


def apply_DDD(state: Qobj, rate: float = 0.05) -> Qobj:
    """Apply simple exponential decoherence."""
    dm = state.proj()
    return (np.exp(-rate) * dm).unit()


def apply_OOO(state: Qobj, observer_matrix: Qobj, step: int, lr: float = 0.1) -> Qobj:
    """Observer feedback adjusting state based on overlap."""
    overlap = (state.dag() * observer_matrix * state).tr().real
    factor = 1 + lr * overlap * step
    return (factor * state).unit()


def apply_TTT(state: Qobj, expansion: int = 2) -> Qobj:
    """Simulate transcendence by expanding and projecting back."""
    extended = state.tensor(qeye(expansion))
    return extended.ptrace(list(range(state.dims[0])))


def apply_RRR(state: Qobj, buffer: List[Qobj], capacity: int = 3) -> Qobj:
    """Delay evolution using a simple buffer."""
    buffer.append(state)
    if len(buffer) <= capacity:
        return state
    return buffer.pop(0)


def apply_AAA(state: Qobj, factor: float = 1.5) -> Qobj:
    """Accelerate evolution by scaling amplitudes."""
    return (factor * state).unit()


def apply_ZZZ(state: Qobj, future_state: Qobj, weight: float = 0.5) -> Qobj:
    """Mix ``state`` with ``future_state`` to model retrocausality."""
    return ((1 - weight) * state + weight * future_state).unit()


def apply_NNN(state: Qobj, history: OperatorHistory) -> Qobj:
    """Influence ``state`` using narrative coherence from ``history``."""
    history.append(state)
    dm = history.as_density()
    mixed = state + dm * state
    return mixed.unit()


def apply_XXX(state: Qobj, external: Qobj) -> Qobj:
    """Synchronize ``state`` with ``external`` signal."""
    return ((state + external) / 2.0).unit()


def qobj_to_qiskit(
    operator: Qobj,
) -> "QuantumCircuit":  # pragma: no cover - convenience
    from qiskit import QuantumCircuit

    dim = operator.shape[0]
    n_qubits = int(np.log2(dim))
    qc = QuantumCircuit(n_qubits)
    try:  # Handle minimal environments
        from qiskit.quantum_info import Operator as QiskitOperator

        qc.unitary(QiskitOperator(operator.full()), range(n_qubits))
    except Exception:  # pragma: no cover - graceful fallback
        pass
    return qc


def qobj_to_cirq(operator: Qobj) -> "cirq.Gate":  # pragma: no cover - convenience
    import cirq

    return cirq.MatrixGate(operator.full())
