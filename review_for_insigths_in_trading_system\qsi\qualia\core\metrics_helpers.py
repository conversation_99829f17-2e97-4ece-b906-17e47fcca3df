"""Cálculo de métricas quânticas auxiliares.

Este módulo contém utilitários para mensurar entropia, coerência e
outras métricas que auxiliam o QUALIA a avaliar estados e circuitos
quânticos.
"""

from __future__ import annotations

from typing import Any, Dict, List, Optional, Tuple, Sequence

import numpy as np
from qiskit.quantum_info import Statevector

from ..utils.logger import get_logger
from ..utils.quantum_utils import sv_entropy
from .metrics import (
    calculate_coherence,
    calculate_entanglement,
    calculate_von_neumann_entropy,
    calculate_symbolic_coherence,
)

logger = get_logger(__name__)


def _calculate_symbolic_entropy_metrics(
    universe: "QUALIAQuantumUniverse",
    expr: "sympy.Expr",
    x_symbol: "sympy.Symbol",
    qast_metrics: Dict[str, Any],
) -> Tuple[float, float, Dict[str, Any]]:
    """Calcula entropias simb\xc3\xb3licas e atualiza ``qast_metrics``."""

    poly_coeffs = (
        expr.as_poly(x_symbol).all_coeffs()
        if x_symbol in expr.free_symbols
        else [expr.evalf() if expr.is_Number else 0]
    )
    abs_coeffs = np.abs(np.asarray(poly_coeffs, dtype=float))
    sum_abs_coeffs = float(abs_coeffs.sum())

    if sum_abs_coeffs > 1e-9:
        probs = abs_coeffs / sum_abs_coeffs
        valid_probs = probs[probs > 1e-9]
        H_symb = float(-np.sum(valid_probs * np.log2(valid_probs)))
    else:
        H_symb = 0.0

    degree = expr.as_poly(x_symbol).degree() if x_symbol in expr.free_symbols else 0
    H_symb_prime = 0.0
    if degree > 0:
        log_deg_plus_1 = np.log2(degree + 1)
        if log_deg_plus_1 > 1e-9:
            H_symb_prime = H_symb / log_deg_plus_1
        else:
            H_symb_prime = H_symb if degree == 1 else 0.0
    elif degree == 0:
        H_symb_prime = 0.0
    H_symb_prime = np.clip(H_symb_prime, 0.0, 1.0)

    qast_metrics["last_symbolic_entropy_raw"] = H_symb
    qast_metrics["last_symbolic_entropy_normalized"] = H_symb_prime

    prev_H_symb_prime_for_delta = qast_metrics.get(
        "avg_normalized_symbolic_entropy", H_symb_prime
    )
    delta_H_symb_prime = H_symb_prime - prev_H_symb_prime_for_delta
    qast_metrics["delta_normalized_symbolic_entropy"] = delta_H_symb_prime

    if "normalized_symbolic_entropy_history" not in qast_metrics or not isinstance(
        qast_metrics["normalized_symbolic_entropy_history"], list
    ):
        qast_metrics["normalized_symbolic_entropy_history"] = []

    norm_history: List[float] = qast_metrics["normalized_symbolic_entropy_history"]
    norm_history.append(H_symb_prime)
    if len(norm_history) > getattr(universe, "max_history_size", 100) // 5:
        norm_history.pop(0)

    current_avg_norm_entropy = np.mean(norm_history) if norm_history else H_symb_prime
    qast_metrics["avg_normalized_symbolic_entropy"] = current_avg_norm_entropy

    return H_symb_prime, delta_H_symb_prime, qast_metrics


def compute_quantum_metrics(
    universe: "QUALIAQuantumUniverse", state: "StateLike"
) -> Dict[str, float]:
    """Compute entropy and coherence and store in :attr:`metrics`."""

    from qiskit.quantum_info import Statevector as _Statevector

    sv = state if isinstance(state, _Statevector) else _Statevector(state)

    entropy_val = sv_entropy(sv)
    coherence_val = calculate_coherence(sv)
    vn_entropy_val = calculate_von_neumann_entropy(sv)
    entanglement_val = calculate_entanglement(sv)

    if hasattr(universe, "metrics") and hasattr(universe.metrics, "add_entry"):
        universe.metrics.add_entry("quantum_entropy", entropy_val)
        universe.metrics.add_entry("quantum_coherence", coherence_val)
        universe.metrics.add_entry("von_neumann_entropy", vn_entropy_val)
        universe.metrics.add_entry("quantum_entanglement", entanglement_val)

    return {
        "entropy": entropy_val,
        "coherence": coherence_val,
        "entanglement": entanglement_val,
        "von_neumann_entropy": vn_entropy_val,
    }


def symbolic_coherence_from_tokens(entropy: float, tokens: Sequence[str]) -> float:
    """Wrapper para ``calculate_symbolic_coherence`` usando lista de tokens."""

    return calculate_symbolic_coherence(entropy, tokens)


def compute_symbolic_coherence(
    universe: "QUALIAQuantumUniverse", entropy: float, tokens: Sequence[str]
) -> float:
    """Calcula coerência simbólica e registra na instância de métricas."""

    value = symbolic_coherence_from_tokens(entropy, tokens)
    if hasattr(universe, "metrics") and hasattr(universe.metrics, "add_entry"):
        universe.metrics.add_entry("symbolic_coherence", value)
    return value


def _calculate_post_run_metrics(
    universe: "QUALIAQuantumUniverse",
    statevector: Optional[Statevector],
    counts: Optional[Dict[str, int]],
) -> Dict[str, Any]:
    """Calcula m\xc3\xa9tricas p\xc3\xb3s-execu\xc3\xa7\xc3\xa3o."""

    logger.debug(
        f"_calculate_post_run_metrics chamada com statevector: {'present' if statevector is not None else 'None'}, counts: {'present' if counts is not None else 'None'}"
    )

    metrics: Dict[str, Any] = {
        "fidelity_to_initial": None,
        "otoc_calculated": None,
        "execution_time": getattr(universe, "_last_execution_time", 0.0),
        "final_entropy_from_counts": None,
        "sv_entropy": None,
        "linear_entropy": None,
    }

    if statevector is not None and getattr(universe, "current_sv", None) is not None:
        try:
            sv_final = (
                statevector
                if isinstance(statevector, Statevector)
                else Statevector(statevector)
            )
            sv_initial = (
                universe.current_sv
                if isinstance(universe.current_sv, Statevector)
                else Statevector(universe.current_sv)
            )
            if sv_final.dim == sv_initial.dim:
                dot_product = np.abs(np.dot(sv_initial.data.conj(), sv_final.data)) ** 2
                metrics["fidelity_to_initial"] = float(dot_product)
            else:
                logger.warning(
                    "Dimens\xc3\xb5es do statevector inicial (%s) e final (%s) n\xc3\xa3o coincidem.",
                    sv_initial.dim,
                    sv_final.dim,
                )
        except Exception as e:
            logger.error(
                "Erro ao calcular a fidelidade ao estado inicial: %s", e, exc_info=True
            )
    else:
        logger.debug(
            "Statevector final ou inicial n\xc3\xa3o dispon\xc3\xadvel. Fidelidade n\xc3\xa3o calculada."
        )

    if statevector is not None:
        try:
            metrics["sv_entropy"] = sv_entropy(statevector)
            metrics["linear_entropy"] = linear_entropy(statevector)
        except Exception as exc:
            logger.error("Erro ao calcular entropias do statevector: %s", exc)

    if counts:
        try:
            total_shots = sum(counts.values())
            if total_shots > 0:
                probabilities = np.array(list(counts.values())) / total_shots
                probabilities = probabilities[probabilities > 0]
                shannon_entropy = -np.sum(probabilities * np.log2(probabilities))
                metrics["final_entropy_from_counts"] = float(max(shannon_entropy, 0.0))
        except Exception as e:
            logger.error(
                "Erro ao calcular a entropia de Shannon das contagens: %s",
                e,
                exc_info=True,
            )

    unique_outcomes = len(counts) if counts else 0
    metrics["counts_unique_outcomes"] = unique_outcomes
    if counts:
        measured_bits = max(len(bitstr.replace(" ", "")) for bitstr in counts)
        total_shots = sum(counts.values())
    else:
        measured_bits = universe.n_qubits
        total_shots = 0
    metrics["num_measured_qubits"] = measured_bits
    metrics["counts_diversity"] = (
        unique_outcomes / float(total_shots) if total_shots > 0 else 0.0
    )
    metrics["counts_diversity_ratio"] = (
        unique_outcomes / float(2**measured_bits) if counts else 0.0
    )

    if getattr(universe, "qc", None):
        metrics["circuit_depth"] = universe.qc.depth()
        metrics["circuit_size"] = universe.qc.size()
    else:
        metrics["circuit_depth"] = None
        metrics["circuit_size"] = None

    return metrics
