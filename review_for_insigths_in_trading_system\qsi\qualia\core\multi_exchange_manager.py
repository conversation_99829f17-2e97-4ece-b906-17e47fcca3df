"""
QUALIA Multi-Exchange Dimensional Trading Manager

Coordena operações de trading em múltiplas exchanges para maximizar
a diversificação dimensional quântica e explorar oportunidades de arbitragem.
"""

import asyncio
import logging
import json
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any, Tuple, Coroutine, TypeVar

T = TypeVar("T")
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path
import numpy as np
import random

from ..exchanges.base_exchange import BaseExchange
from ..exchanges.kraken_client import KrakenClient
from ..exchanges.kucoin_client import KuCoinClient

logger = logging.getLogger(__name__)


class ExchangeRole(Enum):
    """Define o papel de cada exchange na estratégia dimensional"""

    PRIMARY = "primary"  # Exchange principal para execução
    SECONDARY = "secondary"  # Exchange secundária para arbitragem
    LIQUIDITY = "liquidity"  # Exchange para balanceamento de liquidez
    DATA_FEED = "data_feed"  # Exchange apenas para coleta de dados


@dataclass
class DimensionalOpportunity:
    """Representa uma oportunidade de arbitragem dimensional"""

    symbol: str
    exchange_a: str
    exchange_b: str
    price_a: float
    price_b: float
    spread_pct: float
    volume_available: float
    quantum_confidence: float
    temporal_window: float
    execution_priority: int
    timestamp: float = 0.0

    def to_dict(self) -> Dict[str, Any]:
        """Converter para dicionário para persistência"""
        return asdict(self)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "DimensionalOpportunity":
        """Criar instância a partir de dicionário"""
        return cls(**data)


@dataclass
class ExchangeState:
    """Estado dimensional de uma exchange"""

    name: str
    role: ExchangeRole
    connected: bool
    latency_ms: float
    liquidity_score: float
    quantum_coherence: float
    active_pairs: List[str]
    last_update: datetime
    error_count: int = 0
    last_error: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        """Converter para dicionário para persistência"""
        return {
            "name": self.name,
            "role": self.role.value,
            "connected": self.connected,
            "latency_ms": self.latency_ms,
            "liquidity_score": self.liquidity_score,
            "quantum_coherence": self.quantum_coherence,
            "active_pairs": self.active_pairs,
            "last_update": self.last_update.isoformat(),
            "error_count": self.error_count,
            "last_error": self.last_error,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "ExchangeState":
        """Criar instância a partir de dicionário"""
        return cls(
            name=data["name"],
            role=ExchangeRole(data["role"]),
            connected=data["connected"],
            latency_ms=data["latency_ms"],
            liquidity_score=data["liquidity_score"],
            quantum_coherence=data["quantum_coherence"],
            active_pairs=data["active_pairs"],
            last_update=datetime.fromisoformat(data["last_update"]),
            error_count=data.get("error_count", 0),
            last_error=data.get("last_error"),
        )


class MultiExchangeManager:
    """
    Gerenciador Multi-Exchange para Diversificação Dimensional QUALIA

    Coordena múltiplas exchanges para:
    - Arbitragem dimensional
    - Diversificação de risco
    - Otimização de liquidez
    - Análise quântica cross-exchange
    """

    def __init__(self, config: Dict[str, Any]) -> None:
        """Inicializa o gerenciador multi-exchange.

        Parameters
        ----------
        config : dict
            Dicionário de configurações do sistema dimensional.
        """
        self.config = config
        self.exchanges: Dict[str, BaseExchange] = {}
        self.exchange_states: Dict[str, ExchangeState] = {}
        self.opportunities: List[DimensionalOpportunity] = []

        # Configurações do sistema dimensional
        self.dimensional_config = config.get("dimensional", {})
        self.min_spread_threshold = self.dimensional_config.get(
            "min_spread_threshold", 0.001
        )  # Reduzido para 0.1%
        self.max_latency_ms = self.dimensional_config.get(
            "max_latency_ms", 1000
        )  # Aumentado para maior tolerância
        self.quantum_coherence_threshold = self.dimensional_config.get(
            "quantum_coherence_threshold", 0.5
        )  # Valor padrão reduzido

        # Estado do sistema
        self.running = False
        self.monitoring_task: Optional[asyncio.Task] = None

        # Rate limiting per exchange
        self.rate_limiters: Dict[str, asyncio.Semaphore] = {}
        self.last_requests: Dict[str, List[float]] = {}

        # Persistência de estado
        self.state_file = Path("data/state/multi_exchange_state.json")
        self.state_file.parent.mkdir(parents=True, exist_ok=True)

        # Métricas dimensionais
        self.dimensional_metrics = {
            "total_opportunities": 0,
            "executed_arbitrages": 0,
            "total_dimensional_pnl": 0.0,
            "cross_exchange_correlation": 0.0,
            "quantum_entanglement_strength": 0.0,
            "session_start": datetime.now(timezone.utc).isoformat(),
        }

        # Carregar estado persistido
        self._load_persistent_state()

    def _load_persistent_state(self) -> None:
        """Carrega estado persistido de sessões anteriores"""
        try:
            if self.state_file.exists():
                with open(self.state_file, "r") as f:
                    state_data = json.load(f)

                # Carregar métricas
                if "metrics" in state_data:
                    self.dimensional_metrics.update(state_data["metrics"])

                # Carregar estados de exchange (se não muito antigos)
                if "exchange_states" in state_data:
                    for name, state_dict in state_data["exchange_states"].items():
                        try:
                            state = ExchangeState.from_dict(state_dict)
                            # Só carregar se menos de 1 hora atrás
                            if (
                                datetime.now(timezone.utc) - state.last_update
                            ).total_seconds() < 3600:
                                self.exchange_states[name] = state
                                self.exchange_states[name].connected = (
                                    False  # Reset connection status
                                )
                        except Exception as e:
                            logger.debug(f"Erro ao carregar estado de {name}: {e}")

                logger.info("🔄 Estado dimensional anterior carregado")

        except Exception as e:
            logger.debug(f"Erro ao carregar estado persistido: {e}")

    def _save_persistent_state(self) -> None:
        """Salva estado atual para persistência"""
        try:
            state_data = {
                "metrics": self.dimensional_metrics,
                "exchange_states": {
                    name: state.to_dict()
                    for name, state in self.exchange_states.items()
                },
                "saved_at": datetime.now(timezone.utc).isoformat(),
            }

            with open(self.state_file, "w") as f:
                json.dump(state_data, f, indent=2)

        except Exception as e:
            logger.warning(f"Erro ao salvar estado: {e}")

    async def initialize(self) -> None:
        """Inicializa o sistema multi-exchange"""
        try:
            logger.info("🌌 Inicializando QUALIA Multi-Exchange Dimensional Manager")

            # 1. Verificar se está habilitado
            if not self.config.get("multi_exchange", {}).get("enabled", False):
                logger.warning("⚠️ Multi-Exchange desabilitado na configuração")
                return

            # 2. Inicializar exchanges configuradas
            await self._initialize_exchanges()

            # 3. Configurar papéis dimensionais
            self._assign_exchange_roles()

            # 4. Inicializar rate limiters
            self._initialize_rate_limiters()

            # 5. Verificar se temos pelo menos uma exchange ativa
            active_exchanges = [
                name for name, state in self.exchange_states.items() if state.connected
            ]
            if not active_exchanges:
                logger.warning(
                    "⚠️ Nenhuma exchange ativa - Multi-Exchange em modo standby"
                )
                return

            # 6. Marcar sistema como em execução
            self.running = True

            # 7. Iniciar monitoramento dimensional
            self.monitoring_task = asyncio.create_task(
                self._monitor_dimensional_opportunities()
            )

            logger.info(
                f"✅ Multi-Exchange Manager ativo com {len(active_exchanges)} exchanges: {active_exchanges}"
            )

        except Exception as e:
            logger.error(f"❌ Falha na inicialização do Multi-Exchange Manager: {e}")
            # Não re-lançar exceção para não quebrar o sistema principal
            self.running = False

    def _initialize_rate_limiters(self) -> None:
        """Inicializa limitadores de taxa para cada exchange"""
        for exchange_name in self.exchanges.keys():
            # Kraken: 1 req/s, KuCoin: 10 req/s
            limit = 10 if exchange_name == "kucoin" else 1
            self.rate_limiters[exchange_name] = asyncio.Semaphore(limit)
            self.last_requests[exchange_name] = []

    async def _rate_limited_request(
        self, exchange_name: str, coro: Coroutine[Any, Any, T]
    ) -> T:
        """Executa requisição com rate limiting.

        Parameters
        ----------
        exchange_name : str
            Nome da exchange associada à requisição.
        coro : Coroutine
            Corrotina a ser executada sob controle de taxa.

        Returns
        -------
        T
            Resultado da corrotina fornecida.
        """
        current_time = asyncio.get_event_loop().time()

        # Limpar requisições antigas (>1s)
        self.last_requests[exchange_name] = [
            t for t in self.last_requests[exchange_name] if current_time - t < 1.0
        ]

        # Verificar se precisa aguardar
        limit = 10 if exchange_name == "kucoin" else 1
        if len(self.last_requests[exchange_name]) >= limit:
            sleep_time = 1.0 - (current_time - self.last_requests[exchange_name][0])
            if sleep_time > 0:
                await asyncio.sleep(sleep_time)

        # Executar com semáforo
        async with self.rate_limiters[exchange_name]:
            self.last_requests[exchange_name].append(current_time)
            return await coro

    async def _initialize_exchanges(self) -> None:
        """Inicializa conexões com as exchanges configuradas"""
        exchange_config = self.config.get("exchanges", {})

        # Kraken
        if exchange_config.get("kraken", {}).get("enabled", False):
            try:
                kraken_client = KrakenClient(exchange_config["kraken"])
                await kraken_client.initialize()
                self.exchanges["kraken"] = kraken_client

                # Restaurar estado ou criar novo
                if "kraken" not in self.exchange_states:
                    self.exchange_states["kraken"] = ExchangeState(
                        name="kraken",
                        role=ExchangeRole.PRIMARY,
                        connected=True,
                        latency_ms=0.0,
                        liquidity_score=0.9,
                        quantum_coherence=0.8,
                        active_pairs=["BTC/USDT", "ETH/USDT"],
                        last_update=datetime.now(timezone.utc),
                    )
                else:
                    self.exchange_states["kraken"].connected = True
                    self.exchange_states["kraken"].last_update = datetime.now(
                        timezone.utc
                    )

                logger.info("🔗 Kraken conectada como PRIMARY exchange")

            except Exception as e:
                logger.warning(f"⚠️ Falha ao conectar Kraken: {e}")
                if "kraken" in self.exchange_states:
                    self.exchange_states["kraken"].connected = False
                    self.exchange_states["kraken"].error_count += 1
                    self.exchange_states["kraken"].last_error = str(e)

        # KuCoin
        if exchange_config.get("kucoin", {}).get("enabled", False):
            try:
                kucoin_client = KuCoinClient(exchange_config["kucoin"])
                await kucoin_client.initialize()
                self.exchanges["kucoin"] = kucoin_client

                # Restaurar estado ou criar novo
                if "kucoin" not in self.exchange_states:
                    self.exchange_states["kucoin"] = ExchangeState(
                        name="kucoin",
                        role=ExchangeRole.SECONDARY,
                        connected=True,
                        latency_ms=0.0,
                        liquidity_score=0.8,
                        quantum_coherence=0.7,
                        active_pairs=["BTC/USDT", "ETH/USDT"],
                        last_update=datetime.now(timezone.utc),
                    )
                else:
                    self.exchange_states["kucoin"].connected = True
                    self.exchange_states["kucoin"].last_update = datetime.now(
                        timezone.utc
                    )

                logger.info("🔗 KuCoin conectada como SECONDARY exchange")

            except Exception as e:
                logger.warning(f"⚠️ Falha ao conectar KuCoin: {e}")
                if "kucoin" in self.exchange_states:
                    self.exchange_states["kucoin"].connected = False
                    self.exchange_states["kucoin"].error_count += 1
                    self.exchange_states["kucoin"].last_error = str(e)

        if not self.exchanges:
            raise RuntimeError(
                "❌ Nenhuma exchange disponível para diversificação dimensional"
            )

        # Salvar estado atualizado
        self._save_persistent_state()

    def _assign_exchange_roles(self) -> None:
        """Atribui papéis dimensionais às exchanges baseado em características"""

        # Kraken como primária (maior estabilidade)
        if "kraken" in self.exchanges:
            self.exchange_states["kraken"].role = ExchangeRole.PRIMARY

        # KuCoin como secundária/arbitragem (maior velocidade)
        if "kucoin" in self.exchanges:
            self.exchange_states["kucoin"].role = ExchangeRole.SECONDARY

        # Se houver múltiplas exchanges, otimizar papéis
        if len(self.exchanges) >= 2:
            self._optimize_dimensional_roles()

    def _optimize_dimensional_roles(self) -> None:
        """Otimiza papéis das exchanges baseado em métricas quânticas"""
        # Análise dimensional para otimização de papéis
        for name, state in self.exchange_states.items():
            # Calcular score dimensional
            error_penalty = min(state.error_count * 0.1, 0.5)  # Penalizar erros
            dimensional_score = (
                state.liquidity_score * 0.3
                + state.quantum_coherence * 0.4
                + (1.0 - min(state.latency_ms / 1000, 1.0)) * 0.3
                - error_penalty
            )

            # Atribuir papel baseado no score
            if dimensional_score > 0.8:
                state.role = ExchangeRole.PRIMARY
            elif dimensional_score > 0.6:
                state.role = ExchangeRole.SECONDARY
            else:
                state.role = ExchangeRole.LIQUIDITY

        logger.info("🎯 Papéis dimensionais otimizados baseado em métricas quânticas")

    async def _start_monitoring(self) -> None:
        """Inicia monitoramento dimensional das exchanges"""
        self.monitoring_task = asyncio.create_task(
            self._monitor_dimensional_opportunities()
        )

    async def _monitor_dimensional_opportunities(self) -> None:
        """Monitora oportunidades dimensionais entre exchanges"""
        while self.running:
            try:
                # Executar tarefas em paralelo para eficiência
                await asyncio.gather(
                    self._scan_arbitrage_opportunities(),
                    self._update_exchange_states(),
                    self._calculate_dimensional_metrics(),
                    return_exceptions=True,
                )

                # Salvar estado periodicamente
                if datetime.now().second % 30 == 0:  # A cada 30 segundos
                    self._save_persistent_state()

                # Aguardar próximo ciclo (frequência reduzida para evitar rate limits)
                await asyncio.sleep(3.0)  # Aumentado para 3 segundos

            except Exception as e:
                logger.error(f"❌ Erro no monitoramento dimensional: {e}")
                await asyncio.sleep(10.0)

    async def _scan_arbitrage_opportunities(self) -> None:
        """Escaneia oportunidades de arbitragem dimensional"""
        if len(self.exchanges) < 2:
            return

        # Símbolos para análise dimensional
        dimensional_symbols = self.dimensional_config.get(
            "cross_exchange_symbols", ["BTC/USDT", "ETH/USDT"]
        )

        new_opportunities = []

        # Comparar todas as combinações de exchanges
        exchange_names = list(self.exchanges.keys())
        for i, ex_a in enumerate(exchange_names):
            for ex_b in exchange_names[i + 1 :]:
                # Verificar se ambas estão conectadas
                if (
                    not self.exchange_states[ex_a].connected
                    or not self.exchange_states[ex_b].connected
                ):
                    continue

                for symbol in dimensional_symbols:
                    try:
                        # CORREÇÃO: Usar get_ticker de forma consistente com rate limiting
                        ticker_a_coro = self.exchanges[ex_a].get_ticker(symbol)
                        ticker_b_coro = self.exchanges[ex_b].get_ticker(symbol)

                        ticker_a, ticker_b = await asyncio.gather(
                            self._rate_limited_request(ex_a, ticker_a_coro),
                            self._rate_limited_request(ex_b, ticker_b_coro),
                            return_exceptions=True,
                        )

                        # Verificar se houve erros
                        if isinstance(ticker_a, Exception) or isinstance(
                            ticker_b, Exception
                        ):
                            if isinstance(ticker_a, Exception):
                                logger.debug(
                                    f"Erro ao buscar ticker {symbol} em {ex_a}: {ticker_a}"
                                )
                                self.exchange_states[ex_a].error_count += 1
                                self.exchange_states[ex_a].last_error = str(ticker_a)
                            if isinstance(ticker_b, Exception):
                                logger.debug(
                                    f"Erro ao buscar ticker {symbol} em {ex_b}: {ticker_b}"
                                )
                                self.exchange_states[ex_b].error_count += 1
                                self.exchange_states[ex_b].last_error = str(ticker_b)
                            continue

                        if ticker_a and ticker_b:
                            opportunity = await self._analyze_arbitrage_opportunity(
                                symbol, ex_a, ex_b, ticker_a, ticker_b
                            )
                            if opportunity:
                                new_opportunities.append(opportunity)

                    except Exception as e:
                        logger.debug(
                            f"Erro ao analisar {symbol} entre {ex_a}-{ex_b}: {e}"
                        )

        # Atualizar lista de oportunidades (manter apenas as últimas 50)
        self.opportunities = sorted(
            new_opportunities,
            key=lambda x: x.quantum_confidence * x.spread_pct,
            reverse=True,
        )[:50]

        if self.opportunities:
            best = self.opportunities[0]
            logger.info(
                f"🎯 Melhor oportunidade dimensional: {best.symbol} "
                f"{best.exchange_a}->{best.exchange_b} "
                f"Spread: {best.spread_pct:.3f}% "
                f"Confiança Quântica: {best.quantum_confidence:.3f}"
            )

    async def _analyze_arbitrage_opportunity(
        self, symbol: str, ex_a: str, ex_b: str, ticker_a: Dict, ticker_b: Dict
    ) -> Optional[DimensionalOpportunity]:
        """Analisa uma oportunidade específica de arbitragem"""

        try:
            price_a = float(ticker_a.get("last", 0) or ticker_a.get("close", 0))
            price_b = float(ticker_b.get("last", 0) or ticker_b.get("close", 0))

            if price_a <= 0 or price_b <= 0:
                return None

            # Calcular spread
            spread_pct = abs(price_a - price_b) / min(price_a, price_b)

            if spread_pct < self.min_spread_threshold:
                return None

            # Calcular confiança quântica baseada em múltiplos fatores
            volume_a = float(
                ticker_a.get("baseVolume", 0) or ticker_a.get("quoteVolume", 0)
            )
            volume_b = float(
                ticker_b.get("baseVolume", 0) or ticker_b.get("quoteVolume", 0)
            )
            min_volume = min(volume_a, volume_b)

            # Fatores para confiança quântica
            volume_factor = min(min_volume / 100.0, 1.0)  # Normalizar volume
            spread_factor = min(spread_pct / 0.01, 1.0)  # Spread normalizado

            # Considerar histórico de erros das exchanges
            error_factor_a = max(
                0.1, 1.0 - (self.exchange_states[ex_a].error_count * 0.1)
            )
            error_factor_b = max(
                0.1, 1.0 - (self.exchange_states[ex_b].error_count * 0.1)
            )
            error_factor = (error_factor_a + error_factor_b) / 2

            latency_factor = 1.0 - (
                (
                    self.exchange_states[ex_a].latency_ms
                    + self.exchange_states[ex_b].latency_ms
                )
                / (2 * self.max_latency_ms)
            )

            quantum_confidence = (
                volume_factor * 0.3
                + spread_factor * 0.3
                + latency_factor * 0.2
                + error_factor * 0.2
            )

            # Apenas oportunidades com alta confiança quântica
            if quantum_confidence < self.quantum_coherence_threshold:
                return None

            return DimensionalOpportunity(
                symbol=symbol,
                exchange_a=ex_a,
                exchange_b=ex_b,
                price_a=price_a,
                price_b=price_b,
                spread_pct=spread_pct,
                volume_available=min_volume,
                quantum_confidence=quantum_confidence,
                temporal_window=5.0,  # Janela temporal de 5 segundos
                execution_priority=int(quantum_confidence * spread_pct * 1000),
                timestamp=asyncio.get_event_loop().time(),
            )

        except Exception as e:
            logger.debug(f"Erro na análise de arbitragem {symbol}: {e}")
            return None

    async def _update_exchange_states(self) -> None:
        """Atualiza estados dimensionais das exchanges"""
        for name, exchange in self.exchanges.items():
            try:
                # Ping para medir latência usando rate limiting
                start_time = asyncio.get_event_loop().time()
                ticker_coro = exchange.get_ticker("BTC/USDT")
                ticker = await self._rate_limited_request(name, ticker_coro)
                latency = (asyncio.get_event_loop().time() - start_time) * 1000

                # Atualizar estado
                state = self.exchange_states[name]
                state.latency_ms = latency
                state.connected = ticker is not None
                state.last_update = datetime.now(timezone.utc)

                # Calcular coerência quântica baseada em responsividade e confiabilidade
                if latency > 0:
                    latency_score = max(0.1, 1.0 - (latency / self.max_latency_ms))
                    error_score = max(
                        0.1, 1.0 - (state.error_count * 0.05)
                    )  # Penalizar erros
                    state.quantum_coherence = (latency_score + error_score) / 2

            except Exception as e:
                logger.debug(f"Erro ao atualizar estado da {name}: {e}")
                state = self.exchange_states[name]
                state.connected = False
                state.error_count += 1
                state.last_error = str(e)
                state.quantum_coherence = max(
                    0.1, state.quantum_coherence * 0.9
                )  # Degradar gradualmente

    async def _calculate_dimensional_metrics(self) -> None:
        """Calcula métricas dimensionais do sistema"""
        try:
            # Correlação cross-exchange
            if len(self.exchanges) >= 2:
                self.dimensional_metrics["cross_exchange_correlation"] = (
                    await self._calculate_cross_correlation()
                )

            # Força de entrelaçamento quântico
            total_coherence = sum(
                state.quantum_coherence
                for state in self.exchange_states.values()
                if state.connected
            )
            connected_count = sum(
                1 for state in self.exchange_states.values() if state.connected
            )
            avg_coherence = (
                total_coherence / connected_count if connected_count > 0 else 0.0
            )
            self.dimensional_metrics["quantum_entanglement_strength"] = avg_coherence

            # Total de oportunidades
            self.dimensional_metrics["total_opportunities"] = len(self.opportunities)

        except Exception as e:
            logger.debug(f"Erro no cálculo de métricas dimensionais: {e}")

    async def _calculate_cross_correlation(self) -> float:
        """Calcula correlação entre exchanges"""
        try:
            # Implementação baseada em oportunidades atuais
            if not self.opportunities:
                return 0.5  # Correlação neutra

            spreads = [opp.spread_pct for opp in self.opportunities]
            confidences = [opp.quantum_confidence for opp in self.opportunities]

            if len(spreads) < 2:
                return 0.5

            # Correlação baseada na consistência de spreads e confiança
            spread_std = np.std(spreads)
            confidence_avg = np.mean(confidences)

            # Menor desvio padrão e maior confiança = maior correlação
            correlation = min(1.0, confidence_avg * (1.0 - min(spread_std / 0.01, 1.0)))

            return max(0.0, correlation)

        except Exception as e:
            logger.debug(f"Erro no cálculo de correlação: {e}")
            return 0.5

    async def get_exchange_for_symbol(
        self, symbol: str, operation_type: str = "trade"
    ) -> Optional[str]:
        """Retorna a melhor exchange para operar um símbolo específico"""

        # 1. Filtrar exchanges conectadas
        available_exchanges = [
            name
            for name, state in self.exchange_states.items()
            if state.connected and name in self.exchanges
        ]

        if not available_exchanges:
            return None

        # 2. Construir função de score unificada
        def dimensional_score(name: str) -> float:
            state = self.exchange_states[name]

            # Pontuação de latência (quanto menor melhor)
            latency_norm = max(0.0, 1.0 - (state.latency_ms / self.max_latency_ms))

            # Coerência quântica já em [0,1]
            coherence = state.quantum_coherence

            # Penalização por erros (cada erro reduz 5 %)
            error_penalty = max(0.5, 1.0 - (state.error_count * 0.05))

            # Peso configurável
            latency_weight = 0.4
            coherence_weight = 0.4
            error_weight = 0.2

            return (
                latency_norm * latency_weight
                + coherence * coherence_weight
                + error_penalty * error_weight
            )

        # 3. Seleção por tipo de operação
        if operation_type == "trade":
            # Priorizar exchanges marcadas como PRIMARY, mas ainda usando score
            primary = [
                ex
                for ex in available_exchanges
                if self.exchange_states[ex].role == ExchangeRole.PRIMARY
            ]
            candidates = primary if primary else available_exchanges
            return max(candidates, key=dimensional_score)

        elif operation_type == "arbitrage":
            # Para arbitragem, buscar melhor combinação; aqui retornamos melhor única exchange
            return max(available_exchanges, key=dimensional_score)

        # 4. Default: melhor score geral
        return max(available_exchanges, key=dimensional_score)

    def get_dimensional_metrics(self) -> Dict[str, Any]:
        """Retorna métricas dimensionais atuais"""
        return {
            **self.dimensional_metrics,
            "exchange_states": {
                name: {
                    "role": state.role.value,
                    "connected": state.connected,
                    "latency_ms": state.latency_ms,
                    "quantum_coherence": state.quantum_coherence,
                    "liquidity_score": state.liquidity_score,
                    "error_count": state.error_count,
                    "last_error": state.last_error,
                }
                for name, state in self.exchange_states.items()
            },
            "active_opportunities": len(self.opportunities),
            "best_opportunity": (
                {
                    "symbol": self.opportunities[0].symbol,
                    "spread_pct": self.opportunities[0].spread_pct,
                    "quantum_confidence": self.opportunities[0].quantum_confidence,
                    "timestamp": self.opportunities[0].timestamp,
                }
                if self.opportunities
                else None
            ),
            "system_health": {
                "connected_exchanges": sum(
                    1 for state in self.exchange_states.values() if state.connected
                ),
                "total_exchanges": len(self.exchange_states),
                "avg_latency": (
                    np.mean(
                        [
                            state.latency_ms
                            for state in self.exchange_states.values()
                            if state.connected
                        ]
                    )
                    if any(state.connected for state in self.exchange_states.values())
                    else 0
                ),
                "total_errors": sum(
                    state.error_count for state in self.exchange_states.values()
                ),
            },
        }

    async def shutdown(self) -> None:
        """Encerra o sistema multi-exchange"""
        logger.info("🛑 Encerrando QUALIA Multi-Exchange Dimensional Manager")

        self.running = False

        # Cancelar tarefas
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass

        # Salvar estado final
        self._save_persistent_state()

        # Fechar conexões
        for name, exchange in self.exchanges.items():
            try:
                await exchange.shutdown()
                logger.info(f"✅ Exchange {name} desconectada")
            except Exception as e:
                logger.warning(f"⚠️ Erro ao desconectar {name}: {e}")

        logger.info("✅ Multi-Exchange Manager encerrado")

    async def get_optimal_exchange_for_operation(
        self,
        symbol: str,
        operation_type: str = "trade",
        amount: Optional[float] = None,
        side: Optional[str] = None,
    ) -> Optional[str]:
        """
        🧠 Seleção inteligente de exchange baseada em IA dimensional

        Considera múltiplos fatores:
        - Latência e confiabilidade
        - Liquidez disponível
        - Coerência quântica
        - Histórico de performance
        - Spread atual do par
        """

        # Filtrar exchanges conectadas
        available_exchanges = [
            name
            for name, state in self.exchange_states.items()
            if state.connected and name in self.exchanges
        ]

        if not available_exchanges:
            logger.warning(
                f"Nenhuma exchange disponível para {operation_type} de {symbol}"
            )
            return None

        # Calcular score dimensional para cada exchange
        exchange_scores = {}

        for exchange_name in available_exchanges:
            try:
                score = await self._calculate_dimensional_score(
                    exchange_name, symbol, operation_type, amount, side
                )
                exchange_scores[exchange_name] = score
            except Exception as e:
                logger.debug(f"Erro ao calcular score para {exchange_name}: {e}")
                exchange_scores[exchange_name] = 0.0

        if not exchange_scores:
            return None

        # Seleção baseada em score com elemento de aleatoriedade quântica
        best_exchanges = sorted(
            exchange_scores.items(), key=lambda x: x[1], reverse=True
        )

        # Top 2 exchanges para seleção probabilística
        top_exchanges = best_exchanges[:2]

        if len(top_exchanges) == 1:
            selected = top_exchanges[0][0]
        else:
            # Seleção probabilística: 70% melhor, 30% segunda melhor
            if random.random() < 0.7:
                selected = top_exchanges[0][0]
            else:
                selected = top_exchanges[1][0]

        logger.debug(
            f"🎯 Exchange selecionada para {operation_type} {symbol}: {selected} "
            f"(score: {exchange_scores[selected]:.3f})"
        )

        return selected

    async def _calculate_dimensional_score(
        self,
        exchange_name: str,
        symbol: str,
        operation_type: str,
        amount: Optional[float] = None,
        side: Optional[str] = None,
    ) -> float:
        """Calcula score dimensional para seleção de exchange"""

        state = self.exchange_states[exchange_name]

        # 1. Score base de latência (0-1, menor latência = maior score)
        latency_score = max(0.0, 1.0 - (state.latency_ms / self.max_latency_ms))

        # 2. Score de coerência quântica (já normalizado 0-1)
        coherence_score = state.quantum_coherence

        # 3. Score de confiabilidade (baseado em erros)
        reliability_score = max(0.1, 1.0 - (state.error_count * 0.03))

        # 4. Score de role/prioridade
        role_score = {
            ExchangeRole.PRIMARY: 1.0,
            ExchangeRole.SECONDARY: 0.8,
            ExchangeRole.LIQUIDITY: 0.6,
            ExchangeRole.DATA_FEED: 0.4,
        }.get(state.role, 0.5)

        # 5. Score de liquidez (simplificado)
        liquidity_score = state.liquidity_score

        # 6. Bonus por spread favorável (se disponível)
        spread_bonus = 0.0
        try:
            if hasattr(self, "opportunities") and self.opportunities:
                relevant_opps = [
                    opp
                    for opp in self.opportunities
                    if opp.symbol == symbol
                    and (
                        opp.exchange_a == exchange_name
                        or opp.exchange_b == exchange_name
                    )
                ]
                if relevant_opps:
                    avg_spread = sum(opp.spread_pct for opp in relevant_opps) / len(
                        relevant_opps
                    )
                    spread_bonus = min(0.2, avg_spread * 10)  # Max 0.2 bonus
        except Exception:
            pass

        # Combinação weighted dos scores
        weights = {
            "latency": 0.25,
            "coherence": 0.20,
            "reliability": 0.20,
            "role": 0.15,
            "liquidity": 0.15,
            "spread": 0.05,
        }

        final_score = (
            latency_score * weights["latency"]
            + coherence_score * weights["coherence"]
            + reliability_score * weights["reliability"]
            + role_score * weights["role"]
            + liquidity_score * weights["liquidity"]
            + spread_bonus * weights["spread"]
        )

        return max(0.0, min(1.0, final_score))  # Normalizar 0-1
