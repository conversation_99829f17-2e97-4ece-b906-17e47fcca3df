# -*- coding: utf-8 -*-
"""
Encoders multimodais (áudio, imagem, vídeo) para QUALIA.

Cada encoder herda de ``QuantumEncoder`` e é automaticamente registrado no
``encoder_registry`` ao final do arquivo.

Snapshot esperado por encoder:

1. SpectrogramQuantumEncoder (áudio/STFT)
   {"band_power": float, "timestamp": float}

2. QuantumPatchEncoder (imagem/patch 8×8)
   {"patch": np.ndarray shape (64,), "position": Tuple[int,int]}

3. TemporalDifferenceEncoder (vídeo/frame diff)
   {"diff_value": float, "frame_idx": int}
"""

from __future__ import annotations

from typing import Any, Dict, List, Optional, Sequence

import numpy as np

from .encoders import QuantumEncoder
from ..utils.logger import get_logger
from ..config.encoder_registry import register_encoder

logger = get_logger(__name__)

# ---------------------------------------------------------------------------
# 1. SpectrogramQuantumEncoder – ÁUDIO
# ---------------------------------------------------------------------------


class SpectrogramQuantumEncoder(QuantumEncoder[Dict[str, Any]]):
    """Codifica a potência de uma banda do espectrograma (STFT) em um único qubit.

    Snapshot esperado::

        {
            "band_power": float,   # já normalizado 0-1
            "timestamp":   float   # opcional
        }
    """

    def _encode_single(self, snap: Dict[str, Any]) -> np.ndarray:  # type: ignore[override]
        power = float(snap.get("band_power", 0.0))
        theta = np.clip(power, 0.0, 1.0) * (np.pi / 2.0)
        return np.array([np.cos(theta), np.sin(theta)], dtype=np.float32)

    def _encode_batch(self, snaps: Sequence[Dict[str, Any]]) -> np.ndarray:  # type: ignore[override]
        powers = np.clip(
            np.asarray([float(s.get("band_power", 0.0)) for s in snaps], dtype=np.float32),
            0.0,
            1.0,
        )
        thetas = powers * (np.pi / 2.0)
        return np.stack([np.cos(thetas), np.sin(thetas)], axis=1)

    def get_quantum_operation(self, snap: Dict[str, Any]):  # type: ignore[override]
        theta = np.clip(float(snap.get("band_power", 0.0)), 0.0, 1.0) * (np.pi / 2.0)
        return ("ry", [float(theta)], [0])


# ---------------------------------------------------------------------------
# 2. QuantumPatchEncoder – IMAGEM
# ---------------------------------------------------------------------------


class QuantumPatchEncoder(QuantumEncoder[Dict[str, Any]]):
    """Codifica um patch 8×8 (64 px) em 6 qubits via amplitude-encoding.

    Snapshot esperado::

        {
            "patch": np.ndarray shape (64,) normalizado 0-1,
            "position": Tuple[int, int]  # opcional
        }
    """

    def __init__(self, name: str = "QuantumPatchEncoder"):
        super().__init__(name)
        # Para amplitude encoding de 64 valores precisamos de 6 qubits => 64 amplitudes
        self.output_dim = 64

    def _encode_single(self, snap: Dict[str, Any]) -> np.ndarray:  # type: ignore[override]
        patch = np.asarray(snap.get("patch"), dtype=np.float32).flatten()
        if patch.size != 64:
            logger.warning("[%s] patch invalido – esperado 64 valores", self.name)
            vec = np.zeros(64, dtype=np.float32)
            vec[0] = 1.0
            return vec

        patch = np.clip(patch, 0.0, 1.0)
        norm = float(np.linalg.norm(patch))
        if norm < 1e-9:
            vec = np.zeros(64, dtype=np.float32)
            vec[0] = 1.0
            return vec
        return patch / norm

    def _encode_batch(self, snaps: Sequence[Dict[str, Any]]) -> np.ndarray:  # type: ignore[override]
        return np.stack([self._encode_single(s) for s in snaps], axis=0).astype(np.float32)

    def get_quantum_operation(self, snap: Dict[str, Any]):  # type: ignore[override]
        vec = self._encode_single(snap)
        
        # YAA CORREÇÃO CRÍTICA: Verificar e garantir normalização adequada
        norm_squared = np.sum(vec ** 2)
        if not np.isclose(norm_squared, 1.0, atol=1e-10):
            if norm_squared > 1e-12:
                vec = vec / np.sqrt(norm_squared)
            else:
                # Fallback para estado base normalizado 
                vec = np.zeros(64, dtype=np.float32)
                vec[0] = 1.0
                
        # Verificação final
        final_norm_squared = np.sum(vec ** 2)
        if not np.isclose(final_norm_squared, 1.0, atol=1e-10):
            logger.warning(
                f"QuantumPatch encoder '{self.name}': normalização falhou "
                f"(norm²={final_norm_squared:.12f}), forçando renormalização"
            )
            vec = vec / np.sqrt(final_norm_squared)
            
        # Qubits locais 0-5 (6 qubits)
        return ("initialize", vec.tolist(), list(range(6)))


# ---------------------------------------------------------------------------
# 3. TemporalDifferenceEncoder – VÍDEO
# ---------------------------------------------------------------------------


class TemporalDifferenceEncoder(QuantumEncoder[Dict[str, Any]]):
    """Codifica a diferença média normalizada entre dois frames consecutivos.

    Snapshot esperado::

        {
            "diff_value": float,  # valor normalizado 0-1 já calculado externamente
            "frame_idx":  int
        }
    """

    def _encode_single(self, snap: Dict[str, Any]) -> np.ndarray:  # type: ignore[override]
        dv = float(snap.get("diff_value", 0.0))
        phi = np.clip(dv, 0.0, 1.0) * np.pi  # fase 0-π
        return np.array([np.cos(phi), np.sin(phi)], dtype=np.float32)

    def _encode_batch(self, snaps: Sequence[Dict[str, Any]]) -> np.ndarray:  # type: ignore[override]
        dvs = np.clip(
            np.asarray([float(s.get("diff_value", 0.0)) for s in snaps], dtype=np.float32),
            0.0,
            1.0,
        )
        phis = dvs * np.pi
        return np.stack([np.cos(phis), np.sin(phis)], axis=1)

    def get_quantum_operation(self, snap: Dict[str, Any]):  # type: ignore[override]
        phi = np.clip(float(snap.get("diff_value", 0.0)), 0.0, 1.0) * np.pi
        return ("rz", [float(phi)], [0])


# ---------------------------------------------------------------------------
# Registro automático dos encoders
# ---------------------------------------------------------------------------

register_encoder("quantum_spectrogram", SpectrogramQuantumEncoder)
register_encoder("quantum_patch", QuantumPatchEncoder)
register_encoder("quantum_temporal_diff", TemporalDifferenceEncoder)

logger.info("Multimodal encoders registrados: quantum_spectrogram, quantum_patch, quantum_temporal_diff")
