"""Noise injection utilities for QUALIA core.

This module provides a simple function :func:`inject_noise` used to perturb
quantum fields with controlled stochasticity. Noise can be white (Gaussian) or
periodic, helping avoid fixation in stationary states during simulations.
"""

from __future__ import annotations

import numpy as np
from numpy.typing import NDArray
from typing import Literal

__all__ = ["inject_noise"]


def inject_noise(
    field: NDArray[np.float_],
    *,
    sigma: float = 0.01,
    mode: Literal["white", "periodic"] = "white",
    frequency: float = 1.0,
    step: int = 0,
    rng: np.random.Generator | None = None,
) -> NDArray[np.float_]:
    """Return ``field`` with additional noise.

    Parameters
    ----------
    field
        Array representing the field to be perturbed.
    sigma
        Standard deviation of the noise.
    mode
        ``"white"`` for additive Gaussian noise or ``"periodic"`` for
        sinusoidal perturbations.
    frequency
        Frequency used when ``mode`` is ``"periodic"``.
    step
        Current step index for the periodic phase.
    rng
        Optional :class:`numpy.random.Generator` for reproducibility.

    Returns
    -------
    np.ndarray
        New field with noise added.
    """
    if rng is None:
        rng = np.random.default_rng()

    data = np.asarray(field, dtype=float)

    if sigma <= 0:
        return data

    if mode == "white":
        noise = rng.normal(0.0, sigma, size=data.shape)
    elif mode == "periodic":
        phase = 2 * np.pi * frequency * step
        noise = sigma * np.sin(phase) * np.ones_like(data)
    else:
        raise ValueError("mode must be 'white' or 'periodic'")

    return data + noise
