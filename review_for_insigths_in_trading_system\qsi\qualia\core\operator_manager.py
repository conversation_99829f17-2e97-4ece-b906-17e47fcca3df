"""Manage instantiation and access of QUALIA operators."""

from __future__ import annotations

import os
from typing import Any, Dict

from .emergence import EmergenceOperator
from .folding import FoldingOperator
from .integration_operator import IntegrationOperator
from .intention import IntentioWaveletEngine
from .observer import ObserverOperator
from .operator_sim import OperatorSI<PERSON>
from .tokenizer import QuantumSymbolicTokenizer
from .reduction_operator import ReductionOperator
from .resonance import ResonanceOperator
from .retrocausality import RetrocausalityOperator
from .symbolic_intention_operator import SymbolicIntentionOperator
from .microtubule_operator import MicrotubuleOperator


# List of human-readable operator keys available in QUALIA
AVAILABLE_OPERATORS = {
    "FOLD": "folding",
    "RES": "resonance",
    "EMG": "emergence",
    "RETRO": "retrocausality",
    "OBS": "observer",
    "RED": "reduction",
    "INT": "integration",
    "MIC": "microtubule_operator",
    "SIM": "operator_sim",  # SIM-05
}


class OperatorManager:
    """Instantiate and keep references to QUALIA operators."""

    def __init__(self, config: Dict[str, Any]) -> None:
        self.config = config
        self.retro_enabled = False
        self.initialize()

    def initialize(self) -> None:
        """Create operator instances from configuration."""
        qualia_config = self.config.get("qualia", {})

        self.folding = FoldingOperator(qualia_config.get("folding", {}))
        self.resonance = ResonanceOperator(qualia_config.get("resonance", {}))
        self.emergence = EmergenceOperator(qualia_config.get("emergence", {}))
        self.microtubule = MicrotubuleOperator(qualia_config.get("microtubule", {}))

        retro_cfg = qualia_config.get("retrocausality", {})
        env_enabled = os.getenv("QUALIA_RETRO_ENABLED")
        if env_enabled is not None:
            self.retro_enabled = env_enabled.lower() in {"1", "true", "yes", "y", "t"}
        else:
            self.retro_enabled = bool(retro_cfg.get("enabled", False))
        self.retrocausality = RetrocausalityOperator(retro_cfg)

        self.observer = ObserverOperator(qualia_config.get("observer", {}))
        self.reduction = ReductionOperator(qualia_config.get("reduction", {}))
        self.integration = IntegrationOperator(qualia_config.get("integration", {}))

        self.intent_engine = IntentioWaveletEngine(
            **qualia_config.get("intent_engine", {})
        )
        self.symbolic_intention = SymbolicIntentionOperator(
            **qualia_config.get("symbolic_intention", {})
        )
        # Optional SIM ------------------------------------------------------
        sym_mod_cfg = qualia_config.get("symbolic_modulation", {})
        self.enable_sim = bool(sym_mod_cfg.get("enabled", False))
        if self.enable_sim:
            self.quantum_tokenizer = QuantumSymbolicTokenizer()
            self.operator_sim = OperatorSIM()
        else:
            self.quantum_tokenizer = None
            self.operator_sim = None

    def __getattr__(self, name: str) -> Any:  # pragma: no cover - simple forwarder
        try:
            return self.__dict__[name]
        except KeyError as exc:
            raise AttributeError(name) from exc
