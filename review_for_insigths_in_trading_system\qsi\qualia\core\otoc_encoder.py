from __future__ import annotations

"""Encoder para valores de OTOC.

Este módulo define :class:`OTOCEncoder`, que converte valores
numéricos de OTOC em operações quânticas simples. Um OTOC igual a ``1``
indica ausência de scrambling, portanto o estado retornado deve ser
``|0\rangle``.
"""

from typing import Any, Dict, List, Optional, Sequence

import numpy as np

from .encoders import QuantumEncoder
from ..config import encoder_registry as _er
from ..utils.logger import get_logger

logger = get_logger(__name__)


class OTOCData(Dict[str, Any]):
    """Estrutura mínima esperada pelo :class:`OTOCEncoder`."""


class OTOCEncoder(QuantumEncoder[OTOCData]):
    """Codifica valores de OTOC em rotações ``Ry``.

    O ângulo de rotação é calculado como ``(1 - otoc) * (pi/2)`` para que
    ``otoc = 1`` resulte em ``|0\rangle`` e ``otoc = 0`` gere ``|1\rangle``.
    """

    def __init__(
        self,
        name: str = "OTOCEncoder",
        data_keys: Optional[List[str]] = None,
        target_qubits: Optional[List[int]] = None,
        **kwargs: Any,
    ) -> None:
        super().__init__(name)
        self.data_keys = data_keys if data_keys else ["otoc"]
        self.target_qubits = target_qubits if target_qubits else []
        self.n_qubits_required = len(self.target_qubits) if self.target_qubits else 1
        for key, value in kwargs.items():
            logger.debug("Encoder '%s' ignorou parametro extra '%s'", self.name, key)
        logger.info(
            "Encoder '%s' inicializado com data_keys=%s, target_qubits=%s",
            self.name,
            self.data_keys,
            self.target_qubits,
        )
        self._internal_rotation_angle: Optional[float] = None

    def _encode_single(self, snap: OTOCData) -> np.ndarray:  # type: ignore[override]
        self._internal_rotation_angle = None
        key = self.data_keys[0]
        value = snap.get(key)
        if not isinstance(value, (int, float)):
            logger.warning(
                "Encoder '%s' snapshot malformado ou sem chave '%s': %s",
                self.name,
                key,
                snap,
            )
            return np.array([1.0, 0.0], dtype=np.float32)
        otoc_val = np.clip(float(value), 0.0, 1.0)
        angle = (1.0 - otoc_val) * (np.pi / 2.0)
        self._internal_rotation_angle = angle
        return np.array([np.cos(angle), np.sin(angle)], dtype=np.float32)

    def _encode_batch(self, snaps: Sequence[OTOCData]) -> np.ndarray:  # type: ignore[override]
        vectors = [self._encode_single(s) for s in snaps]
        return np.stack(vectors, axis=0).astype(np.float32)

    def get_quantum_operation(
        self, snap: OTOCData
    ) -> Optional[tuple[str, List[float], List[int]]]:
        self._encode_single(snap)
        if self._internal_rotation_angle is not None:
            return ("ry", [self._internal_rotation_angle], [0])
        return None


_er.register_encoder("OTOCEncoder", OTOCEncoder)
logger.info("OTOC encoder registrado: OTOCEncoder")
