from __future__ import annotations

import time
from collections import deque
from typing import Any, Callable, Deque, Dict, List, Optional, Tuple

import numpy as np
from qiskit import QuantumCircuit
from qiskit.quantum_info import Statevector, entropy

from ..core.imports_safe import safe_get_backend
from ..metrics.metrics_model import QUALIAMetrics
from ..utils.logger import get_logger
from ..core.state_management import shallow_copy_statevector

logger = get_logger(__name__)


# Type hints from qast_core
# Imported inside functions to avoid circular imports.


def _prepare_qast_feedback_state(
    universe: "QUALIAQuantumUniverse",
    entropy_threshold: float,
    stable_cycles: int,
    use_density_matrix: bool,
    initial_type_fallback: str,
    qast_hyperparams_override: Optional[Dict[str, Any]],
) -> Tuple[
    Callable[[str, Any], None],
    Callable[[], Dict[str, Any]],
    "HyperParams",
    "PIDController",
    "MassManager",
    Deque[float],
    Any,
    Any,
]:
    """Prepare internal state and helper objects for QAST feedback run."""

    from ..core.qast_core import HyperParams, MassManager
    from ..core.metrics_controller import PIDController

    metrics_obj = QUALIAMetrics(max_history_size=universe.max_history_size)

    def add_metric(name: str, value: Any) -> None:
        metrics_obj.add_entry(name, value)

    def get_metrics() -> Dict[str, Any]:
        return metrics_obj.get_metrics_dict()

    qast_params_dict = {
        "max_degree": getattr(universe, "qast_max_degree", 2),
        "alpha_coeffs": getattr(universe, "qast_alpha_coeffs", (0.01, 0.05, 0.02)),
        "mass_min": getattr(universe, "min_informational_mass", 0.1),
        "H_target": getattr(universe, "qast_input_target_H_symb", 0.5),
        "Kp": getattr(universe, "qast_input_Kp", 0.5),
        "Ki": getattr(universe, "qast_input_Ki", 0.1),
        "stop_window": getattr(universe, "qast_stop_window", stable_cycles),
        "H_var_threshold": getattr(universe, "qast_H_var_threshold", entropy_threshold),
    }
    if qast_hyperparams_override:
        qast_params_dict.update(qast_hyperparams_override)

    try:
        qast_hyper = HyperParams(**qast_params_dict)
    except TypeError as e_hyper_init:
        logger.error(
            "Erro ao inicializar HyperParams com %s: %s. Usando defaults.",
            qast_params_dict,
            e_hyper_init,
        )
        qast_hyper = HyperParams()
        qast_params_dict["alpha_coeffs"] = qast_hyper.alpha_coeffs
        qast_params_dict["mass_min"] = qast_hyper.mass_min
        qast_params_dict["Kp"] = qast_hyper.Kp
        qast_params_dict["Ki"] = qast_hyper.Ki

    pid_controller = PIDController(
        Kp=qast_hyper.Kp, Ki=qast_hyper.Ki, i_max=qast_hyper.i_max
    )
    mass_manager = MassManager(
        alpha_coeffs=qast_params_dict["alpha_coeffs"],
        mass_min=qast_params_dict["mass_min"],
    )
    mass_manager.mass = universe.informational_mass

    H_symb_history_qast: Deque[float] = deque(maxlen=qast_hyper.stop_window)

    backend_sv = safe_get_backend(
        f"aer_simulator_{'density_matrix' if use_density_matrix else 'statevector'}",
        use_gpu=universe.use_gpu,
    )
    backend_counts = safe_get_backend("qasm_simulator", use_gpu=universe.use_gpu)

    if universe.current_sv is None and universe.initial_sv is not None:
        universe.current_sv = shallow_copy_statevector(universe.initial_sv)
    elif universe.current_sv is None:
        logger.warning(
            "run_with_qast_feedback: current_sv e initial_sv s\u00e3o None. Tentando criar estado inicial."
        )
        universe.current_sv = universe._create_initial_state(
            specific_type=initial_type_fallback
        )
        if universe.current_sv is not None:
            universe.initial_sv = shallow_copy_statevector(universe.current_sv)
        else:
            raise RuntimeError("Failed to create initial quantum state")

    return (
        add_metric,
        get_metrics,
        qast_hyper,
        pid_controller,
        mass_manager,
        H_symb_history_qast,
        backend_sv,
        backend_counts,
    )


def _execute_qast_cycle(
    universe: "QUALIAQuantumUniverse",
    cycle_index: int,
    *,
    qast_hyper: "HyperParams",
    pid_controller: "PIDController",
    mass_manager: "MassManager",
    H_symb_history_qast: Deque[float],
    backend_sv: Any,
    backend_counts: Any,
    add_metric: Callable[[str, Any], None],
    encoded_circuit: Optional[QuantumCircuit],
    encoded_market_inputs: Optional[Dict[str, Any]],
    shots: int,
    measure_frequency: int,
    thermal: bool,
    retro_mode: str,
    initial_type_fallback: str,
    qast_influence_factor: float,
    stable_cycles: int,
    quantum_threshold: float,
    H_symb_prime_history: List[float],
    S_quantum_history: List[float],
    scr_depth_history: List[int],
    temperature_history: List[float],
    prev_quantum_entropy: Optional[float],
    stable_counter: int,
) -> Tuple[bool, Optional[float], int, Optional[QuantumCircuit]]:
    """Execute a single QAST feedback cycle."""

    from ..core.qast_core import SymbolicEntropy

    logger.debug("--- Ciclo QAST/Universo %s ---", cycle_index + 1)

    qast_input_to_use = universe.current_qast_input_value
    if universe.control_mode == "PID":
        if universe.qast_input_control_enabled:
            current_H_symb_prime_for_pid = (
                H_symb_history_qast[-1] if H_symb_history_qast else qast_hyper.H_target
            )
            error_H_symb = qast_hyper.H_target - current_H_symb_prime_for_pid
            qast_input_from_pid = pid_controller.update(error_H_symb)
            universe.current_qast_input_value = qast_input_from_pid
            qast_input_to_use = universe.current_qast_input_value
        elif encoded_market_inputs and "qast_input_value" in encoded_market_inputs:
            qast_input_to_use = encoded_market_inputs["qast_input_value"]
            universe.current_qast_input_value = qast_input_to_use
    elif universe.control_mode == "LQR":
        if (
            hasattr(universe, "lqr_K_gain")
            and universe.lqr_K_gain is not None
            and hasattr(universe, "target_informational_mass")
            and universe.target_informational_mass is not None
            and hasattr(universe, "target_H_symb_prime")
            and universe.target_H_symb_prime is not None
        ):
            current_H_symb_prime_for_lqr = (
                H_symb_history_qast[-1] if H_symb_history_qast else 0.5
            )
            x_k = np.array(
                [
                    universe.informational_mass - universe.target_informational_mass,
                    current_H_symb_prime_for_lqr - universe.target_H_symb_prime,
                ]
            )
            try:
                u_k = -np.dot(universe.lqr_K_gain, x_k)
                delta_scr_depth = u_k[0] if len(u_k) > 0 else 0.0
                delta_temperature = u_k[1] if len(u_k) > 1 else 0.0
                universe.scr_depth = int(
                    np.clip(
                        universe.scr_depth + delta_scr_depth, 1, universe.scr_depth * 2
                    )
                )
                universe.temperature = float(
                    np.clip(universe.temperature + delta_temperature * 0.01, 0.001, 0.5)
                )
                logger.debug(
                    "LQR Control: x_k=%s, u_k=%s, new scr_depth=%s, new temp=%s",
                    x_k,
                    u_k,
                    universe.scr_depth,
                    universe.temperature,
                )
            except Exception as e_lqr:
                logger.warning("Erro ao aplicar controle LQR: %s", e_lqr)
        else:
            logger.debug(
                "LQR: Par\u00e2metros de controle n\u00e3o configurados. Mantendo valores atuais."
            )
    elif universe.control_mode == "OPEN_LOOP":
        if encoded_market_inputs and "qast_input_value" in encoded_market_inputs:
            qast_input_to_use = encoded_market_inputs["qast_input_value"]
            universe.current_qast_input_value = qast_input_to_use

    if qast_hyper.max_degree == 2:
        qast_coeffs = np.array([1.0, qast_input_to_use, float(cycle_index + 1)])
    elif qast_hyper.max_degree == 1:
        qast_coeffs = np.array([qast_input_to_use, float(cycle_index + 1)])
    else:
        qast_coeffs = np.array([qast_input_to_use])

    current_H_symb_prime = SymbolicEntropy.compute(qast_coeffs)
    if current_H_symb_prime is None:
        current_H_symb_prime = 0.0
        logger.warning(
            "SymbolicEntropy.compute retornou None no ciclo %s. Usando 0.0.",
            cycle_index + 1,
        )

    H_symb_history_qast.append(current_H_symb_prime)
    add_metric("page_entropy", current_H_symb_prime)

    effective_scr_depth_for_circuit = universe.scr_depth
    effective_temperature_for_circuit = universe.temperature

    if universe.control_mode == "PID":
        mass_effect_scr = (
            (mass_manager.mass / universe.initial_informational_mass)
            if universe.initial_informational_mass > 1e-9
            else 0.0
        )
        delta_H_symb_prime_val = 0.0
        if len(H_symb_history_qast) >= 2:
            delta_H_symb_prime_val = H_symb_history_qast[-1] - H_symb_history_qast[-2]

        stagnation_effect_scr = 1.0 - abs(np.tanh(delta_H_symb_prime_val * 5.0))
        mass_influence_factor = qast_influence_factor
        scr_depth_modulator = (1 - mass_influence_factor) * (
            universe.scr_depth * mass_effect_scr
        ) + mass_influence_factor * (
            universe.scr_depth * (1 + stagnation_effect_scr * 0.5)
        )
        effective_scr_depth_for_circuit = int(
            np.clip(scr_depth_modulator, 1, universe.scr_depth * 2)
        )
        temp_modulator = (1 - mass_influence_factor) * (
            universe.temperature * mass_effect_scr
        ) + mass_influence_factor * (
            universe.temperature * (1 + stagnation_effect_scr * 0.2)
        )
        effective_temperature_for_circuit = np.clip(
            temp_modulator, 0.001, universe.temperature * 3
        )
        logger.debug(
            "Ciclo %s [PID]: M/M0=%.3f, Stagnation_scr=%.3f -> DynScrDepth=%s, DynTemp=%.4f",
            cycle_index + 1,
            mass_effect_scr,
            stagnation_effect_scr,
            effective_scr_depth_for_circuit,
            effective_temperature_for_circuit,
        )

    qc_this_cycle = universe.build_circuit(
        cycle_index + 1,
        thermal=thermal,
        temperature=effective_temperature_for_circuit,
        retro_mode=retro_mode,
        initial_encoded_circuit=encoded_circuit if cycle_index == 0 else None,
        initial_type_fallback=initial_type_fallback,
        measure_frequency=measure_frequency,
        qast_input_for_circuit=qast_input_to_use,
        scr_depth_override=effective_scr_depth_for_circuit,
        check_mid_circuit=cycle_index == 0,
    )

    current_sv_obj: Optional[Statevector] = None
    counts_this_cycle: Dict[str, int] = {}
    if (
        backend_sv
        and hasattr(backend_sv, "run")
        and backend_counts
        and hasattr(backend_counts, "run")
    ):
        try:
            qc_sv_run = qc_this_cycle.copy()
            try:
                qc_sv_run.remove_final_measurements(inplace=True)
            except Exception as e:
                logger.warning("run: remove_final_measurements falhou: %s", e)
            sv_label = f"sv_cycle_{cycle_index}"
            sv_result = backend_sv.run(qc_sv_run).result()
            current_sv_obj = sv_result.get_statevector(sv_label)
            universe.current_sv = current_sv_obj
            if cycle_index == 0 and universe.current_sv is not None:
                universe.initial_sv = shallow_copy_statevector(universe.current_sv)

            counts_result = backend_counts.run(qc_this_cycle, shots=shots).result()
            counts_this_cycle = counts_result.get_counts()
            universe._last_counts = counts_this_cycle
            if counts_this_cycle:
                unique_outcomes_tick = len(counts_this_cycle)
                total_shots_tick = sum(counts_this_cycle.values())
                diversity_tick = unique_outcomes_tick / float(total_shots_tick)
                add_metric("counts_diversity", diversity_tick)
                universe.on_tick_metrics({"counts_diversity": diversity_tick})
        except Exception as e_q_exec:
            logger.error(
                "Erro na execu\u00e7\u00e3o qu\u00e2ntica do ciclo %s: %s",
                cycle_index + 1,
                e_q_exec,
            )
            current_sv_obj = universe.current_sv
            counts_this_cycle = universe._last_counts
    else:
        logger.warning(
            "Backends de simula\u00e7\u00e3o n\u00e3o dispon\u00edveis. Usando mocks."
        )
        current_sv_obj = universe.current_sv
        num_q_circuit = qc_this_cycle.num_qubits if qc_this_cycle else universe.n_qubits
        counts_this_cycle = {
            bin(i)[2:].zfill(num_q_circuit): shots // (2**num_q_circuit)
            for i in range(2**num_q_circuit)
            if num_q_circuit > 0
        }
        if counts_this_cycle:
            unique_outcomes_tick = len(counts_this_cycle)
            total_shots_tick = sum(counts_this_cycle.values())
            diversity_tick = unique_outcomes_tick / float(total_shots_tick)
            add_metric("counts_diversity", diversity_tick)
            universe.on_tick_metrics({"counts_diversity": diversity_tick})

    quantum_entropy_val = None
    coherence_l1_val = None
    otoc_val = None
    if current_sv_obj:
        try:
            psi_vector = np.asarray(current_sv_obj.data, dtype=complex)
            rho_matrix = np.outer(psi_vector, np.conj(psi_vector))
            sum_abs_all_elements = np.sum(np.abs(rho_matrix))
            sum_abs_diag_elements = np.sum(np.abs(np.diag(rho_matrix)))
            coherence_l1_val = float(sum_abs_all_elements - sum_abs_diag_elements)
            add_metric("quantum_coherence", coherence_l1_val)
        except Exception as e_coh:
            logger.debug(
                "Erro c\u00e1lculo coer\u00eancia ciclo %s: %s", cycle_index + 1, e_coh
            )

        try:
            quantum_entropy_val = (
                entropy(current_sv_obj, base=2) if current_sv_obj else 0.0
            )
            add_metric("quantum_entropy", quantum_entropy_val)
            logger.debug(
                "Ciclo %s: Quantum entropy=%.6f", cycle_index + 1, quantum_entropy_val
            )
        except Exception as e_qent:
            logger.debug(
                "Erro c\u00e1lculo q_entropy ciclo %s: %s", cycle_index + 1, e_qent
            )

        try:
            num_q_otoc = (
                qc_this_cycle.num_qubits if qc_this_cycle else universe.n_qubits
            )
            if num_q_otoc > 0:
                otoc_val = universe.calculate_otoc(
                    statevector=current_sv_obj,
                    time_step=cycle_index,
                    n_qubits=num_q_otoc,
                    target_qubits=[0],
                )
                add_metric("otoc", otoc_val)
        except Exception as e_otoc:
            logger.debug("Erro c\u00e1lculo OTOC ciclo %s: %s", cycle_index + 1, e_otoc)
    else:
        add_metric("quantum_coherence", 0.0)
        add_metric("quantum_entropy", 0.5)
        add_metric("otoc", 0.5)

    coherence_for_mass = coherence_l1_val if coherence_l1_val is not None else 0.0
    mass_manager.update(current_H_symb_prime, coherence_for_mass)
    current_mass = mass_manager.mass
    add_metric("informational_mass", current_mass)

    H_symb_prime_history.append(current_H_symb_prime)
    S_quantum_history.append(
        quantum_entropy_val if quantum_entropy_val is not None else -1.0
    )
    scr_depth_history.append(universe.scr_depth)
    temperature_history.append(universe.temperature)

    current_q_entropy_for_stable = (
        quantum_entropy_val if quantum_entropy_val is not None else 0.0
    )
    if (
        prev_quantum_entropy is not None
        and len(H_symb_history_qast) >= qast_hyper.stop_window
    ):
        delta_symbolic_prime_window = np.ptp(list(H_symb_history_qast))
        delta_quantum_abs = abs(current_q_entropy_for_stable - prev_quantum_entropy)
        if (
            delta_symbolic_prime_window < qast_hyper.H_var_threshold
            and delta_quantum_abs < quantum_threshold
        ):
            stable_counter += 1
        else:
            stable_counter = 0
    prev_quantum_entropy = current_q_entropy_for_stable

    if stable_counter >= stable_cycles:
        logger.info(
            "Estabilidade h\u00edbrida detectada ap\u00f3s %s ciclos.", cycle_index + 1
        )
        return False, prev_quantum_entropy, stable_counter, qc_this_cycle

    if mass_manager.mass <= mass_manager.mass_min:
        logger.info(
            "Massa informacional (%s) atingiu limiar (%s) ap\u00f3s %s ciclos.",
            mass_manager.mass,
            mass_manager.mass_min,
            cycle_index + 1,
        )
        return False, prev_quantum_entropy, stable_counter, qc_this_cycle

    return True, prev_quantum_entropy, stable_counter, qc_this_cycle
