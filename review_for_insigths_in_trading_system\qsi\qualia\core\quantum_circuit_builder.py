"""Rotinas de criação de circuitos quânticos para o QUALIA."""

from __future__ import annotations

from qiskit import QuantumCircuit, ClassicalRegister
from qiskit.circuit.library import QFT as DefaultQFTGate

from ..utils.logger import get_logger

logger = get_logger(__name__)


def create_qft_circuit(
    n_qubits: int,
    add_measurements: bool = True,
    *,
    gate_cls=DefaultQFTGate,
) -> QuantumCircuit:
    """Cria um circuito QFT padrão."""
    if n_qubits <= 0:
        logger.warning(
            "Solicitado circuito QFT para %s qubits. Retornando circuito vazio.",
            n_qubits,
        )
        return QuantumCircuit(0)

    qc = QuantumCircuit(n_qubits)
    try:
        qft_op = gate_cls(n_qubits)
        qc.append(qft_op, range(n_qubits))
        if add_measurements:
            qc.measure_all()
        logger.info("Circuito QFT para %s qubits criado com sucesso.", n_qubits)
    except Exception as exc:  # pragma: no cover - defensive
        logger.error(
            "Erro inesperado ao criar QFTGate em create_qft_circuit para %s qubits: %s",
            n_qubits,
            exc,
        )
        return QuantumCircuit(n_qubits)
    return qc


def create_grover_circuit(
    n_qubits: int, target_state: str, add_measurements: bool = True
) -> QuantumCircuit:
    """Cria um circuito de busca de Grover."""
    import numpy as np

    if n_qubits <= 0:
        raise ValueError("n_qubits must be positive")
    if len(target_state) != n_qubits:
        raise ValueError("target_state length must match n_qubits")
    if not set(target_state).issubset({"0", "1"}):
        raise ValueError("target_state must contain only '0' or '1'")

    qc = QuantumCircuit(n_qubits)
    qc.h(range(n_qubits))
    iterations = int(np.pi / 4 * np.sqrt(2**n_qubits))
    for _ in range(iterations):
        rev_state = target_state[::-1]
        for i, bit in enumerate(rev_state):
            if bit == "0":
                qc.x(i)
        qc.h(n_qubits - 1)
        qc.mcx(list(range(n_qubits - 1)), n_qubits - 1)
        qc.h(n_qubits - 1)
        for i, bit in enumerate(rev_state):
            if bit == "0":
                qc.x(i)
        qc.h(range(n_qubits))
        qc.x(range(n_qubits))
        qc.h(n_qubits - 1)
        qc.mcx(list(range(n_qubits - 1)), n_qubits - 1)
        qc.h(n_qubits - 1)
        qc.x(range(n_qubits))
        qc.h(range(n_qubits))
    if add_measurements:
        creg = ClassicalRegister(n_qubits)
        qc.add_register(creg)
        qc.measure(range(n_qubits), range(n_qubits))
    return qc


def create_vqe_circuit(
    n_qubits: int, depth: int, add_measurements: bool = True
) -> QuantumCircuit:
    """Cria um circuito ansatz para VQE."""
    qc = QuantumCircuit(n_qubits)
    qc.h(range(n_qubits))
    for _ in range(depth):
        for q in range(n_qubits):
            qc.rx(0.1, q)
            qc.ry(0.1, q)
            qc.rz(0.1, q)
        for q in range(n_qubits - 1):
            qc.cx(q, q + 1)
    if add_measurements:
        creg = ClassicalRegister(n_qubits)
        qc.add_register(creg)
        qc.measure(range(n_qubits), range(n_qubits))
    return qc
