# src/qualia/core/simulation_qast_core.py

from __future__ import annotations

import os
import time
from typing import Any, Dict, List
from dataclasses import dataclass, field

try:  # Optional dependency
    import psutil
except Exception:  # pragma: no cover - fallback when psutil is absent
    psutil = None

import numpy as np
from sklearn.cluster import KMeans

from ..personas.base import BasePersona
from ..farsight.holographic_extension import HolographicFarsightEngine
from ..consciousness.social_simulation_universe import SocialSimulationUniverse
from ..utils.logger import get_logger
from ..utils.metrics import record_metric

logger = get_logger(__name__)


@dataclass
class FutureScenario:
    """Represents a potential future outcome with its associated probability."""

    scenario_id: str
    probability: float
    description: str
    dominant_patterns: List[Any] = field(default_factory=list)


@dataclass
class FutureProbabilityMap:
    """Represents a map of possible future scenarios at a given time."""

    timestamp: float
    scenarios: List[FutureScenario]


class SimulationQASTCore:
    """
    Motor de simulação de futuros (Future Simulation Engine).

    Orquestra simulações de Monte Carlo das interações de agentes (Personas)
    para prever múltiplos futuros possíveis e suas probabilidades.
    """

    def __init__(
        self,
        personas: List[BasePersona],
        farsight_engine: HolographicFarsightEngine,
        social_universe: SocialSimulationUniverse,
        config: Dict[str, Any],
    ):
        self.personas = personas
        self.farsight = farsight_engine
        self.universe = social_universe
        self.config = config
        self.simulation_cycles = self.config.get("simulation_cycles", 100)
        self.simulation_steps_per_cycle = self.config.get(
            "simulation_steps_per_cycle", 10
        )
        self.dominant_patterns_per_scenario = self.config.get(
            "dominant_patterns_per_scenario", 3
        )
        logger.info(
            f"SimulationQASTCore inicializado com {len(self.personas)} personas."
        )

    def run_simulation_cycle(self, market_data: Dict[str, Any]) -> FutureProbabilityMap:
        """
        Executa um ciclo completo de simulação de Monte Carlo.

        Args:
            market_data: Dados de mercado atuais para alimentar as personas.

        Returns:
            Um mapa de probabilidades de futuros possíveis.
        """
        logger.info(
            f"Iniciando ciclo de simulação com {self.simulation_cycles} iterações."
        )

        start_perf = time.perf_counter()
        mem_before = psutil.Process(os.getpid()).memory_info().rss if psutil else 0

        # 1. Obter o estado mental coletivo que influenciará a simulação
        collective_mind_state = self.farsight.generate_collective_mind_state()

        final_universe_states = []

        for i in range(self.simulation_cycles):
            # Resetar o universo para cada simulação de Monte Carlo
            self.universe.reset_field()
            logger.debug(
                f"Simulação Monte Carlo - Ciclo {i+1}/{self.simulation_cycles}"
            )

            # 2. Simular a evolução do ecossistema por um número de passos
            for step in range(self.simulation_steps_per_cycle):
                timestamp = time.time()
                # a. Atualizar o estado de cada persona
                for persona in self.personas:
                    persona.update_state(
                        market_data, {"collective_mind_state": collective_mind_state}
                    )

                # b. Obter ações prováveis e injetar no universo
                for persona in self.personas:
                    probable_action = persona.get_probable_action(market_data)
                    self.universe.inject_persona_action_event(
                        persona, probable_action, timestamp
                    )

                # c. Evoluir o universo social
                self.universe.step_evolution(timestamp)

            # 3. Salvar o estado final do campo de influência
            final_universe_states.append(self.universe.get_current_field().flatten())

        # 4. Analisar os resultados da simulação
        logger.info(
            "Analisando resultados da simulação para identificar cenários futuros."
        )
        future_scenarios = self._cluster_and_analyze_outcomes(final_universe_states)

        # 5. Gerar o mapa de probabilidade de futuros
        future_map = FutureProbabilityMap(
            timestamp=time.time(),
            scenarios=future_scenarios,
        )

        duration_ms = (time.perf_counter() - start_perf) * 1000
        mem_after = (
            psutil.Process(os.getpid()).memory_info().rss if psutil else mem_before
        )
        record_metric("simulation.cycle_duration_ms", duration_ms)
        if psutil:
            record_metric("simulation.cycle_memory_mb", mem_after / 1024**2)
            record_metric(
                "simulation.cycle_memory_delta_mb",
                (mem_after - mem_before) / 1024**2,
            )

        return future_map

    def _cluster_and_analyze_outcomes(
        self, final_states: List[np.ndarray]
    ) -> List[FutureScenario]:
        """
        Agrupa os estados finais da simulação em clusters (cenários) e calcula suas probabilidades.

        Args:
            final_states: Uma lista dos estados do campo de influência ao final de cada simulação.

        Returns:
            Uma lista de FutureScenarios.
        """
        if not final_states:
            return []

        start_perf = time.perf_counter()

        # Usar K-Means para agrupar os resultados em um número definido de cenários
        n_clusters = self.config.get("future_scenario_clusters", 3)
        kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
        clusters = kmeans.fit_predict(np.array(final_states))

        scenarios = []
        total_simulations = len(final_states)

        analysis_start = time.perf_counter()
        pattern_count = int(self.dominant_patterns_per_scenario)

        for i in range(n_clusters):
            cluster_indices = np.where(clusters == i)[0]
            probability = len(cluster_indices) / total_simulations

            if probability == 0:
                continue

            centroid = kmeans.cluster_centers_[i]
            top_indices = np.argsort(np.abs(centroid))[::-1][:pattern_count]
            dominant_patterns = [
                {"feature_idx": int(idx), "strength": float(centroid[idx])}
                for idx in top_indices
            ]

            feature_list = ", ".join(f"f{idx}" for idx in top_indices)
            scenario_description = (
                f"Cenário {i+1}: Features dominantes {feature_list}"
                f" com {probability:.2%} de probabilidade."
            )

            scenario = FutureScenario(
                scenario_id=f"scenario_{i}",
                probability=probability,
                description=scenario_description,
                dominant_patterns=dominant_patterns,
            )
            scenarios.append(scenario)
            logger.info(f"Cenário detectado: {scenario_description}")

        analysis_duration_ms = (time.perf_counter() - analysis_start) * 1000
        record_metric("simulation.dominant_pattern_analysis_ms", analysis_duration_ms)

        duration_ms = (time.perf_counter() - start_perf) * 1000
        record_metric("simulation.cluster_duration_ms", duration_ms)

        return sorted(scenarios, key=lambda s: s.probability, reverse=True)

    def run_internal_reality_simulation(
        self, market_data: Dict[str, Any] | None = None
    ) -> Dict[str, Any]:
        """
        Executa o Loop de Consciência Social para determinar o estado de equilíbrio.

        Este método simula a interação das Personas no ``SocialSimulationUniverse``
        até que os resumos consecutivos do campo de influência apresentem
        variação abaixo de ``internal_stability_threshold``.
        A diferença entre resumos é calculada pela distância Euclidiana entre os
        valores numéricos de cada dicionário, representando um consenso ou um
        estado de reflexão interna consolidado.

        Returns:
            O estado de equilíbrio do campo de influência social.
        """
        max_cycles = int(
            self.config.get("max_internal_cycles", self.simulation_steps_per_cycle)
        )
        stability_threshold = float(
            self.config.get("internal_stability_threshold", 1e-4)
        )
        logger.info(
            "Iniciando simulação da realidade interna por até %s ciclos.", max_cycles
        )

        accumulated_vector = np.zeros(3, dtype=float)
        field_summary = self.universe.get_field_summary()
        previous_summary = dict(field_summary)

        for i in range(max_cycles):
            persona_actions: list[tuple[BasePersona, Dict[str, float]]] = []

            for persona in self.personas:
                persona.update_state(
                    market_data or {}, {"field_summary": field_summary}
                )
                action = persona.get_probable_action(market_data or {})
                persona_actions.append((persona, action))

                vec = np.array(
                    [
                        float(action.get("BUY", 0.0)),
                        float(action.get("SELL", 0.0)),
                        float(action.get("HOLD", 0.0)),
                    ],
                    dtype=float,
                )
                accumulated_vector += vec

            timestamp = time.time()
            for persona, action in persona_actions:
                self.universe.inject_persona_action_event(persona, action, timestamp)

            self.universe.step_evolution(timestamp)
            field_summary = self.universe.get_field_summary()

            numeric_keys = sorted(
                {k for k, v in field_summary.items() if isinstance(v, (int, float))}
                | {
                    k
                    for k, v in previous_summary.items()
                    if isinstance(v, (int, float))
                }
            )

            if numeric_keys:
                current_vals = np.array(
                    [float(field_summary.get(k, 0.0)) for k in numeric_keys],
                    dtype=float,
                )
                prev_vals = np.array(
                    [float(previous_summary.get(k, 0.0)) for k in numeric_keys],
                    dtype=float,
                )
                field_change = float(np.linalg.norm(current_vals - prev_vals))
            else:
                field_change = 0.0
            previous_summary = dict(field_summary)

            record_metric("internal_reality.field_change", field_change)
            logger.debug(
                "Iteração %s/%s - Mudança do Campo: %.6f",
                i + 1,
                max_cycles,
                field_change,
            )

            if field_change < stability_threshold:
                logger.info("Equilíbrio interno atingido na iteração %s", i + 1)
                break
        else:
            logger.warning(
                "Simulação interna alcançou o limite de ciclos sem completa estabilidade"
            )

        total = float(accumulated_vector.sum())
        if total > 0.0:
            intention_vector = (accumulated_vector / total).tolist()
        else:
            intention_vector = [1.0 / 3.0] * 3

        return {
            "intention_vector": intention_vector,
            "field_summary": field_summary,
            "current_field": (
                self.universe.get_current_field()
                if hasattr(self.universe, "get_current_field")
                else np.asarray(getattr(self.universe, "current_field", np.zeros(1)))
            ),
        }
