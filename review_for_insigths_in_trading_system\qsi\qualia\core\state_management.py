"""Helper utilities for quantum state management.

These functions extract :class:`qiskit.quantum_info.Statevector` objects
from experiment results and provide helper routines for caching and
serialization.

Examples
--------
>>> sv = get_state_representation(Statevector([1, 0]))
>>> key = generate_circuit_cache_key(1, False, 0.0, "none", 1, 0, False, 1)
"""

from __future__ import annotations

from ..utils.logger import get_logger
from typing import Any, Callable, Optional

from qiskit.quantum_info import Statevector
from qiskit.exceptions import QiskitError
import numpy as np

logger = get_logger(__name__)

# Mapping of representation formats to extraction callables
REPRESENTATION_FUNCS: dict[str, Callable[[Statevector], Any]] = {
    "statevector": lambda sv: sv.data,
    "density_matrix": lambda sv: sv.to_operator().data,
    "probabilities": lambda sv: sv.probabilities(),
    "probabilities_dict": lambda sv: sv.probabilities_dict(),
}

# Known snapshot labels that may contain statevector data
SNAPSHOT_LABELS = ["final_run_state", "statevector"]


def generate_circuit_cache_key(
    steps: int,
    thermal: bool,
    temperature: float,
    retro_mode: str,
    measure_frequency: int,
    scr_depth: int,
    apply_scrambling: bool,
    n_qubits: int,
) -> str:
    """Cria uma chave única para o cache de circuitos baseada nos parâmetros de construção."""
    key_parts = [
        f"steps_{steps}",
        f"thermal_{thermal}",
        f"temp_{temperature:.4f}",
        f"retro_{retro_mode}",
        f"meas_freq_{measure_frequency}",
        f"scr_{scr_depth}",
        f"scramble_{apply_scrambling}",
        f"n_qubits_{n_qubits}",
    ]
    return "_".join(key_parts)


def clear_circuit_cache(cache: dict) -> None:
    """Esvazia o cache de circuitos informado."""
    cache.clear()
    logger.info("Cache de circuitos limpo")


def shallow_copy_statevector(statevector: Statevector) -> Statevector:
    """Return a new :class:`Statevector` sharing the same data array.

    This avoids the deep copy performed by :meth:`Statevector.copy` while
    still creating an independent wrapper object.
    """
    try:
        dims = statevector.dims()
        return Statevector(statevector.data, dims=dims)
    except (AttributeError, TypeError):
        return Statevector(statevector.data)


def get_state_representation(
    statevector: Optional[Statevector], format_type: str = "statevector"
) -> Optional[Any]:
    """Retorna diferentes representações de um ``Statevector``.

    Parameters
    ----------
    statevector:
        ``Statevector`` a ser representado.
    format_type:
        Tipo de representação: ``statevector``, ``density_matrix``,
        ``probabilities`` ou ``probabilities_dict``.

    Returns
    -------
    Optional[Any]
        Representação do estado ou ``None`` se indisponível.
    """
    if statevector is None:
        logger.warning(
            "QUALIAQuantumUniverse: current_sv é None. Não é possível obter a representação do estado."
        )
        return None

    try:
        extractor = REPRESENTATION_FUNCS.get(format_type)
        if extractor is None:
            logger.warning(
                (
                    "QUALIAQuantumUniverse: Formato de representação '%s' não é "
                    "suportado. Use 'statevector', 'density_matrix', 'probabilities', "
                    "ou 'probabilities_dict'."
                ),
                format_type,
            )
            return None
        return extractor(statevector)
    except Exception as exc:  # broad catch to preserve legacy behaviour
        logger.error(
            "QUALIAQuantumUniverse: Erro ao obter representação do estado como '%s': %s",
            format_type,
            exc,
            exc_info=True,
        )
        return None


def try_get_statevector_direct(
    result: Any, circuit_ref: Optional[Any] = None
) -> Optional[Statevector]:
    """Tenta obter um ``Statevector`` usando métodos ``get_statevector``."""
    sv = None
    if hasattr(result, "get_statevector"):
        try:
            if circuit_ref is not None:
                sv = result.get_statevector(circuit_ref)
                logger.debug(
                    "try_get_statevector_direct: get_statevector(circuit_ref) returned %s",
                    type(sv),
                )
                if sv is not None:
                    return sv
            if sv is None:
                sv = result.get_statevector()
                logger.debug(
                    "try_get_statevector_direct: get_statevector() returned %s",
                    type(sv),
                )
                if sv is not None:
                    return sv
        except (TypeError, ValueError, QiskitError) as exc:
            logger.debug(
                "try_get_statevector_direct: error calling get_statevector: %s",
                exc,
            )
    return None


def get_data_dict(exp_data: Any) -> Optional[dict]:
    """Converte os dados de um experimento em dicionário, se possível."""
    if isinstance(exp_data, dict):
        return exp_data
    if hasattr(exp_data, "to_dict") and callable(exp_data.to_dict):
        try:
            return exp_data.to_dict()
        except (TypeError, ValueError, QiskitError) as exc:
            logger.warning("Falha ao chamar to_dict() em exp_data: %s", exc)
            return None
    logger.warning(
        "exp_data do tipo %s não é um dicionário nem possui método to_dict()",
        type(exp_data),
    )
    return None


def extract_from_snapshots(
    data_dict: dict, convert_fn: Callable[[Any], Optional[Statevector]]
) -> Optional[Statevector]:
    """Tenta obter um ``Statevector`` a partir de dados de snapshot do Qiskit."""
    if "snapshots" not in data_dict:
        return None
    snapshots = data_dict["snapshots"]
    if not isinstance(snapshots, dict) or "statevector" not in snapshots:
        return None
    sv_snapshots = snapshots["statevector"]
    if not isinstance(sv_snapshots, dict):
        return None
    for label in SNAPSHOT_LABELS:
        if label in sv_snapshots and sv_snapshots[label]:
            sv_data_list = sv_snapshots[label]
            if isinstance(sv_data_list, list) and sv_data_list:
                try:
                    return convert_fn(sv_data_list[-1])
                except (TypeError, ValueError, QiskitError) as exc:
                    logger.warning(
                        "Falha ao converter snapshot '%s' para Statevector: %s",
                        label,
                        exc,
                    )
                    return None
    return None


def convert_to_statevector(data: Any) -> Optional[Statevector]:
    """Converte dados arbitrários em um :class:`Statevector`, se possível."""
    if isinstance(data, Statevector):
        return data
    if isinstance(data, (np.ndarray, list)):
        try:
            return Statevector(data)
        except (TypeError, ValueError, QiskitError) as exc:
            logger.warning(
                "Falha ao converter %s para Statevector: %s", type(data), exc
            )
            return None
        except Exception:
            logger.exception("Erro inesperado ao converter para Statevector")
            raise
    else:
        logger.warning("Tipo não suportado para conversão: %s", type(data))
    return None


def extract_statevector_from_dict(
    data_dict: dict,
    exp_index: int,
    convert_fn: Callable[[Any], Optional[Statevector]],
) -> Optional[Statevector]:
    """Extrai um ``Statevector`` de um dicionário de dados produzido pelo Qiskit."""
    if "final_run_state" in data_dict:
        try:
            sv = convert_fn(data_dict["final_run_state"])
        except (TypeError, ValueError, QiskitError) as exc:
            logger.warning("Falha ao converter 'final_run_state': %s", exc)
            sv = None
        if sv:
            logger.info(
                "Statevector recuperado de data_dict['final_run_state'] para exp %s",
                exp_index,
            )
            return sv
    if "statevector" in data_dict:
        try:
            sv = convert_fn(data_dict["statevector"])
        except (TypeError, ValueError, QiskitError) as exc:
            logger.warning("Falha ao converter 'statevector': %s", exc)
            sv = None
        if sv:
            logger.info(
                "Statevector recuperado de data_dict['statevector'] para exp %s",
                exp_index,
            )
            return sv
    if "quantum_state" in data_dict:
        try:
            sv = convert_fn(data_dict["quantum_state"])
        except (TypeError, ValueError, QiskitError) as exc:
            logger.warning("Falha ao converter 'quantum_state': %s", exc)
            sv = None
        if sv:
            logger.info(
                "Statevector recuperado de data_dict['quantum_state'] para exp %s",
                exp_index,
            )
            return sv
    sv_from_snapshot = extract_from_snapshots(data_dict, convert_fn)
    if sv_from_snapshot is not None:
        logger.info(
            "Statevector recuperado de 'snapshots' em data_dict para exp %s",
            exp_index,
        )
        return sv_from_snapshot
    logger.debug(
        "Nenhuma chave de statevector conhecida encontrada em data_dict para exp %s. Chaves: %s",
        exp_index,
        list(data_dict.keys()),
    )
    return None


def try_get_statevector_from_data(result: Any) -> Optional[Statevector]:
    """Tenta extrair um ``Statevector`` a partir dos dados de resultado de um experimento."""
    if not hasattr(result, "data"):
        return None
    if callable(result.data):
        data_payload = result.data()
    else:
        data_payload = result.data
    if isinstance(data_payload, list):
        for i, exp_result in enumerate(data_payload):
            data_dict = get_data_dict(exp_result)
            if data_dict is None:
                continue
            sv = extract_statevector_from_dict(
                data_dict,
                i,
                convert_to_statevector,
            )
            if sv is not None:
                return sv
            if hasattr(exp_result, "data"):
                potential_sv_data = exp_result.data
                try:
                    sv = convert_to_statevector(potential_sv_data)
                except (TypeError, ValueError, QiskitError) as exc:
                    logger.warning(
                        "Falha ao converter exp_result.data para Statevector: %s",
                        exc,
                    )
                    sv = None
                if sv is not None:
                    logger.info(
                        "Statevector recuperado de exp_result.data para experimento %s",
                        i,
                    )
                    return sv
    else:
        data_dict = get_data_dict(data_payload)
        if data_dict:
            return extract_statevector_from_dict(
                data_dict,
                0,
                convert_to_statevector,
            )
    if hasattr(result, "get_statevector"):
        try:
            sv = result.get_statevector()
            if sv is not None:
                logger.info(
                    "Statevector recuperado via result.get_statevector() como fallback",
                )
                return sv
        except (TypeError, ValueError, QiskitError) as exc:
            logger.debug(
                "Falha ao usar result.get_statevector() como fallback: %s", exc
            )
    logger.debug(
        "try_get_statevector_from_data: não foi possível extrair statevector dos dados"
    )
    return None


# ---------------------------------------------------------------------------
# Operator state serialization utilities


def serialize_operator_state(operator: Any) -> dict:
    """Return a dictionary representation of an operator state."""
    if hasattr(operator, "get_state_dict"):
        return operator.get_state_dict()  # type: ignore[no-any-return]
    raise TypeError("operator must implement get_state_dict()")


def deserialize_operator_state(operator: Any, state: dict) -> None:
    """Load values from ``state`` into ``operator``."""
    for key, value in state.items():
        setattr(operator, key, value)


def save_operator_state(path: str, operator: Any) -> None:
    """Save operator state dictionary to ``path`` as JSON."""
    import json

    with open(path, "w", encoding="utf-8") as fh:
        json.dump(serialize_operator_state(operator), fh)


def load_operator_state(path: str) -> dict:
    """Load an operator state dictionary from ``path``."""
    import json

    with open(path, "r", encoding="utf-8") as fh:
        return json.load(fh)
