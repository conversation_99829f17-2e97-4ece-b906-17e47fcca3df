"""QuantumSymbolicTokenizer

Conversão de hotspots em tokens simbólicos de 24 bits (2×12 bits) com
tratamento de colisão. Migrado de *intention.py* (SIM-12).
"""
from __future__ import annotations

from typing import List

__all__ = ["QuantumSymbolicTokenizer"]


class QuantumSymbolicTokenizer:  # noqa: D101
    """Converte uma lista de *hotspots* em *tokens* simbólicos.

    • Cada token codifica índice e intensidade em 12 bits cada (24 bits totais).
    • Prefixo ``S`` garante início alfabético.
    • Colisões resolvidas por probing incremental.
    """

    MAX_VAL: int = 0xFFF  # 12-bit mask (4095)
    SCALE: float = 1000.0  # intensidade → 12 bits

    def _encode_int(self, val: int) -> str:
        """Retorna ``Sxxx`` onde *xxx* são 3 dígitos hex."""
        return f"S{val:03X}"

    def tokenize(self, hotspots: List[dict[str, float]] | None = None) -> List[str]:  # noqa: D401
        """Gera tokens únicos a partir de *hotspots*.

        Parameters
        ----------
        hotspots
            Lista de dicionários com chaves ``index`` e ``intensity``.
        """
        if not hotspots:
            return []

        tokens: list[str] = []
        used: set[str] = set()
        for hs in hotspots:
            idx_part = int(hs.get("index", 0)) & QuantumSymbolicTokenizer.MAX_VAL
            intensity_val = float(hs.get("intensity", hs.get("zscore", 0.0)))
            int_part = int(abs(intensity_val) * QuantumSymbolicTokenizer.SCALE) & QuantumSymbolicTokenizer.MAX_VAL

            base_token = f"{self._encode_int(idx_part)}{self._encode_int(int_part)}"
            unique_token = base_token
            suffix = 0
            while unique_token in used:
                suffix += 1
                unique_token = f"{base_token}{self._encode_int(suffix & QuantumSymbolicTokenizer.MAX_VAL)}"
            used.add(unique_token)
            tokens.append(unique_token)
        return tokens
