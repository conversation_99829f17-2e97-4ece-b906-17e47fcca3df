"""
Warmup Manager para QUALIA
Garante que o sistema tenha dados históricos suficientes antes de operar.
"""

import asyncio
import logging
import math
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Tuple, Union
import pandas as pd
from collections import defaultdict
import time
import os

from ..exchanges.kucoin_client import KuCoinClient
from ..utils.logger import get_logger

logger = get_logger(__name__)

# Configurações de requisitos mínimos de dados
DEFAULT_MIN_CANDLES = {
    "5m": 720,  # 720 candles de 5min ≈ 60 horas
    "15m": 240,  # 240 candles de 15min ≈ 60 horas
    "1h": 200,  # 200 candles de 1h ≈ 8 dias
    "4h": 24,  # 24 candles de 4h = 4 dias
}

# Mapeamento de timeframes para segundos
TIMEFRAME_TO_SECONDS = {
    "1m": 60,
    "5m": 300,
    "15m": 900,
    "30m": 1800,
    "1h": 3600,
    "4h": 14400,
    "1d": 86400,
}


class WarmupManager:
    """Gerencia o carregamento de dados históricos antes da inicialização do sistema."""

    def __init__(
        self,
        kucoin_client: KuCoinClient,
        min_candles_required: Optional[Dict[str, int]] = None,
        cache_dir: str = "data/warmup_cache",
        config: Optional[Dict[str, int]] = None,
    ):
        """
        Inicializa o gerenciador de warmup.

        Parameters
        ----------
        kucoin_client : KuCoinClient
            Cliente para acessar a API do KuCoin
        min_candles_required : Dict[str, int], optional
            Requisitos mínimos de candles por timeframe
        cache_dir : str
            Diretório para cache de dados
        """
        self.kucoin_client = kucoin_client
        self.min_candles_required = min_candles_required or DEFAULT_MIN_CANDLES
        self.cache_dir = cache_dir
        self.config = config or {}

        # Limite máximo de candles por requisição
        self.max_candles_per_fetch = int(self.config.get("max_candles_per_fetch", 1500))

        # Buffers de dados por símbolo e timeframe
        self.data_buffer: Dict[str, Dict[str, pd.DataFrame]] = defaultdict(dict)

        # Status de carregamento
        self.loading_status: Dict[str, Dict[str, dict]] = defaultdict(dict)

        # Criar diretório de cache se não existir
        os.makedirs(cache_dir, exist_ok=True)

        logger.info(
            "WarmupManager inicializado com requisitos: %s", self.min_candles_required
        )
        logger.info(
            "Limite de %d candles por chamada (pagina\u00e7\u00e3o YAA-02)",
            self.max_candles_per_fetch,
        )

    async def load_historical_data(
        self, symbols: List[str], timeframes: List[str], force_reload: bool = False
    ) -> bool:
        """
        Carrega dados históricos para todos os símbolos e timeframes.

        Parameters
        ----------
        symbols : List[str]
            Lista de símbolos para carregar (ex: ["BTCUSDT", "ETHUSDT"])
        timeframes : List[str]
            Lista de timeframes (ex: ["5m", "1h", "4h"])
        force_reload : bool
            Se True, ignora cache e recarrega da API

        Returns
        -------
        bool
            True se todos os dados foram carregados com sucesso
        """
        logger.info(
            "🔄 Iniciando warmup para %d símbolos em %d timeframes",
            len(symbols),
            len(timeframes),
        )

        tasks = []
        for symbol in symbols:
            for timeframe in timeframes:
                task = self._load_symbol_timeframe(symbol, timeframe, force_reload)
                tasks.append(task)

        # Executar todas as tarefas em paralelo
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Verificar resultados
        success_count = sum(1 for r in results if r is True)
        total = len(results)

        logger.info(
            "✅ Warmup concluído: %d/%d carregamentos bem-sucedidos",
            success_count,
            total,
        )

        # Log do relatório de status (sem print para evitar problemas de encoding)
        status_report = self.get_status_report()
        for line in status_report.split("\n"):
            if line.strip():
                logger.info(line)

        return success_count == total

    async def _load_symbol_timeframe(
        self, symbol: str, timeframe: str, force_reload: bool
    ) -> bool:
        """Carrega dados históricos para um símbolo/timeframe específico."""
        try:
            # Atualizar status
            self.loading_status[symbol][timeframe] = {
                "status": "loading",
                "progress": 0,
                "message": "Iniciando carregamento...",
            }

            # Verificar cache primeiro
            if not force_reload:
                cached_data = self._load_from_cache(symbol, timeframe)
                if cached_data is not None and len(
                    cached_data
                ) >= self.min_candles_required.get(timeframe, 100):
                    self.data_buffer[symbol][timeframe] = cached_data
                    self.loading_status[symbol][timeframe] = {
                        "status": "completed",
                        "progress": 100,
                        "message": f"Carregado do cache: {len(cached_data)} candles",
                    }
                    logger.info(
                        "📁 %s/%s carregado do cache (%d candles)",
                        symbol,
                        timeframe,
                        len(cached_data),
                    )
                    return True

            # Calcular período necessário
            min_candles = self.min_candles_required.get(timeframe, 100)
            seconds_per_candle = TIMEFRAME_TO_SECONDS.get(timeframe, 300)
            total_seconds = min_candles * seconds_per_candle

            # Adicionar margem de 20% para garantir
            total_seconds = int(total_seconds * 1.2)

            end_time = datetime.now(timezone.utc)
            start_time = end_time - timedelta(seconds=total_seconds)

            logger.info(
                "🌐 Carregando %s/%s da API (últimas %d horas)",
                symbol,
                timeframe,
                total_seconds / 3600,
            )

            # Carregar dados da API com paginação YAA-02
            all_data = []
            current_end = int(end_time.timestamp() * 1000)

            candles_loaded = 0
            chunk_number = 1
            total_chunks_estimated = max(
                1, math.ceil(min_candles / self.max_candles_per_fetch)
            )

            while candles_loaded < min_candles:
                chunk_size = min(
                    self.max_candles_per_fetch, min_candles - candles_loaded
                )
                safety = 1.2 if chunk_size <= 500 else 1.15
                batch_start = current_end - int(
                    chunk_size * seconds_per_candle * safety * 1000
                )

                self.loading_status[symbol][timeframe]["message"] = (
                    f"Carregando chunk {chunk_number}/{total_chunks_estimated}..."
                )

                logger.info(
                    f"📥 Chunk {chunk_number}/{total_chunks_estimated} {symbol}/{timeframe}: "
                    f"{chunk_size} candles (coletados {candles_loaded})"
                )

                if (
                    not hasattr(self.kucoin_client, "integration")
                    or not self.kucoin_client.integration
                ):
                    await self.kucoin_client.initialize()

                result = await self.kucoin_client.integration.fetch_ohlcv(
                    symbol=symbol,
                    timeframe=timeframe,
                    since=batch_start,
                    limit=chunk_size,
                )

                # fetch_ohlcv já retorna um DataFrame
                if isinstance(result, pd.DataFrame):
                    df = result
                    if df.empty:
                        logger.warning(
                            "⚠️ Nenhum dado retornado para %s/%s", symbol, timeframe
                        )
                        break
                else:
                    # Se for lista, converter para DataFrame
                    if not result:
                        logger.warning(
                            "⚠️ Nenhum dado retornado para %s/%s", symbol, timeframe
                        )
                        break
                    df = self._klines_to_dataframe(result)

                if df is not None and not df.empty:
                    all_data.insert(0, df)
                    candles_loaded = sum(len(d) for d in all_data)
                    current_end = int(df.index[0].timestamp() * 1000)
                    logger.info(
                        f"✅ Chunk {chunk_number} obtido: {len(df)} candles"
                    )
                    chunk_number += 1

                # Atualizar progresso
                progress = min(100, (candles_loaded / min_candles) * 100)
                self.loading_status[symbol][timeframe]["progress"] = progress

                # Verificar se chegamos ao início desejado
                if batch_start <= int(start_time.timestamp() * 1000):
                    break

                if candles_loaded < min_candles:
                    await asyncio.sleep(0.5)

            # Concatenar todos os dados
            if all_data:
                logger.debug(f"Concatenando {len(all_data)} DataFrames")
                try:
                    final_df = pd.concat(all_data, ignore_index=True)

                    # Garantir que temos a coluna timestamp
                    if "timestamp" in final_df.columns:
                        final_df = final_df.drop_duplicates(
                            subset=["timestamp"]
                        ).sort_values("timestamp")
                    else:
                        # Se não tiver timestamp mas tiver datetime index
                        final_df = final_df[
                            ~final_df.index.duplicated(keep="first")
                        ].sort_index()

                    final_df = final_df.reset_index(drop=True)
                except Exception as e:
                    logger.error(f"Erro ao concatenar DataFrames: {e}", exc_info=True)
                    return False

                # Armazenar no buffer
                self.data_buffer[symbol][timeframe] = final_df

                # Salvar no cache
                self._save_to_cache(symbol, timeframe, final_df)

                # Atualizar status
                self.loading_status[symbol][timeframe] = {
                    "status": "completed",
                    "progress": 100,
                    "message": f"Carregado com sucesso: {len(final_df)} candles",
                }

                logger.info(
                    "✅ %s/%s carregado com sucesso (%d candles)",
                    symbol,
                    timeframe,
                    len(final_df),
                )

                return True
            else:
                self.loading_status[symbol][timeframe] = {
                    "status": "failed",
                    "progress": 0,
                    "message": "Falha ao carregar dados da API",
                }
                return False

        except Exception as e:
            logger.error(
                "❌ Erro ao carregar %s/%s: %s",
                symbol,
                timeframe,
                str(e),
                exc_info=True,
            )
            self.loading_status[symbol][timeframe] = {
                "status": "error",
                "progress": 0,
                "message": f"Erro: {str(e)}",
            }
            return False

    def _klines_to_dataframe(self, klines: List[List]) -> pd.DataFrame:
        """Converte dados de klines da API para DataFrame."""
        try:
            if not klines:
                return pd.DataFrame()

            logger.debug(f"Convertendo {len(klines)} klines para DataFrame")

            # KuCoin kline format: [timestamp, open, close, high, low, volume, amount]
            df = pd.DataFrame(
                klines,
                columns=[
                    "timestamp",
                    "open",
                    "close",
                    "high",
                    "low",
                    "volume",
                    "amount",
                ],
            )

            logger.debug(f"DataFrame criado com shape: {df.shape}")

            # Converter tipos
            df["timestamp"] = pd.to_numeric(df["timestamp"], errors="coerce")
            # KuCoin retorna timestamps em milissegundos
            df["datetime"] = pd.to_datetime(df["timestamp"], unit="ms")
            df.set_index("datetime", inplace=True)

            # Converter preços para float
            for col in ["open", "close", "high", "low", "volume", "amount"]:
                df[col] = pd.to_numeric(df[col], errors="coerce")

            logger.debug(f"DataFrame processado com sucesso: {len(df)} linhas")
            return df

        except Exception as e:
            logger.error(f"Erro em _klines_to_dataframe: {e}", exc_info=True)
            return pd.DataFrame()

    def is_ready(
        self,
        symbols: Optional[List[str]] = None,
        timeframes: Optional[List[str]] = None,
    ) -> bool:
        """
        Verifica se temos dados suficientes para operar.

        Parameters
        ----------
        symbols : List[str], optional
            Símbolos para verificar (None = todos)
        timeframes : List[str], optional
            Timeframes para verificar (None = todos)

        Returns
        -------
        bool
            True se todos os requisitos foram atendidos
        """
        if symbols is None:
            symbols = list(self.data_buffer.keys())
        if timeframes is None:
            timeframes = list(self.min_candles_required.keys())

        for symbol in symbols:
            for timeframe in timeframes:
                if symbol not in self.data_buffer:
                    return False
                if timeframe not in self.data_buffer[symbol]:
                    return False

                df = self.data_buffer[symbol][timeframe]
                min_required = self.min_candles_required.get(timeframe, 100)

                if df is None or len(df) < min_required:
                    current_count = len(df) if df is not None else 0
                    logger.warning(
                        "❌ %s/%s: apenas %d/%d candles disponíveis",
                        symbol,
                        timeframe,
                        current_count,
                        min_required,
                    )
                    return False

        return True

    def get_warmup_progress(self) -> Dict[str, Dict[str, float]]:
        """
        Retorna o progresso do warmup por símbolo/timeframe.

        Returns
        -------
        Dict[str, Dict[str, float]]
            Progresso em percentual para cada combinação
        """
        progress = {}

        for symbol, timeframes in self.data_buffer.items():
            progress[symbol] = {}
            for timeframe, df in timeframes.items():
                min_required = self.min_candles_required.get(timeframe, 100)
                current = len(df) if df is not None else 0
                progress[symbol][timeframe] = min(100, (current / min_required) * 100)

        return progress

    def get_data(self, symbol: str, timeframe: str) -> Optional[pd.DataFrame]:
        """
        Retorna os dados carregados para um símbolo/timeframe.

        Parameters
        ----------
        symbol : str
            Símbolo desejado
        timeframe : str
            Timeframe desejado

        Returns
        -------
        pd.DataFrame or None
            DataFrame com os dados ou None se não disponível
        """
        return self.data_buffer.get(symbol, {}).get(timeframe)

    def get_all_data(self) -> Dict[str, Dict[str, pd.DataFrame]]:
        """Retorna todos os dados carregados."""
        return dict(self.data_buffer)

    def get_status_report(self) -> str:
        """Gera um relatório detalhado do status de warmup."""
        lines = ["WARMUP STATUS REPORT", "=" * 50]

        total_required = 0
        total_loaded = 0

        for symbol in sorted(self.data_buffer.keys()):
            lines.append(f"\n[{symbol}]:")

            for timeframe in sorted(self.data_buffer[symbol].keys()):
                df = self.data_buffer[symbol][timeframe]
                min_required = self.min_candles_required.get(timeframe, 100)
                current = len(df) if df is not None else 0

                total_required += min_required
                total_loaded += min(current, min_required)

                status = "[OK]" if current >= min_required else "[FAIL]"
                percentage = min(100, (current / min_required) * 100)

                lines.append(
                    f"  {status} {timeframe}: {current}/{min_required} "
                    f"({percentage:.1f}%)"
                )

                # Adicionar info de período coberto
                if df is not None and not df.empty:
                    # Verificar se o índice é datetime ou se temos coluna datetime
                    if hasattr(df.index[0], "strftime"):
                        start_date = df.index[0].strftime("%Y-%m-%d %H:%M")
                        end_date = df.index[-1].strftime("%Y-%m-%d %H:%M")
                    elif "datetime" in df.columns:
                        start_date = pd.to_datetime(df["datetime"].iloc[0]).strftime(
                            "%Y-%m-%d %H:%M"
                        )
                        end_date = pd.to_datetime(df["datetime"].iloc[-1]).strftime(
                            "%Y-%m-%d %H:%M"
                        )
                    elif "timestamp" in df.columns:
                        # Verificar se timestamp é numérico ou já é datetime
                        ts_first = df["timestamp"].iloc[0]
                        ts_last = df["timestamp"].iloc[-1]

                        try:
                            # Tentar converter como milissegundos
                            if isinstance(ts_first, (int, float)):
                                start_date = pd.to_datetime(
                                    ts_first, unit="ms"
                                ).strftime("%Y-%m-%d %H:%M")
                                end_date = pd.to_datetime(ts_last, unit="ms").strftime(
                                    "%Y-%m-%d %H:%M"
                                )
                            else:
                                # Já é string/datetime
                                start_date = pd.to_datetime(ts_first).strftime(
                                    "%Y-%m-%d %H:%M"
                                )
                                end_date = pd.to_datetime(ts_last).strftime(
                                    "%Y-%m-%d %H:%M"
                                )
                        except:
                            start_date = str(ts_first)[:19]
                            end_date = str(ts_last)[:19]
                    else:
                        start_date = "N/A"
                        end_date = "N/A"
                    lines.append(f"     Período: {start_date} até {end_date}")

        # Resumo geral
        overall_percentage = (
            (total_loaded / total_required * 100) if total_required > 0 else 0
        )
        lines.append(f"\nPROGRESSO GERAL: {overall_percentage:.1f}%")
        lines.append(f"   Total carregado: {total_loaded}/{total_required} candles")

        return "\n".join(lines)

    def _save_to_cache(self, symbol: str, timeframe: str, data: pd.DataFrame):
        """Salva dados no cache local."""
        try:
            cache_file = os.path.join(self.cache_dir, f"{symbol}_{timeframe}.csv")
            data.to_csv(cache_file, index=True)
            logger.debug("Cache salvo: %s", cache_file)
        except Exception as e:
            logger.warning("Erro ao salvar cache: %s", e)

    def _load_from_cache(self, symbol: str, timeframe: str) -> Optional[pd.DataFrame]:
        """Carrega dados do cache local."""
        try:
            # Tentar primeiro o arquivo CSV
            cache_file = os.path.join(self.cache_dir, f"{symbol}_{timeframe}.csv")

            # Se não existir CSV, tentar parquet (para compatibilidade)
            if not os.path.exists(cache_file):
                cache_file = os.path.join(
                    self.cache_dir, f"{symbol}_{timeframe}.parquet"
                )

            if os.path.exists(cache_file):
                # Verificar idade do cache (máximo 24 horas)
                file_age = time.time() - os.path.getmtime(cache_file)
                if file_age > 86400:  # 24 horas
                    logger.debug("Cache muito antigo, será recarregado: %s", cache_file)
                    return None

                if cache_file.endswith(".csv"):
                    df = pd.read_csv(cache_file, index_col=0)
                else:
                    df = pd.read_parquet(cache_file)
                return df
        except Exception as e:
            logger.warning("Erro ao carregar cache: %s", e)

        return None
