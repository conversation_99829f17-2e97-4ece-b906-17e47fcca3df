from __future__ import annotations

"""Barramentos de eventos compatíveis com ``aiopubsub``.

<PERSON>ste módulo oferece ``AsyncEventBus`` para integrações assíncronas e
reexporta ``SimpleEventBus`` para retrocompatibilidade. Ele serve como
ponto central de comunicação entre as camadas do QUALIA, garantindo que
mensagens circulem de forma desacoplada.
"""

from dataclasses import dataclass
from typing import TYPE_CHECKING, List, Any, Awaitable, Callable
import asyncio

try:
    from aiopubsub import Hub, Key, Publisher, Subscriber
except ModuleNotFoundError:
    # Dummy classes for aiopubsub if it's not installed.
    # This allows the system to run without this optional dependency.
    class Hub:
        pass

    class Key:
        def __init__(self, *args, **kwargs):
            pass

    class Publisher:
        def __init__(self, *args, **kwargs):
            pass

        def publish(self, *args, **kwargs):
            pass

    class Subscriber:
        def __init__(self, *args, **kwargs):
            pass

        def add_async_listener(self, *args, **kwargs):
            pass

        def remove_listener(self, *args, **kwargs):
            pass

from .utils.event_bus import EventBus as _SimpleEventBus

if TYPE_CHECKING:  # pragma: no cover - hints only
    from .consciousness.real_data_collectors import MarketDataPoint, NewsEvent
    from .core.qast_oracle_decision_engine import OracleDecision


@dataclass
class MarketDataUpdated:
    """Payload for market data updates."""

    market_data: List["MarketDataPoint"]
    news_events: List["NewsEvent"]


@dataclass
class TradingSignalGenerated:
    """Payload for validated trading signals."""

    decisions: List["OracleDecision"]


class AsyncEventBus:
    """Asynchronous event bus built on ``aiopubsub``."""

    def __init__(self) -> None:
        self._hub = Hub()
        self._publisher = Publisher(self._hub, prefix="qualia")
        self._subscriber = Subscriber(self._hub, "qualia-async-bus")
        self._callbacks: dict[
            tuple[str, Callable[[Any], Awaitable[None]]],
            Callable[[Key, Any], Awaitable[None]],
        ] = {}

    def subscribe(self, topic: str, handler: Callable[[Any], Awaitable[None]]) -> None:
        """Register ``handler`` to receive events for ``topic``.

        This method is idempotent: invoking it multiple times with the same
        ``topic`` and ``handler`` will result in a single subscription.
        """

        if (topic, handler) in self._callbacks:
            return

        segments = tuple(topic.split("."))
        key = Key("qualia", *segments)

        async def _callback(_key: Key, msg: Any) -> None:
            await handler(msg)

        self._subscriber.add_async_listener(key, _callback)
        self._callbacks[(topic, handler)] = _callback

    def unsubscribe(
        self, topic: str, handler: Callable[[Any], Awaitable[None]]
    ) -> None:
        """Unregister ``handler`` from ``topic`` if previously subscribed."""
        callback = self._callbacks.pop((topic, handler), None)
        if callback is None:
            return
        segments = tuple(topic.split("."))
        key = Key("qualia", *segments)
        coro = self._subscriber.remove_listener(key, callback)
        try:
            loop = asyncio.get_running_loop()
            loop.create_task(coro)
        except RuntimeError:  # pragma: no cover - no event loop
            asyncio.run(coro)

    def publish(self, topic: str, payload: Any) -> None:
        """Publish ``payload`` to all subscribers of ``topic``."""

        segments = tuple(topic.split("."))
        key = Key(*segments)
        self._publisher.publish(key, payload)

    def subscribe_async(
        self, topic: str, handler: Callable[[Any], Awaitable[None]]
    ) -> None:
        """Register ``handler`` to receive events for ``topic`` asynchronously."""

        if (topic, handler) in self._callbacks:
            return

        self.subscribe(topic, handler)

    async def publish_async(self, topic: str, payload: Any) -> None:
        """Compatibility wrapper for :meth:`publish`."""

        self.publish(topic, payload)

    async def shutdown(self) -> None:
        """Libera todos os recursos do barramento de eventos de forma graciosa.

        Remove todos os listeners registrados, cancela event loops internos do
        ``aiopubsub`` e limpa as referências para permitir coleta de lixo. O
        método é *idempotente* — pode ser chamado várias vezes sem efeitos
        colaterais.
        """

        # Remove cada listener assíncrono registrado
        try:
            for (topic, _handler), callback in list(self._callbacks.items()):
                segments = tuple(topic.split("."))
                key = Key("qualia", *segments)
                try:
                    await self._subscriber.remove_listener(key, callback)
                except Exception:  # pragma: no cover – robustness > strictness
                    # Mantém shutdown robusto mesmo se um listener não puder ser removido
                    pass
            self._callbacks.clear()

            # Alguns ambientes criam uma task interna Loop do aiopubsub –
            # tentamos cancelá-la se existir para suprimir CancelledError na saída.
            internal_loop = getattr(self._subscriber, "_loop", None)
            if internal_loop and not internal_loop.done():
                internal_loop.cancel()

            # Chama close() se o objeto oferecer esse método (versões mais
            # recentes do aiopubsub)
            close_fn = getattr(self._subscriber, "close", None)
            if callable(close_fn):
                maybe_coro = close_fn()
                if asyncio.iscoroutine(maybe_coro):
                    await maybe_coro
        except Exception as exc:  # pragma: no cover – não falha o shutdown
            import logging

            logging.getLogger(__name__).error(
                "Erro durante shutdown do AsyncEventBus: %s", exc, exc_info=True
            )


# Re-export for backward compatibility
SimpleEventBus = _SimpleEventBus


__all__ = [
    "MarketDataUpdated",
    "TradingSignalGenerated",
    "AsyncEventBus",
    "SimpleEventBus",
]
