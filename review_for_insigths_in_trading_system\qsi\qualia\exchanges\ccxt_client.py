"""Generic CCXT-based exchange client."""

from __future__ import annotations

import inspect
import logging
from typing import Any, Dict, Optional, Type

from .base_exchange import BaseExchange
from ..market.base_integration import CryptoDataFetcher

logger = logging.getLogger(__name__)


class CCXTExchangeClient(BaseExchange):
    """Base class implementing common logic for CCXT exchanges."""

    integration_cls: Type[CryptoDataFetcher] = CryptoDataFetcher

    def __init__(self, config: Dict[str, Any]) -> None:
        super().__init__(config)
        self.exchange_id = config.get("exchange_id")
        self.api_key = config.get("api_key")
        self.api_secret = config.get("api_secret")
        self.password = config.get("password")
        self._integration: Optional[CryptoDataFetcher] = None

    @property
    def integration(self) -> Optional[CryptoDataFetcher]:
        """
        Propriedade que implementa lazy loading da integração.

        Para compatibilidade com código existente, esta propriedade garante que:
        1. Se a classe derivada já tiver criado a integração, ela é retornada
        2. <PERSON><PERSON><PERSON> contr<PERSON>rio, tenta criar a integração usando a classe configurada em integration_cls
        """
        if self._integration is None and hasattr(self, "integration_cls"):
            try:
                self._integration = self._create_integration()
            except Exception as e:
                logger.error(f"Erro ao criar instância de integração: {e}")

        return self._integration

    @integration.setter
    def integration(self, value: Optional[CryptoDataFetcher]):
        """Setter para a propriedade integration."""
        self._integration = value

    # ------------------------------------------------------------------
    # Helper methods
    # ------------------------------------------------------------------
    def _filter_config(self) -> Dict[str, Any]:
        """Return ``config`` filtered for ``integration_cls`` parameters."""

        if not hasattr(self, "integration_cls"):
            return {}

        sig = inspect.signature(self.integration_cls.__init__)
        return {
            k: v for k, v in self.config.items() if k in sig.parameters and k != "self"
        }

    def _create_integration(self) -> CryptoDataFetcher:
        """Cria a instância de integração passando a configuração completa."""
        # YAA T7: Passa o dicionário de configuração inteiro para a integração,
        # permitindo que a classe filha (CryptoDataFetcher) extraia o que precisa.
        if hasattr(self, "integration_cls"):
            return self.integration_cls(config=self.config)
        else:
            # Subclasses que implementam lazy loading devem sobrescrever este método
            raise NotImplementedError(
                "Subclasses que não definem integration_cls devem sobrescrever _create_integration"
            )

    # ------------------------------------------------------------------
    # Lifecycle management
    # ------------------------------------------------------------------
    async def initialize(self) -> None:
        """Instantiate and connect the integration."""
        await self.initialize_connection()

    async def initialize_connection(self) -> None:
        integration = self.integration
        if integration is None:
            raise ValueError("Nenhuma integração disponível")

        try:
            await integration.initialize_connection()
            self.connected = True
            logger.info("%s connection initialized", integration.__class__.__name__)
        except Exception:
            self.connected = False
            logger.exception("Failed to initialize %s", integration.__class__.__name__)
            raise

    def is_initialized(self) -> bool:
        """Verifica se a conexão foi estabelecida e os mercados foram carregados."""
        integration = self.integration
        return (
            self.connected
            and integration is not None
            and integration.exchange is not None
            and bool(integration.exchange.markets)
        )

    async def reconnect(self) -> None:
        """Reconnect using a fresh integration instance."""

        await self.shutdown()
        self._integration = None  # Força a recriação da integração
        await self.initialize_connection()

    async def shutdown(self) -> None:
        """Close the underlying integration."""

        if self._integration is not None:
            try:
                await self._integration.close()
            except Exception:
                logger.exception(
                    "Error while closing %s", self._integration.__class__.__name__
                )
            finally:
                self.connected = False
                # Preserve the integration instance after shutdown so tests can
                # inspect its final state. A reconnection will explicitly reset
                # this attribute when needed.
                # self._integration = None

    async def close(self) -> None:
        """Alias para shutdown() para compatibilidade de interface."""
        await self.shutdown()

    async def __aenter__(self) -> "CCXTExchangeClient":
        await self.initialize()
        return self

    async def __aexit__(
        self,
        exc_type,
        exc,
        tb,
    ) -> bool:
        await self.shutdown()
        return False

    # ------------------------------------------------------------------
    # Exchange API wrappers
    # ------------------------------------------------------------------
    async def get_ticker(self, symbol: str) -> Optional[Dict[str, Any]]:
        integration = self.integration
        if not integration:
            return None
        try:
            return await integration.fetch_ticker(symbol)
        except Exception:
            logger.exception("Failed to get ticker for %s", symbol)
            return None

    async def place_order(
        self,
        symbol: str,
        order_type: str,
        side: str,
        amount: float,
        price: Optional[float] = None,
    ) -> Optional[Dict[str, Any]]:
        integration = self.integration
        if not integration:
            return None
        try:
            return await integration.place_order(
                symbol, order_type, side, amount, price
            )
        except Exception:
            logger.exception("Failed to place order on %s", symbol)
            return None

    async def get_balance(self) -> Dict[str, float]:
        integration = self.integration
        if not integration:
            return {}
        try:
            info = await integration.fetch_account_info()
            totals = info.get("total_balances") or {}
            return {k: float(v) for k, v in totals.items()}
        except Exception:
            logger.exception("Failed to fetch balance")
            return {}

    async def get_open_orders(
        self, symbol: Optional[str] = None
    ) -> list[Dict[str, Any]]:
        integration = self.integration
        if not integration:
            return []
        try:
            return await integration.fetch_open_orders(symbol)
        except Exception:
            logger.exception("Failed to fetch open orders")
            return []

    async def cancel_order(self, order_id: str, symbol: str) -> bool:
        integration = self.integration
        if not integration:
            return False
        try:
            result = await integration.cancel_order(order_id, symbol)
            return result is not None
        except Exception:
            logger.exception("Failed to cancel order %s", order_id)
            return False

    async def get_order_book(
        self, symbol: str, limit: int = 100
    ) -> Optional[Dict[str, Any]]:
        """Obter o livro de ordens utilizando ``CryptoDataFetcher``."""

        integration = self.integration
        if not integration:
            return None
        try:
            return await integration.fetch_order_book(symbol, limit=limit)
        except NotImplementedError:
            raise
        except Exception:
            logger.exception("Failed to fetch order book for %s", symbol)
            return None

    async def get_trades(self, symbol: str, limit: int = 100) -> List[Dict[str, Any]]:
        """Obter lista de trades recentes via ``CryptoDataFetcher``."""

        integration = self.integration
        if not integration:
            return []
        try:
            return await integration.fetch_trades(symbol, limit=limit)
        except NotImplementedError:
            raise
        except Exception:
            logger.exception("Failed to fetch trades for %s", symbol)
            return []

    async def fetch_ohlcv(
        self,
        spec: "MarketSpec",
        limit: int = 100,
        ohlcv_timeout: Optional[float] = None,
    ) -> "pd.DataFrame":
        """Obter dados OHLCV utilizando ``CryptoDataFetcher``."""
        integration = self.integration
        if not integration:
            import pandas as pd

            return pd.DataFrame()
        try:
            return await integration.fetch_ohlcv(
                spec, limit=limit, ohlcv_timeout=ohlcv_timeout
            )
        except NotImplementedError:
            raise
        except Exception:
            import pandas as pd

            logger.exception("Failed to fetch OHLCV for %s", spec.symbol)
            return pd.DataFrame()
