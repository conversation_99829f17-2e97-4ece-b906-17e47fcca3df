from __future__ import annotations

from typing import List

import numpy as np
from qualia.utils.logger import get_logger

logger = get_logger(__name__)

try:
    from sentence_transformers import SentenceTransformer  # type: ignore

    _SBERT_AVAILABLE = True
except ModuleNotFoundError:  # pragma: no cover
    _SBERT_AVAILABLE = False
    SentenceTransformer = None  # type: ignore


class Embedder:
    """Text embedding helper using SentenceTransformer.

    Parameters
    ----------
    model_name : str, optional
        Name of the pretrained model to load. Defaults to ``"all-MiniLM-L6-v2"``.
    device : str or None, optional
        Explicit device specification. If ``None``, attempts to auto-detect GPU
        support and falls back to CPU.
    """

    def __init__(self, model_name: str = "all-MiniLM-L6-v2", device: str | None = None):
        """Instantiate the embedding model.

        Parameters
        ----------
        model_name : str, optional
            Identifier of the model hosted by `sentence-transformers`.
            Defaults to ``"all-MiniLM-L6-v2"``.
        device : str or None, optional
            Target device (e.g. ``"cpu"`` or ``"cuda:0"``). If ``None`` a device
            is chosen automatically.
        """
        if not _SBERT_AVAILABLE:
            logger.warning(
                "sentence-transformers not installed; embeddings degraded to bag-of-words mean"
            )
            self.model = None
            return
        if device is None:
            # autoselect cuda if available
            try:
                import torch  # type: ignore

                device = "cuda:0" if torch.cuda.is_available() else "cpu"
            except ModuleNotFoundError:  # pragma: no cover
                device = "cpu"
        self.model = SentenceTransformer(model_name, device=device)

    def encode(self, documents: List[str]) -> np.ndarray:
        """Embed multiple documents.

        Parameters
        ----------
        documents : list of str
            Text snippets to embed.

        Returns
        -------
        numpy.ndarray
            Matrix of shape ``(len(documents), dim)`` with normalised embeddings.
        """
        if self.model is None:
            # Simple fallback: length-normalised TF counts (better than nothing)
            return self._bow_fallback(documents)
        embeddings = self.model.encode(
            documents, normalize_embeddings=True, batch_size=64, show_progress_bar=False
        )
        return embeddings

    def _bow_fallback(self, documents: List[str]) -> np.ndarray:  # pragma: no cover
        """Cheap bag-of-words fallback used when SentenceTransformer is unavailable."""
        from sklearn.feature_extraction.text import TfidfVectorizer

        vectorizer = TfidfVectorizer(max_features=512, stop_words="english")
        tfidf_matrix = vectorizer.fit_transform(documents)
        return tfidf_matrix.toarray().astype("float32")
