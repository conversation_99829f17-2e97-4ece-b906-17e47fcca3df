"""Utilidades para cálculo de taxas de trading."""

from __future__ import annotations

from typing import Any

import ccxt

from .config.settings import get_env
from .utils.logger import get_logger

logger = get_logger(__name__)


def apply_fee(value: float, fee_pct: float) -> float:
    """Aplicar ``fee_pct`` a ``value`` e retornar o valor líquido.

    Parameters
    ----------
    value:
        Valor monetário base para o cálculo da taxa.
    fee_pct:
        Percentual da taxa (por exemplo ``0.0026`` para ``0.26%``).

    Returns
    -------
    float
        Valor líquido após a dedução da taxa.
    """

    logger.debug("Aplicando taxa: valor=%.4f fee_pct=%.4f", value, fee_pct)
    return value * (1 - fee_pct)


def calculate_entry_fee(entry_price: float, quantity: float, fee_pct: float) -> float:
    """Calcular a taxa paga ao abrir uma posição."""
    fee = entry_price * quantity * fee_pct
    logger.debug(
        "Entry fee calculado: price=%.4f qty=%.4f fee_pct=%.4f fee=%.4f",
        entry_price,
        quantity,
        fee_pct,
        fee,
    )
    return fee


def calculate_exit_fee(exit_price: float, quantity: float, fee_pct: float) -> float:
    """Calcular a taxa paga ao fechar uma posição."""
    fee = exit_price * quantity * fee_pct
    logger.debug(
        "Exit fee calculado: price=%.4f qty=%.4f fee_pct=%.4f fee=%.4f",
        exit_price,
        quantity,
        fee_pct,
        fee,
    )
    return fee


def net_pnl(raw_pnl: float, entry_fee: float, exit_fee: float) -> float:
    """Ajustar PnL bruto descontando as taxas de entrada e saída."""
    pnl = raw_pnl - entry_fee - exit_fee
    logger.debug(
        "Net PnL calculado: raw=%.4f entry_fee=%.4f exit_fee=%.4f pnl=%.4f",
        raw_pnl,
        entry_fee,
        exit_fee,
        pnl,
    )
    return pnl


async def fetch_dynamic_fee(trader: Any) -> float:
    """Obter taxa de trading a partir da exchange e variáveis de ambiente.

    Parameters
    ----------
    trader:
        Instância do trader com informações da exchange e credenciais.

    Returns
    -------
    float
        Percentual de taxa obtido após checagens dinâmicas.
    """

    trading_fee_pct = trader.trading_fee_pct

    try:
        if trader.data_source == "kraken":
            from .market.paper_broker import fetch_kraken_taker_fee

            fee_dyn = await fetch_kraken_taker_fee()
            if fee_dyn:
                trading_fee_pct = fee_dyn
                logger.info(
                    "[YAA] Kraken fee dinâmico aplicado: %.4f%%",
                    trading_fee_pct * 100,
                )
        elif trader.data_source == "kucoin":
            from .market.fee_utils import fetch_kucoin_taker_fee_async

            if (
                trader.kucoin_api_key
                and trader.kucoin_secret_key
                and trader.kucoin_passphrase
            ):
                fee_dyn = await fetch_kucoin_taker_fee_async(
                    trader.symbols[0].replace("/", "-"),
                    api_key=trader.kucoin_api_key,
                    api_secret=trader.kucoin_secret_key,
                    password=trader.kucoin_passphrase,
                )
                if fee_dyn:
                    trading_fee_pct = fee_dyn
                    logger.info(
                        "[YAA] Kucoin fee dinâmico aplicado: %.4f%%",
                        trading_fee_pct * 100,
                    )
    except (ccxt.BaseError, OSError, ValueError) as exc:  # pragma: no cover - log
        logger.exception("Falha ao obter fee dinâmico: %s", exc)

    fee_env = get_env("TRADING_FEE_PCT", None, warn=False)
    if fee_env is not None:
        try:
            trading_fee_pct = float(fee_env)
        except ValueError:
            logger.warning(
                "Valor inválido para TRADING_FEE_PCT '%s'. Usando %s.",
                fee_env,
                trading_fee_pct,
            )

    return trading_fee_pct
