"""Sacred geometry utilities.

This module implements helper functions to create symbolic patterns
used throughout QUALIA visualizations. The primary focus is
:func:`generate_sri_yantra`, which synthesizes a simplified Sri
Yantra mandala for pattern experiments.
"""

from __future__ import annotations

import numpy as np

__all__ = ["generate_sri_yantra"]


def generate_sri_yantra(levels: int) -> np.ndarray:
    """Generate normalized coordinates for an approximate Sri Yantra.

    Parameters
    ----------
    levels : int
        Number of triangular layers to generate.

    Returns
    -------
    numpy.ndarray
        Array with shape ``(levels * 3, 2)`` containing ``x`` and ``y``
        coordinates normalized to ``[0, 1]``.
    """
    if levels <= 0:
        raise ValueError("levels must be positive")

    coords = []
    for i in range(1, levels + 1):
        size = (levels - i + 1) / (levels + 1) * 0.5
        if i % 2:
            # upward facing triangle
            coords.append((0.5 - size, 0.5 - size))
            coords.append((0.5 + size, 0.5 - size))
            coords.append((0.5, 0.5 + size))
        else:
            # downward facing triangle
            coords.append((0.5 - size, 0.5 + size))
            coords.append((0.5 + size, 0.5 + size))
            coords.append((0.5, 0.5 - size))

    return np.array(coords, dtype=float)
