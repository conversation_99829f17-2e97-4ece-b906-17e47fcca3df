"""Coleção de indicadores técnicos compartilhados."""

from __future__ import annotations

from typing import Any, Tuple, Union, overload

import numpy as np
import pandas as pd
from numpy.typing import NDArray
from pandas.api.extensions import ExtensionArray


def ema(series: Union[NDArray[Any], ExtensionArray], period: int) -> NDArray[Any]:
    """Calcula a média móvel exponencial.

    Parameters
    ----------
    series
        Série de valores sobre a qual será aplicada a média móvel.
    period
        Número de períodos da média móvel. Deve ser positivo.

    Returns
    -------
    NDArray[Any]
        Valores da média móvel exponencial.

    Raises
    ------
    ValueError
        Se ``period`` for menor ou igual a zero.
    """
    if period <= 0:
        raise ValueError("period must be positive")
    series_arr = np.asarray(series, dtype=float)
    return (
        pd.Series(series_arr)
        .ewm(span=period, adjust=False, ignore_na=True)
        .mean()
        .to_numpy()
    )


def sma(series: Union[NDArray[Any], ExtensionArray], period: int) -> NDArray[Any]:
    """Calcula a média móvel simples.

    Parameters
    ----------
    series
        Série de valores para cálculo da média móvel.
    period
        Número de períodos da média móvel. Deve ser positivo.

    Returns
    -------
    NDArray[Any]
        Valores da média móvel simples.

    Raises
    ------
    ValueError
        Se ``period`` for menor ou igual a zero.
    """
    if period <= 0:
        raise ValueError("period must be positive")
    series_arr = np.asarray(series, dtype=float)
    return pd.Series(series_arr).rolling(window=period).mean().to_numpy()


def macd(
    close: Union[NDArray[Any], ExtensionArray],
    fast_period: int = 12,
    slow_period: int = 26,
    signal_period: int = 9,
) -> Tuple[NDArray[Any], NDArray[Any], NDArray[Any]]:
    """Calcula o MACD (Moving Average Convergence Divergence).

    Parameters
    ----------
    close
        Série de preços de fechamento.
    fast_period
        Período para a média exponencial rápida. Deve ser positivo.
    slow_period
        Período para a média exponencial lenta. Deve ser positivo.
    signal_period
        Período para a linha de sinal. Deve ser positivo.

    Returns
    -------
    Tuple[NDArray[Any], NDArray[Any], NDArray[Any]]
        Tupla contendo linha MACD, linha de sinal e histograma.

    Raises
    ------
    ValueError
        Se qualquer período for menor ou igual a zero.
    """
    if fast_period <= 0 or slow_period <= 0 or signal_period <= 0:
        raise ValueError("period must be positive")
    close_arr = np.asarray(close, dtype=float)
    fast_ema = ema(close_arr, fast_period)
    slow_ema = ema(close_arr, slow_period)
    macd_line = fast_ema - slow_ema
    signal_line = ema(macd_line, signal_period)
    histogram = macd_line - signal_line
    return macd_line, signal_line, histogram


def calculate_macd_from_emas(
    fast_ema: Union[NDArray[Any], ExtensionArray],
    slow_ema: Union[NDArray[Any], ExtensionArray],
    signal_period: int = 9,
) -> Tuple[NDArray[Any], NDArray[Any], NDArray[Any]]:
    """Calcula o MACD a partir de EMAs preexistentes.

    Parameters
    ----------
    fast_ema
        Série contendo a média exponencial rápida já calculada.
    slow_ema
        Série contendo a média exponencial lenta já calculada.
    signal_period
        Período para a linha de sinal. Deve ser positivo.

    Returns
    -------
    Tuple[NDArray[Any], NDArray[Any], NDArray[Any]]
        Tupla com a linha MACD, a linha de sinal e o histograma.

    Raises
    ------
    ValueError
        Se ``signal_period`` for menor ou igual a zero ou se as séries tiverem comprimentos diferentes.
    """
    if signal_period <= 0:
        raise ValueError("period must be positive")
    fast_arr = np.asarray(fast_ema, dtype=float)
    slow_arr = np.asarray(slow_ema, dtype=float)
    if fast_arr.shape != slow_arr.shape:
        raise ValueError("fast_ema and slow_ema must have the same length")

    macd_line = fast_arr - slow_arr
    signal_line = ema(macd_line, signal_period)
    histogram = macd_line - signal_line
    return macd_line, signal_line, histogram


@overload
def rsi(prices: pd.Series, period: int = 14) -> pd.Series: ...


@overload
def rsi(
    prices: Union[NDArray[Any], ExtensionArray], period: int = 14
) -> NDArray[Any]: ...


def rsi(
    prices: Union[pd.Series, NDArray[Any], ExtensionArray], period: int = 14
) -> Union[pd.Series, NDArray[Any]]:
    """Calcula o RSI (Relative Strength Index).

    Parameters
    ----------
    prices
        Série de preços de fechamento.
    period
        Janela para cálculo do índice. Deve ser positiva.

    Returns
    -------
    Union[pd.Series, NDArray[Any]]
        Valores do RSI. Mantém o índice quando ``prices`` é uma :class:`pandas.Series`.

    Raises
    ------
    ValueError
        Se ``period`` for menor ou igual a zero.
    """
    if period <= 0:
        raise ValueError("period must be positive")

    if isinstance(prices, pd.Series):
        index = prices.index
        prices_arr = prices.to_numpy(dtype=float)
    else:
        index = None
        prices_arr = np.asarray(prices, dtype=float)

    if len(prices_arr) < period:
        result = pd.Series(np.full(len(prices_arr), np.nan, dtype=float), index=index)
        return result if index is not None else result.to_numpy()

    series = pd.Series(prices_arr, index=index)
    delta = series.diff()
    gain = delta.clip(lower=0)
    loss = -delta.clip(upper=0)
    avg_gain = gain.rolling(window=period, min_periods=period).mean()
    avg_loss = loss.rolling(window=period, min_periods=period).mean()
    rs = avg_gain / avg_loss.replace(0, np.nan)
    rsi_series = 100 - (100 / (1 + rs))
    rsi_series.loc[avg_loss == 0] = 100
    rsi_series.loc[avg_gain == 0] = 0

    first_valid = rsi_series.first_valid_index()
    if first_valid is not None:
        rsi_series.loc[first_valid:] = rsi_series.loc[first_valid:].ffill()
    return rsi_series if index is not None else rsi_series.to_numpy()


def stoch(
    high: Union[NDArray[Any], ExtensionArray],
    low: Union[NDArray[Any], ExtensionArray],
    close: Union[NDArray[Any], ExtensionArray],
    k_period: int = 14,
    d_period: int = 3,
) -> Tuple[NDArray[Any], NDArray[Any]]:
    """Calcula o oscilador estocástico.

    Parameters
    ----------
    high
        Série de máximas.
    low
        Série de mínimas.
    close
        Série de preços de fechamento.
    k_period
        Janela para o cálculo da linha %K. Deve ser positiva.
    d_period
        Janela para o cálculo da linha %D. Deve ser positiva.

    Returns
    -------
    Tuple[NDArray[Any], NDArray[Any]]
        Tupla contendo as linhas %K e %D.

    Raises
    ------
    ValueError
        Se ``k_period`` ou ``d_period`` forem menores ou iguais a zero.
    """
    if k_period <= 0 or d_period <= 0:
        raise ValueError("period must be positive")
    high_arr = np.asarray(high, dtype=float)
    low_arr = np.asarray(low, dtype=float)
    close_arr = np.asarray(close, dtype=float)
    if len(close_arr) < max(k_period, d_period):
        length = len(close_arr)
        return np.full(length, np.nan, dtype=float), np.full(
            length, np.nan, dtype=float
        )

    highest_high = pd.Series(high_arr).rolling(window=k_period).max()
    lowest_low = pd.Series(low_arr).rolling(window=k_period).min()
    denom = highest_high - lowest_low
    k_series = 100 * (close_arr - lowest_low) / denom
    k_series.loc[denom == 0] = 50
    d_series = k_series.rolling(window=d_period).mean()

    return k_series.to_numpy(), d_series.to_numpy()


def atr(
    high: Union[NDArray[Any], ExtensionArray],
    low: Union[NDArray[Any], ExtensionArray],
    close: Union[NDArray[Any], ExtensionArray],
    period: int = 14,
) -> NDArray[Any]:
    """Calcula o ATR (Average True Range).

    Parameters
    ----------
    high
        Série de máximas.
    low
        Série de mínimas.
    close
        Série de preços de fechamento.
    period
        Janela utilizada para cálculo do ATR. Deve ser positiva.

    Returns
    -------
    NDArray[Any]
        Valores do ATR.

    Raises
    ------
    ValueError
        Se ``period`` for menor ou igual a zero.
    """
    if period <= 0:
        raise ValueError("period must be positive")
    high_arr = np.asarray(high, dtype=float)
    low_arr = np.asarray(low, dtype=float)
    close_arr = np.asarray(close, dtype=float)
    if len(close_arr) < period:
        return np.full(close_arr.shape, np.nan, dtype=float)

    tr = np.empty_like(close_arr, dtype=float)

    tr[0] = high_arr[0] - low_arr[0]
    if len(close_arr) > 1:
        high_low = high_arr[1:] - low_arr[1:]
        high_close = np.abs(high_arr[1:] - close_arr[:-1])
        low_close = np.abs(low_arr[1:] - close_arr[:-1])
        tr[1:] = np.maximum.reduce([high_low, high_close, low_close])

    atr_values = np.full_like(close_arr, np.nan, dtype=float)

    # O primeiro valor do ATR é a média dos primeiros 'period' valores do TR.
    atr_values[period - 1] = np.mean(tr[:period])

    # Os ATRs subsequentes são suavizados usando a fórmula de Wilder.
    for i in range(period, len(tr)):
        atr_values[i] = (atr_values[i - 1] * (period - 1) + tr[i]) / period

    return atr_values


__all__ = [
    "ema",
    "sma",
    "macd",
    "calculate_macd_from_emas",
    "rsi",
    "stoch",
    "atr",
]
