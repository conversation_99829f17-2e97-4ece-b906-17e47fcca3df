"""Definições de envelope para intenções de trading no QUALIA."""

from __future__ import annotations

from dataclasses import dataclass

from ..utils.validation import validate_positive_float, validate_non_negative_int


@dataclass
class IntentionEnvelope:
    """Limites para execução de uma intenção de trading.

    Parameters
    ----------
    profit_target : float
        Meta máxima de lucro desejada.
    max_drawdown : float
        Limite de perda aceitável antes de abortar a intenção.
    horizon_hours : int
        Horizonte de execução em horas para a intenção.
    """

    profit_target: float
    max_drawdown: float
    horizon_hours: int

    def __post_init__(self) -> None:
        """Valida os limites fornecidos."""
        validate_positive_float(self.profit_target, "profit_target")
        validate_positive_float(self.max_drawdown, "max_drawdown")
        hours = validate_non_negative_int(self.horizon_hours, "horizon_hours")
        if hours <= 0:
            raise ValueError("horizon_hours deve ser positivo")

    def to_dict(self) -> dict:
        """Retorna uma representação em dicionário do envelope."""

        return {
            "profit_target": self.profit_target,
            "max_drawdown": self.max_drawdown,
            "horizon_hours": self.horizon_hours,
        }

    @classmethod
    def from_dict(cls, data: dict) -> "IntentionEnvelope":
        """Cria uma instância a partir de ``data``."""

        return cls(
            profit_target=data["profit_target"],
            max_drawdown=data["max_drawdown"],
            horizon_hours=data["horizon_hours"],
        )
