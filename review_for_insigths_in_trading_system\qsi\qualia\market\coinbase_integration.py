"""Coinbase exchange integration for QUALIA."""

from __future__ import annotations

from typing import Any, Dict, Optional

from ..config import load_market_defaults
from ..utils.logger import get_logger
from .base_integration import CryptoDataFetcher
from .kraken_integration import _load_exchange_credentials

logger = get_logger("src.qualia.market.coinbase_integration")


def _market_defaults() -> dict:
    return load_market_defaults()


class CoinbaseIntegration(CryptoDataFetcher):
    """Wrapper around :class:`CryptoDataFetcher` for Coinbase."""

    def __init__(
        self,
        api_key: Optional[str] = None,
        api_secret: Optional[str] = None,
        conn_timeout: Optional[float] = None,
        conn_retries: Optional[int] = None,
        ticker_timeout: Optional[float] = None,
        ticker_retries: Optional[int] = None,
        ticker_backoff_base: Optional[float] = None,
        ohlcv_timeout: Optional[float] = None,
        statsd_client: Optional["DogStatsd"] = None,
        config: Optional[Dict[str, Any]] = None,
    ) -> None:
        """Initialize the Coinbase integration.

        Parameters
        ----------
        api_key, api_secret
            Credentials loaded from ``COINBASE_API_KEY`` and
            ``COINBASE_API_SECRET`` when ``None``.
        conn_timeout, conn_retries
            Connection parameters for ``load_markets``.
        ticker_timeout, ticker_retries, ticker_backoff_base
            Parameters controlling ticker retrieval.
        ohlcv_timeout
            Timeout for fetching OHLCV data.
        statsd_client
            Optional StatsD client for metrics.
        config : dict, optional
            QUALIA configuration dictionary.
        """

        defaults = _market_defaults()
        creds = _load_exchange_credentials(
            prefix="COINBASE",
            api_key=api_key,
            api_secret=api_secret,
            ticker_timeout=ticker_timeout,
            ticker_timeout_env="TICKER_TIMEOUT",
            default_ticker_timeout=defaults.get("timeouts", {}).get("ticker", 20.0),
            ticker_retries=ticker_retries,
            ticker_backoff_base=ticker_backoff_base,
            conn_timeout=conn_timeout
            or defaults.get("timeouts", {}).get("connection", 30.0),
            conn_retries=conn_retries or defaults.get("retries", {}).get("conn", 3),
        )

        super().__init__(
            api_key=creds["api_key"],
            api_secret=creds["api_secret"],
            exchange_id="coinbase",
            conn_timeout=creds["conn_timeout"],
            conn_retries=creds["conn_retries"],
            ticker_timeout=creds["ticker_timeout"],
            ticker_retries=creds["ticker_retries"],
            ticker_backoff_base=creds.get("ticker_backoff_base", ticker_backoff_base),
            ohlcv_timeout=ohlcv_timeout,
            statsd_client=statsd_client,
            config=config,
        )
