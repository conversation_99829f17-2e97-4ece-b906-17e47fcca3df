"""QUALIA: Quantum Universal Awareness Lattice Interface Architecture.

Módulo de Controle de Risco Dinâmico que recalibra automaticamente
take-profit e stop-loss utilizando ATR (Average True Range) e volatilidade
implícita a cada ciclo do ``AdaptiveConsciousnessEvolution``.

Volume não é obrigatório nas estruturas de mercado fornecidas. Quando a
coluna correspondente estiver ausente, os ajustes que dependem de volume
assumem valor neutro e um aviso é registrado apenas uma vez por ciclo.
"""

from __future__ import annotations

from ..utils.logger import get_logger
from ..utils.tracing import get_tracer, instrument_logger, is_tracing_enabled
import os
import numpy as np
import pandas as pd
from typing import Any, Dict, List, Optional, Tuple, Union, TYPE_CHECKING
from datadog import DogStatsd
from unittest.mock import Mock
from ..memory.event_bus import SimpleEventBus
import time
from datetime import datetime, timedelta, timezone
from dataclasses import dataclass, asdict
from ..core.qualia_logger import log_event

from ..config.dynamic_risk_defaults import load_dynamic_risk_defaults
from ..utils.dynamic_risk_utils import (
    DynamicRiskParameters,
    validate_dynamic_risk_parameters,
    load_risk_parameters,
    calculate_atr_levels,
)

if TYPE_CHECKING:  # Avoid circular import during runtime
    from ..adaptive_evolution import AdaptiveConsciousnessEvolution
    from ..metacognition.risk_metacognition_integration import (
        RiskMetacognitionEngine,
    )
    from ..event_bus import AsyncEventBus
from ..market.volatility_utils import calculate_implied_volatility
from ..market.regime_classifier import (
    determine_market_regime,
    _get_regime_multiplier,
)
from ..metacognition.risk_metacognition_adapter import (
    process_with_metacognition as _process_with_metacognition_helper,
    _apply_metacognitive_insights as _apply_metacognitive_insights_helper,
)

logger = get_logger(__name__)
if is_tracing_enabled():  # pragma: no cover - simple conditional
    instrument_logger(logger.logger)


@dataclass
class RiskCalibrationResult:
    """Resultado da calibração de risco dinâmica."""

    timestamp: datetime
    symbol: str
    atr_value: float
    volatility_metric: float
    market_regime: str  # 'calm', 'normal', 'volatile'

    # Valores calibrados
    stop_loss_distance: float
    take_profit_distance: float
    stop_loss_price: Optional[float] = None
    take_profit_price: Optional[float] = None

    # Fatores de ajuste aplicados
    atr_multiplier_effective: float = 0.0
    volatility_adjustment_factor: float = 0.0
    regime_adjustment_factor: float = 0.0

    # Informações adicionais
    confidence_level: float = 0.0
    adjustment_reason: str = ""


class DynamicRiskController:
    """Controlador de risco dinâmico integrado ao AdaptiveConsciousnessEvolution."""

    def __init__(
        self,
        risk_profile: str,
        config: Optional[Dict[str, Any]] = None,
        adaptive_evolution: Optional[AdaptiveConsciousnessEvolution] = None,
        statsd_client: Optional[DogStatsd] = None,
        event_bus: Optional[SimpleEventBus] = None,
        *,
        use_gpu: bool | None = None,
    ) -> None:
        """Initialize the dynamic risk controller.

        Args:
            risk_profile: Perfil de risco fornecido pelo trader.
            config: Configuração específica do controlador.
            adaptive_evolution: Instância do ``AdaptiveConsciousnessEvolution``.
            statsd_client: Cliente opcional do DogStatsd para métricas.
            event_bus: Barramento de eventos para notificar recalibrações.
        """
        tracer = get_tracer(__name__)
        with tracer.start_as_current_span("dynamic_risk_controller.init") as span:
            self.config = config or {}
            self.adaptive_evolution = adaptive_evolution
            if statsd_client is not None:
                self.statsd = statsd_client
            else:
                try:
                    if hasattr(DogStatsd, "increment"):
                        self.statsd = DogStatsd()
                    else:
                        raise TypeError("stubbed DogStatsd")
                except Exception:  # pragma: no cover - fallback when stubbed
                    self.statsd = Mock(spec=["increment", "gauge", "timing"])
            self.event_bus = event_bus
            self.use_gpu = use_gpu
            self.risk_profile = risk_profile
            self.trace_id = None
            if is_tracing_enabled():
                ctx = span.get_span_context()
                if ctx and ctx.trace_id:
                    self.trace_id = format(ctx.trace_id, "032x")

        if self.event_bus is not None:
            try:
                from ..event_bus import AsyncEventBus
            except Exception:  # pragma: no cover - optional dependency
                AsyncEventBus = None  # type: ignore
            if AsyncEventBus and isinstance(self.event_bus, AsyncEventBus):
                self.subscribe_to_async_event_bus(self.event_bus)

        # Inicializar parâmetros dinâmicos
        self.params = self._load_risk_parameters()

        # Estado interno
        self.last_calibration_time: Dict[str, datetime] = {}
        self.calibration_history: Dict[str, List[RiskCalibrationResult]] = {}
        self.current_market_regime = "normal"
        self.volatility_cache: Dict[str, float] = {}

        # Métricas
        self.performance_metrics = {
            "total_recalibrations": 0,
            "successful_adjustments": 0,
            "regime_changes": 0,
            "average_atr_multiplier": 0.0,
        }

        # Informações de risco provenientes do RiskManager
        self.current_capital: float | None = None
        self.drawdown_pct: float | None = None
        self.risk_manager_profile: str | None = None

        # Ajustes externos recebidos via EventBus
        self.stop_loss_override: float | None = None
        self.exposure_limit_pct: float | None = None

        self.evaluation_weights: Dict[str, float] = self.config.get(
            "evaluation_weights",
            {"atr": 0.4, "volatility": 0.3, "regime": 0.3},
        )

        logger.info(f"DynamicRiskController inicializado para perfil '{risk_profile}'")
        logger.info(
            f"Parâmetros: ATR período={self.params.atr_period}, "
            f"Multiplicador base={self.params.atr_multiplier_base}"
        )

        if self.event_bus is not None:
            self.event_bus.subscribe("risk.update", self._on_risk_update)
            self.event_bus.subscribe("market.alert", self._on_market_alert)
            self.event_bus.subscribe(
                "risk.manager_created", self._on_risk_manager_created
            )
            self.event_bus.subscribe("risk.recalibrated", self._on_risk_recalibrated)

    def _load_risk_parameters(self) -> DynamicRiskParameters:
        """Load risk parameters based on profile and config."""
        defaults = load_dynamic_risk_defaults()
        return load_risk_parameters(self.risk_profile, self.config, defaults)

    def calculate_implied_volatility(
        self,
        price_data: Union[List[float], np.ndarray, pd.Series],
        method: str = "historical",
        *,
        use_gpu: bool | None = None,
    ) -> float:
        """Delegate to :func:`calculate_implied_volatility`."""
        return calculate_implied_volatility(price_data, method, use_gpu=use_gpu)

    def calculate_atr_levels(
        self,
        high_data: Union[List[float], np.ndarray],
        low_data: Union[List[float], np.ndarray],
        close_data: Union[List[float], np.ndarray],
        current_price: float,
        *,
        symbol: str = "UNKNOWN",
    ) -> Tuple[float, float, float]:
        """Wrapper para :func:`calculate_atr_levels` util."""
        try:
            return calculate_atr_levels(
                high_data,
                low_data,
                close_data,
                current_price,
                self.params,
                use_gpu=self.use_gpu,
            )
        except Exception as exc:
            logger.error(
                "Erro ao calcular níveis ATR: %s",
                exc,
                extra={"symbol": symbol},
            )
            fallback_atr = current_price * 0.01
            return fallback_atr, fallback_atr * 2.0, fallback_atr * 4.0

    def determine_market_regime(
        self, volatility: float, complexity_level: Optional[str] = None
    ) -> str:
        """Determine the market regime for the given volatility."""
        return determine_market_regime(
            volatility,
            self.params.volatility_threshold_low,
            self.params.volatility_threshold_high,
            complexity_level,
        )

    def calibrate_risk_levels(
        self,
        symbol: str,
        market_data: Dict[str, Any],
        current_price: float,
        position_side: str = "long",
        confidence_level: float = 0.5,
        trace_id: Optional[str] = None,
    ) -> RiskCalibrationResult:
        """
        Calibra níveis de risco dinâmicos para um símbolo específico.

        Args:
            symbol: Símbolo do ativo
            market_data: Dados de mercado (OHLCV)
            current_price: Preço atual
            position_side: Lado da posição ('long', 'short')
            confidence_level: Nível de confiança do sinal
            trace_id: ID opcional para rastreabilidade

        Returns:
            Resultado da calibração
        """
        tracer = get_tracer(__name__)
        with tracer.start_as_current_span(
            "risk.calibrate_risk_levels",
            attributes={"symbol": symbol, "position_side": position_side},
        ):
            return self._calibrate_risk_levels_impl(
                symbol,
                market_data,
                current_price,
                position_side,
                confidence_level,
                trace_id,
            )

    def _calibrate_risk_levels_impl(
        self,
        symbol: str,
        market_data: Dict[str, Any],
        current_price: float,
        position_side: str,
        confidence_level: float,
        trace_id: Optional[str],
    ) -> RiskCalibrationResult:
        try:
            start_time = time.perf_counter()
            timestamp = datetime.now(timezone.utc)
            previous_regime = self.current_market_regime

            # Extrair dados OHLCV
            if isinstance(market_data, dict):
                high_data = market_data.get("high", [])
                low_data = market_data.get("low", [])
                close_data = market_data.get("close", [])
            else:
                # Assumir DataFrame
                high_data = market_data["high"].values
                low_data = market_data["low"].values
                close_data = market_data["close"].values

            # Verificar dados suficientes
            if len(close_data) < self.params.atr_period:
                logger.warning(
                    f"Dados insuficientes para {symbol}, usando valores default"
                )
                return self._create_default_calibration(
                    symbol, current_price, timestamp
                )

            # Calcular ATR e níveis
            atr_value, stop_distance, take_profit_distance = self.calculate_atr_levels(
                high_data, low_data, close_data, current_price, symbol=symbol
            )

            # Calcular volatilidade implícita
            volatility_metric = self.calculate_implied_volatility(
                close_data, use_gpu=self.use_gpu
            )

            # Determinar regime de mercado
            ace_complexity = None
            if self.adaptive_evolution:
                ace_complexity = getattr(
                    self.adaptive_evolution, "current_complexity_level", None
                )

            market_regime = self.determine_market_regime(
                volatility_metric, ace_complexity
            )

            # Aplicar ajustes de regime
            regime_multiplier = self._get_regime_multiplier(market_regime)
            stop_distance *= regime_multiplier
            take_profit_distance *= regime_multiplier

            # Ajustar baseado na confiança do sinal
            confidence_multiplier = 0.8 + (confidence_level * 0.4)  # 0.8 a 1.2
            stop_distance *= confidence_multiplier

            # Calcular preços efetivos
            if position_side.lower() == "long":
                stop_loss_price = current_price - stop_distance
                take_profit_price = current_price + take_profit_distance
            else:  # short
                stop_loss_price = current_price + stop_distance
                take_profit_price = current_price - take_profit_distance

            # Criar resultado
            result = RiskCalibrationResult(
                timestamp=timestamp,
                symbol=symbol,
                atr_value=atr_value,
                volatility_metric=volatility_metric,
                market_regime=market_regime,
                stop_loss_distance=stop_distance,
                take_profit_distance=take_profit_distance,
                stop_loss_price=stop_loss_price,
                take_profit_price=take_profit_price,
                atr_multiplier_effective=(
                    stop_distance / atr_value if atr_value > 0 else 2.0
                ),
                volatility_adjustment_factor=volatility_metric,
                regime_adjustment_factor=regime_multiplier,
                confidence_level=confidence_level,
                adjustment_reason=f"Regime: {market_regime}, Vol: {volatility_metric:.3f}",
            )

            # Armazenar no histórico
            if symbol not in self.calibration_history:
                self.calibration_history[symbol] = []
            self.calibration_history[symbol].append(result)

            # Manter apenas últimas 100 calibrações por símbolo
            if len(self.calibration_history[symbol]) > 100:
                self.calibration_history[symbol] = self.calibration_history[symbol][
                    -100:
                ]

            if self.event_bus:
                from ..events import RiskRecalibratedEvent

                self.event_bus.publish(
                    "risk.recalibrated",
                    RiskRecalibratedEvent(
                        symbol=symbol,
                        calibration=result,
                    ),
                )
                log_event(
                    event_type="risk.recalibrated",
                    payload={
                        "symbol": symbol,
                        "calibration": asdict(result),
                    },
                    source="dynamic_risk_controller",
                )

            # Atualizar timestamp da última calibração
            self.last_calibration_time[symbol] = timestamp

            # Atualizar métricas
            self.performance_metrics["total_recalibrations"] += 1
            if market_regime != self.current_market_regime:
                self.performance_metrics["regime_changes"] += 1
                self.current_market_regime = market_regime

            multiplier = stop_distance / atr_value if atr_value > 0 else 0.0
            count = self.performance_metrics["total_recalibrations"]
            prev_avg = self.performance_metrics["average_atr_multiplier"]
            new_avg = (prev_avg * (count - 1) + multiplier) / count
            self.performance_metrics["average_atr_multiplier"] = new_avg

            tags = [f"symbol:{symbol}"]
            if trace_id:
                tags.append(f"trace_id:{trace_id}")
            self.statsd.increment("dynamic_risk.recalibration", tags=tags)
            if market_regime != previous_regime:
                self.statsd.increment("dynamic_risk.regime_change", tags=tags)
            self.statsd.gauge(
                "dynamic_risk.average_atr_multiplier",
                new_avg,
                tags=tags,
            )
            self.statsd.timing(
                "dynamic_risk.calibration_time_ms",
                (time.perf_counter() - start_time) * 1000,
                tags=tags,
            )

            logger.info(
                f"Calibração para {symbol}: ATR={atr_value:.4f}, "
                f"SL={stop_distance:.4f}, TP={take_profit_distance:.4f}, "
                f"Regime={market_regime}"
            )

            return result

        except (ValueError, TypeError, KeyError, IndexError) as exc:
            logger.error(
                "Erro na calibração de risco para %s: %s",
                symbol,
                exc,
                extra={"symbol": symbol},
            )
            return self._create_default_calibration(
                symbol, current_price, datetime.now(timezone.utc)
            )

    def _get_regime_multiplier(self, market_regime: str) -> float:
        """Delegate to :func:`_get_regime_multiplier`."""
        return _get_regime_multiplier(
            market_regime,
            self.params.regime_calm_multiplier,
            self.params.regime_normal_multiplier,
            self.params.regime_volatile_multiplier,
        )

    def _create_default_calibration(
        self, symbol: str, current_price: float, timestamp: datetime
    ) -> RiskCalibrationResult:
        """Cria calibração padrão quando há erro ou dados insuficientes."""
        # Valores conservadores padrão
        default_atr = current_price * 0.01  # 1% do preço
        default_stop = default_atr * 2.0
        default_tp = default_stop * 2.0

        return RiskCalibrationResult(
            timestamp=timestamp,
            symbol=symbol,
            atr_value=default_atr,
            volatility_metric=0.2,
            market_regime="normal",
            stop_loss_distance=default_stop,
            take_profit_distance=default_tp,
            stop_loss_price=current_price - default_stop,
            take_profit_price=current_price + default_tp,
            atr_multiplier_effective=2.0,
            volatility_adjustment_factor=1.0,
            regime_adjustment_factor=1.0,
            confidence_level=0.5,
            adjustment_reason="Default values (insufficient data)",
        )

    def should_recalibrate(self, symbol: str) -> bool:
        """
        Verifica se deve recalibrar o risco para um símbolo.

        Args:
            symbol: Símbolo do ativo

        Returns:
            True se deve recalibrar
        """
        try:
            # Verificar se nunca foi calibrado
            if symbol not in self.last_calibration_time:
                return True

            # Verificar tempo desde última calibração
            last_time = self.last_calibration_time[symbol]
            if last_time.tzinfo is None:
                last_time = last_time.replace(tzinfo=timezone.utc)
            time_since_last = datetime.now(timezone.utc) - last_time

            if time_since_last >= timedelta(
                minutes=self.params.recalibration_frequency_minutes
            ):
                return True

            # Verificar se houve mudança significativa no regime de mercado
            if self.adaptive_evolution:
                ace_complexity = getattr(
                    self.adaptive_evolution, "current_complexity_level", None
                )
                if ace_complexity and ace_complexity != self.current_market_regime:
                    logger.info(
                        f"Recalibração necessária para {symbol}: mudança de regime para {ace_complexity}"
                    )
                    return True

            return False

        except (ValueError, TypeError, KeyError, AttributeError) as exc:
            logger.error(
                "Erro ao verificar necessidade de recalibração: %s",
                exc,
                extra={"symbol": symbol},
            )
            return True  # Errar do lado conservador

    def integrate_with_adaptive_evolution(
        self, market_obs_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Integra com o ciclo do AdaptiveConsciousnessEvolution para recalibração automática.

        Args:
            market_obs_data: Dados de observação de mercado do ACE que podem
                incluir ``trace_id``

        Returns:
            Dados de calibração atualizados
        """
        symbol = "UNKNOWN"
        try:
            calibration_results = {}

            # Extrair informações do ACE
            symbol = market_obs_data.get("symbol", "UNKNOWN")
            volatility = market_obs_data.get("volatility", 0.2)
            non_linearity = market_obs_data.get("non_linearity", 0.5)
            trace_id = market_obs_data.get("trace_id")

            # Verificar se há dados de mercado disponíveis
            if "market_data" in market_obs_data:
                market_data = market_obs_data["market_data"]
                current_price = market_obs_data.get("current_price")

                if current_price and self.should_recalibrate(symbol):
                    # Realizar calibração
                    result = self.calibrate_risk_levels(
                        symbol=symbol,
                        market_data=market_data,
                        current_price=current_price,
                        confidence_level=min(
                            1.0, volatility + non_linearity
                        ),  # Usar métricas ACE
                        trace_id=trace_id,
                    )

                    calibration_results[symbol] = {
                        "stop_loss_price": result.stop_loss_price,
                        "take_profit_price": result.take_profit_price,
                        "atr_value": result.atr_value,
                        "market_regime": result.market_regime,
                        "calibration_timestamp": result.timestamp.isoformat(),
                        "adjustment_reason": result.adjustment_reason,
                    }

                    logger.info(
                        f"Recalibração automática completada para {symbol} via ACE"
                    )

            return {
                "calibration_results": calibration_results,
                "current_regime": self.current_market_regime,
                "total_recalibrations": self.performance_metrics[
                    "total_recalibrations"
                ],
                "integration_status": "success",
            }

        except (ValueError, TypeError, KeyError, AttributeError) as exc:
            logger.error(
                "Erro na integração com AdaptiveConsciousnessEvolution: %s",
                exc,
                extra={"symbol": symbol},
            )
            return {
                "calibration_results": {},
                "integration_status": "error",
                "error_message": str(exc),
            }

    def get_current_risk_levels(self, symbol: str) -> Optional[RiskCalibrationResult]:
        """
        Retorna os níveis de risco atuais para um símbolo.

        Args:
            symbol: Símbolo do ativo

        Returns:
            Último resultado de calibração ou None
        """
        if symbol in self.calibration_history and self.calibration_history[symbol]:
            return self.calibration_history[symbol][-1]
        return None

    def get_performance_metrics(self) -> Dict[str, Any]:
        """Retorna métricas de performance do controlador."""
        return {
            **self.performance_metrics,
            "symbols_tracked": len(self.calibration_history),
            "last_calibration_times": {
                symbol: timestamp.isoformat()
                for symbol, timestamp in self.last_calibration_time.items()
            },
            "current_market_regime": self.current_market_regime,
        }

    def set_metacognition_engine(
        self, metacognition_engine: "RiskMetacognitionEngine"
    ) -> None:
        """
        Conecta o engine de metacognição de risco.

        Args:
            metacognition_engine: Instância do RiskMetacognitionEngine
        """
        self.metacognition_engine = metacognition_engine
        logger.info("Sistema de metacognição conectado ao DynamicRiskController")

    async def process_with_metacognition(
        self, symbol: str, market_data: Dict[str, Any], current_price: float
    ) -> RiskCalibrationResult:
        """Run risk calibration and apply metacognitive feedback."""
        return await _process_with_metacognition_helper(
            self, symbol, market_data, current_price
        )

    def _apply_metacognitive_insights(
        self, result: RiskCalibrationResult, metacognitive_state: Dict[str, Any]
    ) -> RiskCalibrationResult:
        """Apply metacognitive insights to a calibration result."""
        return _apply_metacognitive_insights_helper(result, metacognitive_state)

    def update_evaluation_weight(self, metric: str, weight: float) -> None:
        """Update internal evaluation weight and publish an event."""

        if weight <= 0:
            raise ValueError("weight must be positive")
        old = float(self.evaluation_weights.get(metric, 0.0))
        self.evaluation_weights[metric] = float(weight)
        if self.event_bus:
            from ..events import EvaluationWeightAdjustedEvent

            self.event_bus.publish(
                "risk.evaluation_weight_updated",
                EvaluationWeightAdjustedEvent(
                    metric=metric,
                    old_weight=old,
                    new_weight=float(weight),
                ),
            )
        logger.info(
            "Peso de avaliação '%s' alterado de %.3f para %.3f", metric, old, weight
        )

    def apply_parameter_updates(self, params: Dict[str, Any]) -> None:
        """Apply a mapping of parameter updates to ``self.params``."""

        for key, value in params.items():
            if hasattr(self.params, key):
                setattr(self.params, key, value)

    def subscribe_to_async_event_bus(
        self, event_bus: "AsyncEventBus", event_name: str = "dynamic_risk.update"
    ) -> None:
        """Listen for parameter updates on an async event bus."""

        async def _handler(payload: Any) -> None:
            from ..events import DynamicRiskParameterUpdate

            if isinstance(payload, DynamicRiskParameterUpdate):
                updates = payload.params
            elif isinstance(payload, dict):
                updates = payload.get("params", payload)
            else:
                updates = getattr(payload, "params", {})
            if isinstance(updates, dict):
                self.apply_parameter_updates(updates)

        event_bus.subscribe(event_name, _handler)

    def adjust_stop_take_profit(
        self,
        stop_loss: float,
        take_profit: float,
        current_price: float,
        entropy: float,
        otoc: float,
        *,
        symbol: str = "UNKNOWN",
    ) -> Tuple[float, float]:
        """Adjust stop loss and take profit distances.

        The adjustment is applied to the distance between ``current_price`` and
        the provided levels instead of directly scaling the absolute prices. This
        prevents values from exploding when ``entropy`` or ``otoc`` are high.
        """
        try:
            adj = 1.0 + float(entropy) + min(float(otoc), 1.0)
            adj = np.clip(adj, 0.5, self.params.max_adjustment_factor)

            if current_price is not None and np.isfinite(current_price):
                if stop_loss < current_price < take_profit:
                    sl_dist = current_price - stop_loss
                    tp_dist = take_profit - current_price
                    new_sl = current_price - sl_dist * adj
                    new_tp = current_price + tp_dist * adj
                elif take_profit < current_price < stop_loss:
                    sl_dist = stop_loss - current_price
                    tp_dist = current_price - take_profit
                    new_sl = current_price + sl_dist * adj
                    new_tp = current_price - tp_dist * adj
                else:
                    new_sl = stop_loss * adj
                    new_tp = take_profit * adj
            else:
                new_sl = stop_loss * adj
                new_tp = take_profit * adj

            if self.event_bus:
                from ..events import RiskAdjustedEvent

                self.event_bus.publish(
                    "risk.adjusted",
                    RiskAdjustedEvent(
                        symbol=symbol,
                        stop_loss=new_sl,
                        take_profit=new_tp,
                    ),
                )

            return new_sl, new_tp
        except (ValueError, TypeError, OverflowError) as exc:
            logger.error(
                "Erro em adjust_stop_take_profit: %s",
                exc,
                extra={"symbol": symbol},
            )
            return stop_loss, take_profit

    def _on_risk_update(self, payload: Any) -> None:
        """Apply parameter updates from ``risk.update`` events."""

        params = getattr(payload, "params", payload)
        if hasattr(payload, "new_capital") and payload.new_capital is not None:
            try:
                self.current_capital = float(payload.new_capital)
            except (TypeError, ValueError):
                pass
        if not isinstance(params, dict):
            return
        updates = {k: v for k, v in params.items() if hasattr(self.params, k)}
        if "stop_loss" in params:
            try:
                self.stop_loss_override = float(params["stop_loss"])
            except (TypeError, ValueError):
                pass
        if "exposure_limit_pct" in params:
            try:
                self.exposure_limit_pct = float(params["exposure_limit_pct"])
            except (TypeError, ValueError):
                pass
        if updates:
            for key, value in updates.items():
                setattr(self.params, key, value)
            logger.info(
                "Parâmetros do DRC atualizados via risk.update: %s",
                updates,
            )

    def _on_market_alert(self, payload: Any) -> None:
        """Handle ``market.alert`` events carrying ``drc_parameters``."""

        if not isinstance(payload, dict) and not hasattr(payload, "drc_parameters"):
            return
        params = (
            payload.get("drc_parameters")
            if isinstance(payload, dict)
            else getattr(payload, "drc_parameters", None)
        )
        if not isinstance(params, dict):
            return
        updates = {k: v for k, v in params.items() if hasattr(self.params, k)}
        if not updates:
            return
        for key, value in updates.items():
            setattr(self.params, key, value)
        logger.info(
            "Parâmetros do DRC atualizados via market.alert: %s",
            updates,
        )

    def _on_risk_manager_created(self, payload: Any) -> None:
        """Armazena informações iniciais do gerenciador de risco."""

        try:
            self.current_capital = float(payload.initial_capital)
            self.risk_manager_profile = str(payload.risk_profile)
            logger.info(
                "RiskManager criado: capital=%.2f perfil=%s",
                self.current_capital,
                self.risk_manager_profile,
            )
        except AttributeError:
            return

    def _on_risk_recalibrated(self, payload: Any) -> None:
        """Atualiza métricas de capital e drawdown."""

        if hasattr(payload, "current_capital") and payload.current_capital is not None:
            try:
                self.current_capital = float(payload.current_capital)
            except (TypeError, ValueError):
                pass
        if hasattr(payload, "drawdown_pct") and payload.drawdown_pct is not None:
            try:
                self.drawdown_pct = float(payload.drawdown_pct)
            except (TypeError, ValueError):
                pass
