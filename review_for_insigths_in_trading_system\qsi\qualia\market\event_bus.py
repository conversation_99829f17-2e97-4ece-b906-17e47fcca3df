from __future__ import annotations

from dataclasses import dataclass
from typing import Any, Dict, Sequence

from ..events import SystemEvent

MARKET_PATTERN_EVENT = "market.pattern_detected"
MARKET_PATTERN_RECORDED = "market.pattern_recorded"


@dataclass(kw_only=True)
class MarketPatternDetected(SystemEvent):
    """Payload for :data:`MARKET_PATTERN_EVENT`."""

    vector: Sequence[float]
    metadata: Dict[str, Any] | None = None


@dataclass(kw_only=True)
class MarketPatternRecorded(SystemEvent):
    """Payload for :data:`MARKET_PATTERN_RECORDED`."""

    pattern_id: str


__all__ = [
    "MARKET_PATTERN_EVENT",
    "MarketPatternDetected",
    "MARKET_PATTERN_RECORDED",
    "MarketPatternRecorded",
]
