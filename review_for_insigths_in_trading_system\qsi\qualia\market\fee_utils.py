"""Utility functions to retrieve dynamic trading fees from exchanges.

Currently supports Kucoin taker fee retrieval via ccxt. Additional helpers can
be added for other exchanges when necessary.
"""

from __future__ import annotations

from typing import Optional

import asyncio

import ccxt

from ..utils.logger import get_logger

logger = get_logger(__name__)


def fetch_kucoin_taker_fee(
    symbol: str,
    *,
    api_key: str | None = None,
    api_secret: str | None = None,
    password: str | None = None,
    timeout: float = 10.0,
) -> Optional[float]:
    """Return the taker fee rate for *symbol* on Kucoin.

    Deprecated. Use :func:`fetch_kucoin_taker_fee_async` instead.

    Parameters
    ----------
    symbol : str
        Trading pair in the format accepted by Kucoin REST/ccxt (e.g. ``"BTC-USDT"``).
    api_key, api_secret, password : str, optional
        Credentials for authenticated endpoints. *At the time of writing* the fee
        endpoint requires the *General* permission which means the request **must
        be signed**. For public-only keys a *403* will be returned.
    timeout : float, optional
        REST timeout in seconds. Defaults to ``10.0``.

    Returns
    -------
    Optional[float]
        The taker fee as a decimal fraction (e.g. ``0.001`` for *0.1%*). Returns
        ``None`` when the fee cannot be obtained.
    """
    try:
        exchange = ccxt.kucoin(
            {
                "apiKey": api_key or "",
                "secret": api_secret or "",
                "password": password or "",
                "enableRateLimit": True,
                "timeout": int(timeout * 1000),
            }
        )

        # Ensure markets are loaded before querying fees
        exchange.load_markets()

        fee_info = exchange.fetch_trading_fee(symbol)
        taker_fee = fee_info.get("taker")
        if taker_fee is not None:
            return float(taker_fee)
    except Exception as exc:  # pragma: no cover – network/credential issues
        logger.warning("Falha ao obter taker fee Kucoin via ccxt: %s", exc)
    finally:
        # Explicitly close the underlying session to avoid open handles when
        # running inside long-lived processes.
        try:
            exchange.close()
        except Exception:  # pragma: no cover – cleanup errors
            pass

    return None


async def fetch_kucoin_taker_fee_async(
    symbol: str,
    *,
    api_key: str | None = None,
    api_secret: str | None = None,
    password: str | None = None,
    timeout: float = 10.0,
) -> Optional[float]:
    """Return the taker fee rate for ``symbol`` asynchronously.

    This wraps :func:`fetch_kucoin_taker_fee` in :func:`asyncio.to_thread` to
    avoid blocking the event loop.
    """

    return await asyncio.to_thread(
        fetch_kucoin_taker_fee,
        symbol,
        api_key=api_key,
        api_secret=api_secret,
        password=password,
        timeout=timeout,
    )
