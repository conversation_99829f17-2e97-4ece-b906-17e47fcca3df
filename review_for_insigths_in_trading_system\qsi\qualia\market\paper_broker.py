"""Simulation broker utilities."""

from __future__ import annotations

import inspect
from typing import Optional, <PERSON><PERSON>, Dict

import asyncio
import aiohttp

import time
from threading import Lock


from ..config.settings import get_env
from ..utils.logger import get_logger

logger = get_logger(__name__)

# Cache for Kraken taker fee results with timestamp of retrieval
_KRAKEN_FEE_CACHE: Dict[str, Tuple[Optional[float], float]] = {}
_KRAKEN_FEE_CACHE_LOCK = Lock()
# Time-to-live for cache entries
KRAKEN_FEE_TTL = (
    float(get_env("QUALIA_KRAKEN_FEE_TTL", str(60 * 60), warn=False)) or 60 * 60
)
# Default pair for taker fee queries
DEFAULT_KRAKEN_PAIR = get_env("QUALIA_DEFAULT_FEE_PAIR", "ETHUSDT", warn=False)


def clear_kraken_fee_cache() -> None:
    """Remove all cached Kraken taker fee entries."""
    with _KRAKEN_FEE_CACHE_LOCK:
        _KRAKEN_FEE_CACHE.clear()


def purge_expired_fee_entries() -> None:
    """Remove expired entries from the taker fee cache."""
    now = time.time()
    with _KRAKEN_FEE_CACHE_LOCK:
        expired = [
            key
            for key, (_, ts) in _KRAKEN_FEE_CACHE.items()
            if now - ts >= KRAKEN_FEE_TTL
        ]
        for key in expired:
            _KRAKEN_FEE_CACHE.pop(key, None)


async def fetch_kraken_taker_fee(
    pair: str = DEFAULT_KRAKEN_PAIR,
    session: Optional[aiohttp.ClientSession] = None,
) -> Optional[float]:
    """Return the taker fee percentage for ``pair`` from Kraken.

    Parameters
    ----------
    pair : str, optional
        Asset pair to query. Defaults to ``DEFAULT_KRAKEN_PAIR`` which can
        be overridden via ``QUALIA_DEFAULT_FEE_PAIR``.

    Returns
    -------
    Optional[float]
        Fee percentage expressed as a decimal fraction, or ``None`` if
        retrieval fails.
    """
    purge_expired_fee_entries()
    now = time.time()
    with _KRAKEN_FEE_CACHE_LOCK:
        cached = _KRAKEN_FEE_CACHE.get(pair)
        if cached and now - cached[1] < KRAKEN_FEE_TTL:
            return cached[0]
        if cached:
            _KRAKEN_FEE_CACHE.pop(pair, None)

    url = f"https://api.kraken.com/0/public/AssetPairs?pair={pair}"
    close_session = False
    if session is None:
        session = aiohttp.ClientSession()
        close_session = True
    try:
        ctx = session.get(url, timeout=10)
        if inspect.iscoroutine(ctx):
            ctx = await ctx
        async with ctx as resp:
            resp.raise_for_status()
            data = await resp.json()
            pair_data = next(iter(data.get("result", {}).values()))
            fees = pair_data.get("fees")
            if fees:
                fee = float(fees[0][1]) / 100.0
                with _KRAKEN_FEE_CACHE_LOCK:
                    _KRAKEN_FEE_CACHE[pair] = (fee, time.time())
                return fee
    except Exception as exc:  # pragma: no cover - network issues
        logger.warning("Falha ao obter taker_fee da Kraken: %s", exc)
        with _KRAKEN_FEE_CACHE_LOCK:
            _KRAKEN_FEE_CACHE.pop(pair, None)
    finally:
        if close_session:
            await session.close()
    return None


class PaperBroker:
    """Simple broker for paper trading."""

    def __init__(self, fee_pct: Optional[float] = None) -> None:
        if fee_pct is None:
            try:
                asyncio.get_running_loop()
            except RuntimeError:
                fee_pct = asyncio.run(fetch_kraken_taker_fee())
            else:
                logger.warning("Ignorando busca de taker_fee; event loop já está ativo")
        if fee_pct is None:
            fee_pct = 0.0026
        self.fee_pct = fee_pct

    def calculate_fee(self, price: float, size: float) -> float:
        """Return fee amount for a trade."""
        return price * size * self.fee_pct

    @classmethod
    async def create(cls, *, pair: str = DEFAULT_KRAKEN_PAIR) -> "PaperBroker":
        """Return :class:`PaperBroker` loading fee from Kraken asynchronously."""
        fee_pct = await fetch_kraken_taker_fee(pair)
        return cls(fee_pct=fee_pct)
