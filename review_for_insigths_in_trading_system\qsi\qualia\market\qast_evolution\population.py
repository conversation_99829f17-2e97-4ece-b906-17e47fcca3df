"""Population management utilities for QAST."""

from __future__ import annotations

import logging
import random
from datetime import datetime, timezone
from typing import Any, Dict, Optional

import numpy as np
import pandas as pd

from ...strategies.strategy_utils import copy_strategy
from ...strategies.strategy_interface import TradingStrategy
from ...utils.logger import get_logger

logger = get_logger(__name__)


def initialize_population(
    engine: Any, mutation_strength: Optional[float] = None
) -> list[Any]:
    """Initialize the strategy population.

    Args:
        engine: Instance of ``QASTEvolutionaryStrategy`` that manages the
            population.
        mutation_strength: Custom mutation strength for the initial variants.
            If ``None`` the engine's ``base_mutation_strength`` is used.

    Returns:
        list[Any]: The list of strategy representations created for generation
        ``0``.

    Examples:
        >>> engine = QASTEvolutionaryStrategy(template, consciousness)
        >>> population = initialize_population(engine)
        >>> len(population)  # doctest: +SKIP
        32
    """
    logger.debug(
        f"QAST (ID: {id(engine)}): Iniciando inicialização da população. Tamanho desejado: {engine.population_size}"
    )
    engine.population = []
    if not engine.strategy_template:
        logger.error(
            "Strategy template não fornecida para QAST. População não pode ser inicializada."
        )
        return

    base_mutation = (
        mutation_strength
        if mutation_strength is not None
        else engine.base_mutation_strength
    )

    # Adicionar a estratégia original (template) à população
    original_candidate_raw = copy_strategy(engine.strategy_template)

    processed_original = None
    try:
        if isinstance(original_candidate_raw, dict):
            original_obj = engine._instantiate_from_config(original_candidate_raw)
        else:
            original_obj = original_candidate_raw

        setattr(original_obj, "id", "QAST_Original_Gen0")
        setattr(original_obj, "generation", 0)
        setattr(original_obj, "fitness", 0.0)

        obj_params: Dict[str, Any] = {}
        if hasattr(original_obj, "get_params") and callable(
            getattr(original_obj, "get_params")
        ):
            obj_params = original_obj.get_params()
        elif hasattr(original_obj, "params") and isinstance(
            getattr(original_obj, "params"), dict
        ):
            obj_params = getattr(original_obj, "params")
        elif hasattr(original_obj, "parameters") and isinstance(
            getattr(original_obj, "parameters"), dict
        ):
            obj_params = getattr(original_obj, "parameters")
        else:
            obj_params = {
                k: v
                for k, v in vars(original_obj).items()
                if not k.startswith("_")
                and k not in ["id", "generation", "fitness", "parameters"]
            }
        setattr(original_obj, "parameters", obj_params)
        processed_original = original_obj
    except Exception as exc:
        logger.error(
            f"QAST (ID: {id(engine)}): Falha ao preparar estratégia original: {exc}",
            exc_info=True,
        )
        return

    if processed_original is None:
        logger.error(
            "Não foi possível processar a estratégia original para a população QAST."
        )
        return

    engine.population.append(processed_original)
    original_id_for_log = (
        processed_original.get("id")
        if isinstance(processed_original, dict)
        else processed_original.id
    )
    logger.debug(
        f"Estratégia original (ID: {original_id_for_log}) adicionada à população QAST (Instância QAST ID: {id(engine)})."
    )

    # Gerar o restante da população inicial através de mutações da original
    for i in range(1, engine.population_size):
        variant_raw = copy_strategy(
            engine.strategy_template
        )  # Esta é a cópia da *configuração* da estratégia

        processed_variant = None

        try:
            if isinstance(variant_raw, dict):
                variant_obj = engine._instantiate_from_config(variant_raw)
            else:
                variant_obj = variant_raw

            setattr(variant_obj, "id", f"QAST_Gen0_Variant{i}")
            setattr(variant_obj, "generation", 0)
            setattr(variant_obj, "fitness", 0.0)

            obj_params: Dict[str, Any] = {}
            if hasattr(variant_obj, "get_params") and callable(
                getattr(variant_obj, "get_params")
            ):
                obj_params = variant_obj.get_params()
            elif hasattr(variant_obj, "params") and isinstance(
                getattr(variant_obj, "params"), dict
            ):
                obj_params = getattr(variant_obj, "params")
            elif hasattr(variant_obj, "parameters") and isinstance(
                getattr(variant_obj, "parameters"), dict
            ):
                obj_params = getattr(variant_obj, "parameters")
            else:
                obj_params = {
                    k: v
                    for k, v in vars(variant_obj).items()
                    if not k.startswith("_")
                    and k
                    not in [
                        "id",
                        "generation",
                        "fitness",
                        "parameters",
                    ]
                }
            setattr(variant_obj, "parameters", obj_params)
            processed_variant = variant_obj
        except Exception as exc:
            logger.error(
                f"QAST (ID: {id(engine)}): Falha ao preparar variante inicial: {exc}",
                exc_info=True,
            )
            continue

        if processed_variant is None:  # Se houve 'continue' no try-except
            continue

        engine._mutate_strategy(processed_variant, base_mutation)

        # Logging de debug atualizado
        variant_id_for_log = (
            processed_variant.get("id")
            if isinstance(processed_variant, dict)
            else processed_variant.id
        )
        params_for_log = None
        if isinstance(processed_variant, dict):
            params_for_log = processed_variant.get("parameters")
        elif hasattr(processed_variant, "parameters"):
            params_for_log = getattr(processed_variant, "parameters")

        if logger.isEnabledFor(logging.DEBUG):
            if params_for_log is not None:
                log_content = (
                    params_for_log
                    if engine.detailed_population_log
                    else list(params_for_log.keys())
                )
                log_label = (
                    "parameters"
                    if engine.detailed_population_log
                    else "parameters keys"
                )
                if logger.isEnabledFor(logging.DEBUG):
                    logger.debug(
                        "[DEBUG_PARAMS] QAST.initialize_population: variant %s.%s = %s",
                        variant_id_for_log,
                        log_label,
                        log_content,
                    )
            else:
                if logger.isEnabledFor(logging.DEBUG):
                    logger.debug(
                        "[DEBUG_PARAMS] QAST.initialize_population: variant %s (tipo %s) não tem 'parameters' acessíveis para log.",
                        variant_id_for_log,
                        type(processed_variant),
                    )

        engine.population.append(processed_variant)

    engine.evolution_history.append(
        {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "generation": engine.generation,
            "event": "population_initialized",
            "population_size": len(engine.population),
            "mutation_strength_used": base_mutation,
        }
    )
    logger.info(
        "QAST (ID: %s): população inicializada com %d estratégias",
        id(engine),
        len(engine.population),
    )
    # Handle empty population
    engine.best_strategy = engine.population[0] if engine.population else None
    return engine.population


def evolve_strategies(
    engine: Any,
    market_data: pd.DataFrame,
    cycles: int = 1,
    qast_tuning_directives: Optional[Any] = None,
    trace_id: Optional[str] = None,
) -> Optional[Any]:
    """Evolve the strategy population.

    Args:
        engine: Engine instance controlling the evolutionary process.
        market_data: Market data used to evaluate each strategy.
        cycles: Number of generations to run during this call.
        qast_tuning_directives: Optional directives from the metacognitive
            layer.
        trace_id: Identifier used to correlate logs, if provided.

    Returns:
        Optional[Any]: The best strategy representation after evolution, if
        available.

    Examples:
        >>> best = evolve_strategies(engine, df, cycles=3)
        >>> best is not None  # doctest: +SKIP
        True
    """
    if trace_id:
        logger.info(
            f"TraceID: {trace_id} - QAST (ID: {id(engine)}): Iniciando evolução por {cycles} ciclo(s). Geração atual: {engine.generation}."
        )
    else:
        logger.info(
            f"QAST (ID: {id(engine)}): Iniciando evolução por {cycles} ciclo(s). Geração atual: {engine.generation}."
        )

    # Aceitar dados em formato de dicionário ou DataFrame.
    if not isinstance(market_data, pd.DataFrame):
        try:
            if isinstance(market_data, dict):
                # Caso seja dict de listas ou DataFrames, tentar concatenação
                if all(isinstance(v, pd.DataFrame) for v in market_data.values()):
                    market_data = pd.concat(
                        list(market_data.values()), ignore_index=True
                    )
                else:
                    market_data = pd.DataFrame(market_data)
            else:
                market_data = pd.DataFrame(market_data)
        except Exception as exc:
            logger.error(
                "QAST (ID: %s): Falha ao converter market_data para DataFrame: %s",
                id(engine),
                exc,
            )
            return engine.get_best_strategy()

    engine.last_market_data_for_eval = market_data

    if not engine.population:
        logger.warning(
            f"QAST (ID: {id(engine)}): População vazia. Tentando inicializar a população antes de evoluir."
        )
        engine.initialize_population()
        if not engine.population:
            logger.error(
                f"QAST (ID: {id(engine)}): Falha ao inicializar população. Evolução não pode continuar."
            )
            return (
                engine.get_best_strategy()
            )  # Retorna a melhor atual (None se nenhuma)

    if market_data is None or market_data.empty:
        logger.error(
            f"QAST (ID: {id(engine)}): market_data está vazio ou None. Avaliação não pode ser feita. Retornando melhor estratégia atual."
        )
        return engine.get_best_strategy()

    for i in range(cycles):
        logger.info(
            f"QAST (ID: {id(engine)}): Ciclo de evolução {i+1}/{cycles} (Geração Global: {engine.generation}) iniciado."
        )

        # Avaliar a aptidão de cada estratégia na população
        logger.info(
            f"QAST (ID: {id(engine)}): Avaliando fitness da população (Geração: {engine.generation}, {len(engine.population)} indivíduos)..."
        )
        current_population_to_evaluate = list(
            engine.population
        )  # Copiar para evitar problemas com modificação durante iteração
        for strategy_repr in current_population_to_evaluate:
            strategy_id = (
                strategy_repr.get("id")
                if isinstance(strategy_repr, dict)
                else getattr(
                    strategy_repr,
                    "id",
                    f"unknown_id_{random.randint(0,10000)}",
                )
            )
            # Check if already evaluated this generation to avoid redundant evaluation
            if (
                strategy_id in engine.strategy_fitness
                and engine.strategy_fitness[strategy_id].get("generation_evaluated")
                == engine.generation
            ):
                if logger.isEnabledFor(logging.DEBUG):
                    logger.debug(
                        f"QAST (ID: {id(engine)}): Estratégia {strategy_id} já avaliada na geração {engine.generation}. Fitness: {engine.strategy_fitness[strategy_id]['fitness']:.4f}. Pulando reavaliação."
                    )
                continue

            # Obtain the TradingStrategy object from its representation
            actual_strategy_object: Optional[TradingStrategy] = None
            strategy_params_for_eval: Optional[Dict[str, Any]] = None

            if isinstance(
                strategy_repr, dict
            ):  # É um dict {id, generation, fitness, parameters}
                strategy_params_for_eval = strategy_repr.get("parameters")
                if strategy_params_for_eval is None:
                    logger.error(
                        f"QAST (ID: {id(engine)}): Representação de estratégia (dict) {strategy_id} não possui 'parameters'. Não pode ser avaliada."
                    )
                    engine.strategy_fitness[strategy_id] = {
                        "fitness": -float("inf"),
                        "details": "Missing parameters",
                        "generation_evaluated": engine.generation,
                    }
                    continue
                try:
                    # Tentar instanciar a estratégia a partir do template e dos parâmetros
                    try:
                        if "class_name" in strategy_repr:
                            actual_strategy_object = engine._instantiate_from_config(
                                strategy_repr, strategy_params_for_eval
                            )
                        elif isinstance(engine.strategy_template, dict):
                            actual_strategy_object = engine._instantiate_from_config(
                                engine.strategy_template, strategy_params_for_eval
                            )
                        elif hasattr(engine.strategy_template, "__call__"):
                            actual_strategy_object = engine.strategy_template(
                                **strategy_params_for_eval
                            )
                        elif hasattr(engine.strategy_template, "reconfigure"):
                            temp_strat_copy = copy_strategy(engine.strategy_template)
                            if not isinstance(temp_strat_copy, TradingStrategy):
                                raise TypeError(
                                    f"resultado de copy_strategy para {strategy_id} não é TradingStrategy: {type(temp_strat_copy)}"
                                )
                            temp_strat_copy.reconfigure(**strategy_params_for_eval)
                            actual_strategy_object = temp_strat_copy
                        else:
                            temp_strat_copy = copy_strategy(engine.strategy_template)
                            for p_name, p_val in strategy_params_for_eval.items():
                                if hasattr(temp_strat_copy, p_name):
                                    setattr(temp_strat_copy, p_name, p_val)
                                elif hasattr(temp_strat_copy, "params") and isinstance(
                                    getattr(temp_strat_copy, "params"), dict
                                ):
                                    getattr(temp_strat_copy, "params")[p_name] = p_val
                            actual_strategy_object = temp_strat_copy
                    except Exception:
                        raise

                    if not isinstance(actual_strategy_object, TradingStrategy):
                        if (
                            isinstance(actual_strategy_object, dict)
                            and "class_name" in actual_strategy_object
                        ):
                            actual_strategy_object = engine._instantiate_from_config(
                                actual_strategy_object,
                                actual_strategy_object.get(
                                    "parameters", strategy_params_for_eval
                                ),
                            )
                        if not isinstance(actual_strategy_object, TradingStrategy):
                            raise TypeError(
                                f"Objeto criado para {strategy_id} não é TradingStrategy: {type(actual_strategy_object)}"
                            )

                except Exception as e_instantiate:
                    logger.error(
                        f"QAST (ID: {id(engine)}): Erro ao instanciar/reconfigurar estratégia {strategy_id} a partir dos parâmetros {strategy_params_for_eval} e template {type(engine.strategy_template)}: {e_instantiate}",
                        exc_info=True,
                    )
                    engine.strategy_fitness[strategy_id] = {
                        "fitness": -float("inf"),
                        "details": f"Instantiation error: {e_instantiate}",
                        "generation_evaluated": engine.generation,
                    }
                    continue

            elif isinstance(
                strategy_repr, TradingStrategy
            ):  # É um objeto TradingStrategy
                actual_strategy_object = strategy_repr
                # Tentar obter parâmetros do objeto para consistência, se necessário para logging ou debug
                if hasattr(actual_strategy_object, "get_params") and callable(
                    getattr(actual_strategy_object, "get_params")
                ):
                    strategy_params_for_eval = actual_strategy_object.get_params()
                elif hasattr(actual_strategy_object, "params") and isinstance(
                    getattr(actual_strategy_object, "params"), dict
                ):
                    strategy_params_for_eval = getattr(actual_strategy_object, "params")
                else:  # Fallback para vars(), menos ideal
                    strategy_params_for_eval = {
                        k: v
                        for k, v in vars(actual_strategy_object).items()
                        if not k.startswith("_")
                        and k not in ["id", "generation", "fitness"]
                    }

            else:  # Tipo de representação desconhecido
                logger.error(
                    f"QAST (ID: {id(engine)}): Representação de estratégia {strategy_id} (tipo: {type(strategy_repr)}) não reconhecida. Não pode ser avaliada."
                )
                engine.strategy_fitness[strategy_id] = {
                    "fitness": -float("inf"),
                    "details": "Representação de estratégia desconhecida",
                    "generation_evaluated": engine.generation,
                }
                continue

            if actual_strategy_object is None:
                logger.error(
                    f"QAST (ID: {id(engine)}): Falha ao obter objeto de estratégia para {strategy_id}. Não pode ser avaliado."
                )
                engine.strategy_fitness[strategy_id] = {
                    "fitness": -float("inf"),
                    "details": "Falha ao obter objeto de estratégia",
                    "generation_evaluated": engine.generation,
                }
                continue

            # Realizar a avaliação
            # logger.debug(f"Avaliando estratégia {strategy_id}...") # Log já existe no _evaluate_strategy
            fitness, backtest_details = engine._evaluate_strategy(
                actual_strategy_object,
                market_data,
                trace_id=trace_id,
            )
            engine.strategy_fitness[strategy_id] = {
                "fitness": fitness,
                "details": backtest_details,
                "generation_evaluated": engine.generation,
            }
            # logger.debug(f"Estratégia {strategy_id} avaliada. Fitness: {fitness:.4f}") # Log já existe no _evaluate_strategy

        # Verificar se todos os fitness são -inf e abortar caso afirmativo
        fitness_values = [
            engine.strategy_fitness.get(getattr(s_repr, "id", ""), {}).get(
                "fitness", -float("inf")
            )
            for s_repr in current_population_to_evaluate
        ]
        if fitness_values and all(np.isneginf(val) for val in fitness_values):
            logger.error(
                f"QAST (ID: {id(engine)}): Todos os backtests falharam na geração {engine.generation}. Evolução abortada."
            )
            return None

        # Ordenar estratégias por fitness (do maior para o menor)
        sorted_strategies = sorted(
            current_population_to_evaluate,
            key=lambda s_repr: engine.strategy_fitness.get(
                getattr(s_repr, "id", ""), {}
            ).get("fitness", -float("inf")),
            reverse=True,
        )

        # Criar nova população
        new_population = []

        # Elitismo: manter as N melhores estratégias
        num_elites = int(len(sorted_strategies) * engine.current_elite_ratio)
        elites = sorted_strategies[:num_elites]
        new_population.extend(elites)
        if elites:
            logger.info(
                f"QAST (ID: {id(engine)}): {len(elites)} elites selecionados para a próxima geração. Melhor elite: {elites[0].get('id') if isinstance(elites[0], dict) else elites[0].id} (Fitness: {engine.strategy_fitness[elites[0].get('id') if isinstance(elites[0], dict) else elites[0].id]['fitness']:.4f})"
            )
        else:
            logger.warning(
                f"QAST (ID: {id(engine)}): Nenhum elite selecionado (num_elites={num_elites}, elite_ratio={engine.current_elite_ratio}, pop_size={len(sorted_strategies)})."
            )

        # Add some top non-elite strategies for diversity
        # ... existing code ...
        engine.crossover_child_count_this_generation = (
            0  # Resetar contador de filhos de crossover
        )
        engine.mutation_count_this_generation = 0  # Resetar contador de mutações

        # Preencher o restante da população com descendentes (crossover e mutação)
        logger.info(
            f"QAST (ID: {id(engine)}): Gerando {engine.population_size - len(new_population)} novos indivíduos para a Geração {engine.generation + 1}..."
        )
        parents_pool = engine._create_parents_pool(sorted_strategies)
        if not parents_pool:
            logger.warning(
                f"QAST (ID: {id(engine)}): Pool de pais vazio. Não é possível gerar novos indivíduos. População pode diminuir."
            )

        while len(new_population) < engine.population_size and parents_pool:
            parent1 = engine._tournament_selection(parents_pool)
            parent2 = engine._tournament_selection(parents_pool)

            if random.random() < engine.crossover_rate and parent1 and parent2:
                # Crossover
                # logger.debug(f"Realizando crossover entre {parent1.id} e {parent2.id}") # Log já existe no _perform_crossover
                child = engine._perform_crossover(
                    parent1, parent2, engine.generation + 1
                )
            else:
                # Clonar um dos pais (o melhor dos dois selecionados para o torneio "falho" de crossover)
                # Ou apenas pegar um aleatório se a seleção do torneio não for feita antes do random.random()
                # Vamos pegar parent1 como base para mutação se não houver crossover.
                # child = copy_strategy(parent1)
                # A lógica de _perform_crossover já pode retornar um clone de P1 se não fizer crossover real,
                # mas vamos garantir que temos um 'child' para mutar.
                # Para simplificar: se não cruzar, pegue o parent1 e apenas o mute depois.
                # Criar uma cópia para mutação, para não alterar o pai original no pool da geração atual.
                # child = copy_strategy(parent1) # Isso copia o objeto/dict
                child_base_repr = copy_strategy(
                    parent1
                )  # Isso copia a *representação* (obj ou dict)
                child_id = f"Gen{engine.generation + 1}_ClonedMut_{engine.mutation_count_this_generation}"
                engine.mutation_count_this_generation += 1

                # Atualizar a representação do filho clonado ANTES da mutação
                child_params = engine._get_strategy_parameters(
                    child_base_repr, ensure_copy=True
                )
                engine._update_strategy_representation_common_fields(
                    child_base_repr,
                    child_id,
                    engine.generation + 1,
                    child_params,
                )
                child = child_base_repr  # Agora child é a representação atualizada

            if (
                random.random() < engine.mutation_rate and child
            ):  # Aplicar mutação ao filho (de crossover ou clonado)
                # logger.debug(f"Mutando strategy {child.id}") # Log já existe no _mutate_strategy
                child = engine._mutate_strategy(
                    child, engine.current_mutation_strength
                )  # _mutate_strategy deve retornar a representação mutada

            if child:
                new_population.append(child)
            else:
                logger.warning(
                    f"QAST (ID: {id(engine)}): Falha ao gerar filho (crossover/mutação)."
                )

        engine.population = new_population
        engine.generation += 1
        # Limpar fitness da geração anterior para forçar reavaliação na próxima,
        # exceto para elites que podem ser carregados, mas a lógica atual reavalia.
        # Se quisermos carregar fitness de elites, precisaríamos de uma lógica mais complexa.
        # Por ora, a reavaliação de todos é mais simples.
        # engine.strategy_fitness.clear()  # Fitness is cleared per strategy_id and generation_evaluated

        logger.info(
            f"QAST (ID: {id(engine)}): Evolução para Geração {engine.generation} concluída. Tamanho da População: {len(engine.population)}"
        )

    # Save the top N strategies and feed them to QPM
    # ... existing code ...
    engine.best_strategy = engine.get_best_strategy()  # Atualiza engine.best_strategy
    if engine.best_strategy:
        best_id = (
            engine.best_strategy.get("id")
            if isinstance(engine.best_strategy, dict)
            else engine.best_strategy.id
        )
        best_fitness = engine.strategy_fitness.get(best_id, {}).get(
            "fitness", float("-inf")
        )
        logger.info(
            f"QAST (ID: {id(engine)}): Evolução finalizada. Melhor estratégia da Geração {engine.generation}: ID {best_id}, Fitness: {best_fitness:.4f}"
        )
    else:
        logger.warning(
            f"QAST (ID: {id(engine)}): Evolução finalizada, mas nenhuma melhor estratégia foi determinada."
        )
    return engine.best_strategy
