"""
Estratégias Evolucionárias baseadas no Ciclo QAST.

Este módulo implementa o mecanismo de adaptação de estratégias de trading
utilizando o ciclo QAST (Quantum Awareness, Self-reflection, Transformation)
do QUALIA, permitindo que as estratégias evoluam organicamente em resposta
às mudanças nas condições de mercado.
"""

import inspect
import logging
import random
import time
from typing import Any, Dict, List, Optional, Tuple

import numpy as np
import pandas as pd

from .qast_evolution import QASTEvolutionConfig

# YAA: Importar TradingStrategy para anotação de tipo
from ..strategies.strategy_interface import TradingStrategy
from ..strategies.strategy_utils import copy_strategy, load_strategy_class
from ..utils.logger import get_logger
from datadog import DogStatsd

from .qast_evolution.population import (
    initialize_population as _initialize_population_impl,
    evolve_strategies as _evolve_strategies_impl,
)
from .qast_evolution.genetic_ops import (
    _mutate_strategy as _mutate_strategy_impl,
    _perform_crossover as _perform_crossover_impl,
    _get_adjustable_params as _get_adjustable_params_impl,
    _set_param_value as _set_param_value_impl,
    _get_strategy_parameters as _get_strategy_parameters_impl,
    _update_strategy_representation_common_fields as _update_strategy_representation_common_fields_impl,
)

# YAA: Adicionar importação para load_strategy_class
# from ..strategies.strategy_utils import load_strategy_class # YAA: Comentado para quebrar dependência circular

logger = get_logger(__name__)


class QASTEvolutionaryStrategy:
    """
    Adapta estratégias de trading usando o ciclo QAST (Quantum Awareness, Self-reflection, Transformation).

    Esta classe gerencia uma população de variantes de estratégias de trading, avaliando
    seu desempenho em dados históricos e aplicando o ciclo QAST do QUALIA para guiar
    a evolução dessas estratégias, resultando em adaptação contínua às condições de mercado.
    """

    def __init__(
        self,
        strategy_template,
        qualia_consciousness,
        population_size=32,
        base_mutation_strength=0.1,  # YAA: Mantido default original se não fornecido
        base_elite_ratio=0.2,  # YAA: Mantido default original se não fornecido
        # YAA: Adicionado config opcional
        evolution_config: Optional[QASTEvolutionConfig] = None,
        config: Optional[Dict[str, Any]] = None,
        mode: str = "backtest",
        detailed_population_log: Optional[bool] = None,
        metrics_client: Optional[DogStatsd] = None,
    ):
        """
        Inicializa o mecanismo de evolução estratégica baseado em QAST.

        Args:
            strategy_template: Modelo base de estratégia a ser evoluído
            qualia_consciousness: Referência ao objeto de consciência do QUALIA
            population_size: Tamanho da população de estratégias a manter (default: 32)
            base_mutation_strength: Força base da mutação (default: 0.1, conforme sugestão para 0.3)
            base_elite_ratio: Porcentagem da população a ser considerada elite (default: 0.2, conforme sugestão para <=0.1)
            evolution_config: Conjunto de parâmetros de evolução. Quando ``None``
                são usados valores de :class:`QASTEvolutionConfig` com os
                padrões carregados do ambiente.
            config: Dicionário de configuração adicional (pode sobrescrever defaults ou passar novos params).
            mode: Modo de operação do trader ("backtest", "live" etc.).
            detailed_population_log: Se ``True`` registra os valores completos dos parâmetros de cada
                indivíduo ao inicializar a população. Com ``False`` apenas os nomes dos parâmetros são exibidos.
            metrics_client: Cliente opcional do DogStatsd para envio de métricas.
        """
        self.strategy_template = strategy_template
        self.consciousness = qualia_consciousness
        self.mode = mode
        self.metrics_client = metrics_client or DogStatsd()
        self.evolution_config = evolution_config or QASTEvolutionConfig()

        # Validate that the strategy template exposes a callable backtest method
        backtest_attr = None
        if isinstance(strategy_template, dict):
            class_name = strategy_template.get("class_name")
            if not class_name:
                raise ValueError(
                    "dicionário strategy_template deve conter 'class_name'."
                )
            strategy_cls = load_strategy_class(class_name)
            backtest_attr = getattr(strategy_cls, "backtest", None)
        else:
            backtest_attr = getattr(strategy_template, "backtest", None)

        if not callable(backtest_attr):
            raise ValueError(
                "strategy_template deve implementar um método 'backtest' chamável"
            )
        self.strategy_fitness: Dict[str, Dict[str, Any]] = {}
        self.population_size = (
            evolution_config.population_size
            if evolution_config is not None
            else population_size
        )
        if mode != "backtest" and config and "population_size_live" in config:
            self.population_size = config["population_size_live"]
        self.evolution_history: List[Dict[str, Any]] = []
        self.best_strategy: Optional[Any] = None
        self.generation = 0
        self.last_market_data_for_eval: Optional[pd.DataFrame] = None
        self.last_best_strategy_selection_generation: int = -1

        # YAA: Parâmetros para alimentar QPM com base nos melhores backtests
        self.N_BEST_STRATEGIES_FOR_QPM = (
            config.get("N_BEST_STRATEGIES_FOR_QPM", 3) if config else 3
        )
        self.QPM_PATTERN_WINDOW_SIZE = (
            config.get("QPM_PATTERN_WINDOW_SIZE", 20) if config else 20
        )

        # Parâmetros evolutivos base
        self.base_mutation_strength = base_mutation_strength
        self.base_elite_ratio = base_elite_ratio
        self.backtest_results_dir = self.evolution_config.backtest_results_dir

        effective_config = config if config is not None else {}

        # Parâmetros evolutivos detalhados da configuração
        self.generations_per_call = self.evolution_config.generations_per_call
        self.mutation_rate = self.evolution_config.mutation_rate
        self.crossover_rate = self.evolution_config.crossover_rate
        self.generations_per_call = effective_config.get(
            "generations_per_call", self.generations_per_call
        )
        self.mutation_rate = effective_config.get("mutation_rate", self.mutation_rate)
        self.crossover_rate = effective_config.get(
            "crossover_rate", self.crossover_rate
        )

        # Controle de verbosidade do log de inicialização da população. Pode ser
        # definido diretamente via parâmetro ou na configuração fornecida.
        self.detailed_population_log = (
            detailed_population_log
            if detailed_population_log is not None
            else effective_config.get("detailed_population_log", False)
        )

        # Tournament size pode vir de 'tournament_size' ou 'selective_pressure_adjustment'
        # YAA: Usar um default robusto (e.g., 3) e logar qual chave foi usada.
        tournament_source = "default"
        if "tournament_size" in effective_config:
            self.tournament_size = effective_config.get("tournament_size", 3)
            tournament_source = "tournament_size"
        elif "selective_pressure_adjustment" in effective_config:
            self.tournament_size = effective_config.get(
                "selective_pressure_adjustment", 3
            )
            tournament_source = "selective_pressure_adjustment"
        else:
            self.tournament_size = 3  # Default se nenhuma chave estiver presente

        self.config_selective_pressure_adjustment = (
            self.tournament_size
        )  # Manter para compatibilidade se usado em outro lugar

        # Parâmetros adaptativos (controlados pela QualiaConsciousness ou
        # diretivas)
        self.current_mutation_strength = base_mutation_strength
        self.current_elite_ratio = base_elite_ratio
        # Se True, foca em substituir os piores
        self.target_worst_performers_flag = False

        # Contador de gerações consecutivas com melhor fitness 0
        self._consecutive_zero_fitness_generations = 0
        # Limite padrão de gerações para interromper a evolução
        self.zero_fitness_threshold = effective_config.get("zero_fitness_threshold", 10)

        # YAA: Carregar metadados dos parâmetros para mutação QAST
        self.qast_params_meta: Dict[str, Dict[str, Any]] = effective_config.get(
            "qast_params_meta", {}
        )
        if not self.qast_params_meta:
            # Silencia aviso ruidoso gerando metadados padrão quando não especificados.
            logger.debug(
                f"QAST (ID: {id(self)}) 'qast_params_meta' não foi fornecido ou está vazio na configuração. "
                "Gerando metadados de parâmetros evoluíveis padrão a partir do strategy_template."
            )
            self.qast_params_meta = self._generate_default_qast_params_meta()
            if not self.qast_params_meta:
                logger.error(
                    f"QAST (ID: {id(self)}): Não foi possível gerar qast_params_meta default a partir do strategy_template "
                    f"(tipo: {type(self.strategy_template)}). A mutação de parâmetros QAST pode não funcionar como esperado. "
                    "Verifique se a strategy_template possui um atributo 'params' (dict) ou implementa 'get_params()'."
                )
            else:
                logger.debug(
                    f"QAST (ID: {id(self)}) gerou qast_params_meta default com {len(self.qast_params_meta)} entradas: {list(self.qast_params_meta.keys())}"
                )
        else:  # qast_params_meta foi fornecido explicitamente
            logger.debug(
                f"QAST (ID: {id(self)}) carregou qast_params_meta fornecido com {len(self.qast_params_meta)} entradas: {list(self.qast_params_meta.keys())}"
            )

        # YAA: Definir initial_capital e risk_per_trade_percentage a partir da
        # config ou defaults
        self.initial_capital = effective_config.get("initial_capital", 10000)
        self.risk_per_trade_percentage = effective_config.get(
            "risk_per_trade_percentage", 0.01
        )  # 1% default

        # YAA: Parâmetros para Elite Diversificado - Adicionar defaults
        self.diversity_ratio = effective_config.get(
            "diversity_ratio", 0.1
        )  # Percentual da população para diversidade
        self.diversity_source_from_best_non_elites = effective_config.get(
            "diversity_source_from_best_non_elites", True
        )

        # YAA: Contadores para nomear filhos de forma única por geração
        self.crossover_child_count_this_generation = 0
        self.mutation_count_this_generation = 0

        adjustable_params_summary = {
            "population_size": self.population_size,
            "base_mutation_strength": self.base_mutation_strength,
            "base_elite_ratio": self.base_elite_ratio,
            "generations_per_call": self.generations_per_call,
            "mutation_rate": self.mutation_rate,
            "crossover_rate": self.crossover_rate,
            "tournament_size": self.tournament_size,
            "initial_capital": self.initial_capital,
            "risk_per_trade_percentage": self.risk_per_trade_percentage,
            "diversity_ratio": self.diversity_ratio,
            "diversity_source_from_best_non_elites": self.diversity_source_from_best_non_elites,
            "N_BEST_STRATEGIES_FOR_QPM": self.N_BEST_STRATEGIES_FOR_QPM,
            "QPM_PATTERN_WINDOW_SIZE": self.QPM_PATTERN_WINDOW_SIZE,
            "tournament_source": tournament_source,
            "qast_params_meta_count": len(self.qast_params_meta),
        }

        logger.info(
            "QASTEvolutionaryStrategy inicializada (ID: %s) com parametros: %s",
            id(self),
            adjustable_params_summary,
        )

    def _generate_default_qast_params_meta(self) -> Dict[str, Dict[str, Any]]:
        """
        Gera um qast_params_meta default inspecionando self.strategy_template.params.
        Considera apenas tipos int, float e bool.
        """
        default_meta = {}
        strategy_params = None

        if hasattr(self.strategy_template, "get_params") and callable(
            getattr(self.strategy_template, "get_params")
        ):
            strategy_params = self.strategy_template.get_params()
        elif hasattr(self.strategy_template, "get_parameters") and callable(
            getattr(self.strategy_template, "get_parameters")
        ):
            strategy_params = self.strategy_template.get_parameters()
        elif hasattr(self.strategy_template, "params") and isinstance(
            getattr(self.strategy_template, "params"), dict
        ):
            strategy_params = getattr(self.strategy_template, "params")
        elif hasattr(self.strategy_template, "parameters") and isinstance(
            getattr(self.strategy_template, "parameters"), dict
        ):
            strategy_params = getattr(self.strategy_template, "parameters")
        elif isinstance(self.strategy_template, dict):
            # Permitir templates em formato de dicionário
            strategy_params = self.strategy_template.get("params")

        if not strategy_params:
            logger.warning(
                f"QAST (ID: {id(self)}): _generate_default_qast_params_meta: strategy_template (tipo: {type(self.strategy_template)}) "
                "não possui 'params' (dict) ou 'get_params()' para introspecção. Não é possível gerar defaults."
            )
            return default_meta

        logger.debug(
            f"QAST (ID: {id(self)}): Gerando qast_params_meta default a partir dos parâmetros da estratégia: {list(strategy_params.keys())}"
        )

        for p_name, p_value in strategy_params.items():
            if isinstance(p_value, bool):
                default_meta[p_name] = {"type": "bool"}
                if logger.isEnabledFor(logging.DEBUG):
                    logger.debug(
                        "  -> Param '%s' (bool): adicionado para evolução (flip).",
                        p_name,
                    )
            elif isinstance(p_value, int):
                abs_val = abs(p_value)
                # Define um range, garantindo que min < max e step seja razoável.
                # Se valor for 0, usa um range pequeno em torno de 0.
                # Se valor for pequeno, garante um range mínimo.
                delta = max(5, abs_val // 2)  # Garante um delta de pelo menos 5
                if p_value == 0:
                    p_min, p_max = -5, 5
                elif (
                    abs_val < 10
                ):  # Para valores pequenos, usar um range fixo para evitar min=max
                    p_min = p_value - 5
                    p_max = p_value + 5
                else:  # Range proporcional
                    p_min = p_value - delta
                    p_max = p_value + delta

                if p_min == p_max:  # Evitar min=max para int
                    p_max = p_min + 1

                default_meta[p_name] = {
                    "type": "int",
                    "min": p_min,
                    "max": p_max,
                    "step": 1,
                }
                if logger.isEnabledFor(logging.DEBUG):
                    logger.debug(
                        "  -> Param '%s' (int, valor: %s): meta default gerada (min: %s, max: %s, step: 1).",
                        p_name,
                        p_value,
                        p_min,
                        p_max,
                    )
            elif isinstance(p_value, float):
                abs_val_float = abs(p_value)
                if p_value == 0.0:
                    # Range simétrico pequeno em torno de 0 para floats
                    p_min_float, p_max_float = -0.5, 0.5
                    p_step_float = 0.01
                else:
                    # Range proporcional (+/- 50% do valor)
                    p_min_float = p_value * 0.5
                    p_max_float = p_value * 1.5
                    # Ajustar se p_value for negativo para manter min < max
                    if p_value < 0:
                        p_min_float, p_max_float = p_max_float, p_min_float

                    # Garantir que min < max para floats também, especialmente se p_value for muito pequeno
                    if (
                        abs(p_max_float - p_min_float) < 1e-9
                    ):  # Se min e max são efetivamente iguais
                        p_min_float = (
                            p_value - abs_val_float * 0.1 - 1e-6
                        )  # Adiciona uma pequena perturbação
                        p_max_float = p_value + abs_val_float * 0.1 + 1e-6

                    # Definir um step razoável (ex: 1% do range ou 10% do valor absoluto)
                    p_step_float = abs((p_max_float - p_min_float) / 100.0)
                    if p_step_float < 1e-9:  # Se step for muito pequeno ou zero
                        p_step_float = (
                            abs_val_float * 0.01 if abs_val_float > 1e-6 else 0.001
                        )

                default_meta[p_name] = {
                    "type": "float",
                    "min": p_min_float,
                    "max": p_max_float,
                    "step": p_step_float,
                }
                if logger.isEnabledFor(logging.DEBUG):
                    logger.debug(
                        "  -> Param '%s' (float, valor: %s): meta default gerada (min: %.4g, max: %.4g, step: %.4g).",
                        p_name,
                        p_value,
                        p_min_float,
                        p_max_float,
                        p_step_float,
                    )
            else:
                if logger.isEnabledFor(logging.DEBUG):
                    logger.debug(
                        f"  -> Param '{p_name}' (tipo: {type(p_value)}) não é int, float ou bool. Ignorando para geração de qast_params_meta default."
                    )

        if not default_meta:
            logger.warning(
                f"QAST (ID: {id(self)}): Nenhum parâmetro numérico ou booleano encontrado em strategy_template.params para gerar qast_params_meta default."
            )

        return default_meta

    def _instantiate_from_config(
        self, template: Dict[str, Any], params: Optional[Dict[str, Any]] = None
    ) -> TradingStrategy:
        """Instantiate a strategy from a configuration dictionary."""
        class_name = template.get("class_name")
        if not class_name:
            raise ValueError("Dicionário de template deve conter 'class_name'.")

        strategy_cls = load_strategy_class(class_name)
        context: Dict[str, Any] = {}
        if "symbol" in template:
            context["symbol"] = template["symbol"]
        if "timeframe" in template:
            context["timeframe"] = template["timeframe"]

        strategy_params = params if params is not None else template.get("params", {})

        constructor_sig = inspect.signature(strategy_cls.__init__)
        kwargs: Dict[str, Any] = {}
        for param in constructor_sig.parameters.values():
            if param.name == "self":
                continue
            if param.name == "symbol" and "symbol" in template:
                kwargs["symbol"] = template["symbol"]
            elif param.name == "timeframe" and "timeframe" in template:
                kwargs["timeframe"] = template["timeframe"]
            elif param.name == "params":
                kwargs["params"] = strategy_params
            elif param.name == "context":
                kwargs["context"] = context
            elif param.name == "shared_context":
                kwargs["shared_context"] = context

        return strategy_cls(**kwargs)

    def initialize_population(self, mutation_strength: Optional[float] = None):
        """Initialize the population using helper implementation."""
        return _initialize_population_impl(self, mutation_strength)

    def evolve_strategies(
        self,
        market_data,
        cycles: int = 1,
        qast_tuning_directives: Optional[Any] = None,
        trace_id: Optional[str] = None,
    ):
        """Delegate to implementation in :mod:`qast_evolution.population`."""
        return _evolve_strategies_impl(
            self,
            market_data,
            cycles=cycles,
            qast_tuning_directives=qast_tuning_directives,
            trace_id=trace_id,
        )

    def _adjust_evolution_parameters(self, qast_tuning_directives: Any):
        # self.current_mutation_strength = max(0.01, min(1.0, new_mutation_strength))
        # self.current_elite_ratio = max(0.05, min(0.5, new_elite_ratio))

        logger.info(
            f"QAST (ID: {id(self)}): Parâmetros de evolução ajustados por diretivas. "
            f"Nova Força Mutação: {self.current_mutation_strength:.3f} (Base: {self.base_mutation_strength:.3f}), "
            f"Novo Ratio Elite: {self.current_elite_ratio:.2f} (Base: {self.base_elite_ratio:.2f}), "
            f"Target Worst: {self.target_worst_performers_flag}"
        )

    def get_best_strategy(self) -> Optional[Any]:
        """Return the best strategy of the current population based on fitness."""
        if not self.population:
            logger.warning(
                f"QAST (ID: {id(self)}): Tentativa de obter melhor estratégia, mas a população está vazia."
            )
            return None

        best_repr = None
        best_fitness = -float("inf")

        for strat_repr in self.population:
            strat_id = (
                strat_repr.get("id")
                if isinstance(strat_repr, dict)
                else getattr(strat_repr, "id", None)
            )
            fitness = self.strategy_fitness.get(strat_id, {}).get(
                "fitness", -float("inf")
            )
            if fitness > best_fitness:
                best_fitness = fitness
                best_repr = strat_repr

        return copy_strategy(best_repr) if best_repr is not None else None

    def get_best_fitness(self) -> float:
        """Return the highest fitness value recorded for the current generation."""
        if not self.strategy_fitness:
            return 0.0
        best = max(
            (data.get("fitness", 0.0) for data in self.strategy_fitness.values()),
            default=0.0,
        )
        if best == -float("inf"):
            return 0.0
        return float(best)

    def evolve_population(
        self,
        market_data,
        cycles: int = 1,
        qast_tuning_directives: Optional[Any] = None,
        trace_id: Optional[str] = None,
    ):
        """Wrapper over :meth:`evolve_strategies` monitoring fitness stagnation."""

        result = None
        for _ in range(cycles):
            start = time.perf_counter()
            result = self.evolve_strategies(
                market_data,
                cycles=1,
                qast_tuning_directives=qast_tuning_directives,
                trace_id=trace_id,
            )
            elapsed_ms = (time.perf_counter() - start) * 1000
            if self.metrics_client:
                tags = [f"trace_id:{trace_id}"] if trace_id else None
                self.metrics_client.timing(
                    "qast.generation_time_ms",
                    elapsed_ms,
                    tags=tags,
                )
                self.metrics_client.gauge(
                    "qast.num_crossovers",
                    self.crossover_child_count_this_generation,
                    tags=tags,
                )
                self.metrics_client.gauge(
                    "qast.best_fitness",
                    self.get_best_fitness(),
                    tags=tags,
                )
            if self.get_best_fitness() == 0:
                self._consecutive_zero_fitness_generations += 1
                if (
                    self._consecutive_zero_fitness_generations
                    >= self.zero_fitness_threshold
                ):
                    logger.warning(
                        "QAST (ID: %s): Limite de %d gera\xe7\xf5es consecutivas com fitness 0 atingido. Evolu\xe7\xe3o interrompida.",
                        id(self),
                        self.zero_fitness_threshold,
                    )
                    break
            else:
                self._consecutive_zero_fitness_generations = 0

        return result

    def _mutate_strategy(self, strategy_representation, mutation_strength):
        """Delegate mutation logic to :mod:`qast_evolution.genetic_ops`."""
        return _mutate_strategy_impl(self, strategy_representation, mutation_strength)

    def _get_adjustable_params(
        self, current_strategy_params: Any
    ) -> Dict[str, Dict[str, Any]]:
        """Delegate to implementation in :mod:`qast_evolution.genetic_ops`."""
        return _get_adjustable_params_impl(self, current_strategy_params)

    def _set_param_value(self, target_entity, param_name, value):
        """Delegate to implementation in :mod:`qast_evolution.genetic_ops`."""
        return _set_param_value_impl(self, target_entity, param_name, value)

    def _get_strategy_parameters(
        self, strategy_representation: Any, ensure_copy: bool = False
    ) -> Dict[str, Any]:
        """Delegate to implementation in :mod:`qast_evolution.genetic_ops`."""
        return _get_strategy_parameters_impl(self, strategy_representation, ensure_copy)

    def _update_strategy_representation_common_fields(
        self,
        strategy_representation: Any,
        new_id: str,
        generation: int,
        params: Optional[Dict[str, Any]] = None,
    ) -> None:
        """Delegate to implementation in :mod:`qast_evolution.genetic_ops`."""
        return _update_strategy_representation_common_fields_impl(
            self, strategy_representation, new_id, generation, params
        )

    def _evaluate_strategy(
        self,
        strategy: TradingStrategy,
        market_data: pd.DataFrame,
        trace_id: Optional[str] = None,
    ) -> Tuple[float, Dict[str, Any]]:
        """
        Avalia a aptidão de uma única estratégia usando dados de mercado históricos.
        Retorna o fitness e um dicionário com detalhes do backtest.
        """
        strategy_id_for_log = getattr(strategy, "id", "unknown_strategy_object")
        if trace_id:
            logger.debug(
                "TraceID: %s - QAST (ID: %s): Avaliando fitness para Estratégia ID: %s...",
                trace_id,
                id(self),
                strategy_id_for_log,
            )
        else:
            logger.debug(
                "QAST (ID: %s): Avaliando fitness para Estratégia ID: %s...",
                id(self),
                strategy_id_for_log,
            )

        if not isinstance(strategy, TradingStrategy):
            logger.error(
                f"QAST (ID: {id(self)}): Objeto fornecido para avaliação (ID: {strategy_id_for_log}, Tipo: {type(strategy)}) não é uma instância de TradingStrategy. Fitness: -inf."
            )
            return -float("inf"), {
                "error": "Not a TradingStrategy instance",
                "trades": [],
                "final_balance": self.initial_capital,
                "pnl_pct": 0.0,
                "sharpe": 0.0,
                "max_drawdown": 0.0,
            }

        # Executa o backtest utilizando o método da própria estratégia, caso
        # esteja disponível. Caso contrário, retorna erro.
        if hasattr(strategy, "backtest") and callable(getattr(strategy, "backtest")):
            try:
                backtest_func = strategy.backtest
                sig = inspect.signature(backtest_func)
                call_kwargs = {
                    "initial_capital": self.initial_capital,
                    "risk_per_trade_pct": self.risk_per_trade_percentage,
                }

                if "market_data_map" in sig.parameters:
                    param = sig.parameters["market_data_map"]
                    ann = param.annotation
                    accepts_dict = False
                    if ann is dict:
                        accepts_dict = True
                    elif getattr(ann, "__origin__", None) is dict:
                        accepts_dict = True

                    if accepts_dict:
                        call_kwargs["market_data_map"] = {
                            getattr(strategy, "symbol", "N/A"): market_data
                        }
                    else:
                        call_kwargs["market_data_map"] = market_data
                elif "market_data" in sig.parameters:
                    call_kwargs["market_data"] = market_data
                else:
                    # Fallback para o nome original usado anteriormente
                    call_kwargs["market_data_map"] = market_data

                if "timeframe" in sig.parameters:
                    call_kwargs["timeframe"] = getattr(strategy, "timeframe", None)

                backtest_result = backtest_func(**call_kwargs)
            except Exception as exc:
                logger.error(
                    f"QAST (ID: {id(self)}): Erro ao executar backtest da estratégia {strategy_id_for_log}: {exc}",
                    exc_info=True,
                )
                return -float("inf"), {
                    "error": f"backtest failed: {exc}",
                    "trades": [],
                    "final_balance": self.initial_capital,
                    "pnl_pct": 0.0,
                    "sharpe": 0.0,
                    "max_drawdown": 0.0,
                }
        else:
            logger.error(
                f"QAST (ID: {id(self)}): Estratégia {strategy_id_for_log} não possui método backtest."
            )
            return -float("inf"), {
                "error": "strategy has no backtest method",
                "trades": [],
                "final_balance": self.initial_capital,
                "pnl_pct": 0.0,
                "sharpe": 0.0,
                "max_drawdown": 0.0,
            }

        trade_history = backtest_result.get("trades_details") or backtest_result.get(
            "trade_history", []
        )
        total_fees = sum(t.get("fee", 0.0) for t in trade_history)
        current_balance = self.initial_capital
        portfolio_values = [current_balance]
        pnl_values = []
        for trade in trade_history:
            pnl = trade.get("pnl_abs") or trade.get("pnl", 0.0)
            pnl_values.append(pnl)
            current_balance += pnl - trade.get("fee", 0.0)
            portfolio_values.append(current_balance)

        num_winning_trades = sum(1 for p in pnl_values if p > 0)
        num_losing_trades = sum(1 for p in pnl_values if p < 0)
        avg_trade_pnl = np.mean(pnl_values) if pnl_values else 0.0
        avg_win_pnl = (
            np.mean([p for p in pnl_values if p > 0]) if num_winning_trades else 0.0
        )
        avg_loss_pnl = (
            np.mean([p for p in pnl_values if p < 0]) if num_losing_trades else 0.0
        )

        gross_profit = sum(p for p in pnl_values if p > 0)
        gross_loss = abs(sum(p for p in pnl_values if p < 0))
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else float("inf")

        peak = portfolio_values[0]
        max_drawdown_pct = 0.0
        for value in portfolio_values:
            if value > peak:
                peak = value
            drawdown = (peak - value) / peak if peak > 0 else 0.0
            if drawdown > max_drawdown_pct:
                max_drawdown_pct = drawdown
        max_drawdown_pct *= 100

        if len(pnl_values) > 1 and np.std(pnl_values) > 0:
            returns = [p / self.initial_capital for p in pnl_values]
            sharpe_ratio = (np.mean(returns) / np.std(returns)) * np.sqrt(len(returns))
        else:
            sharpe_ratio = 0.0

        pnl_abs = current_balance - self.initial_capital
        # Fitness é o PnL absoluto normalizado pelo drawdown máximo percentual.
        if max_drawdown_pct > 0:
            fitness = pnl_abs / (max_drawdown_pct / 100)
        else:
            fitness = pnl_abs

        backtest_summary = {
            "trades": len(trade_history),
            "final_balance": current_balance,
            "pnl_abs": pnl_abs,
            "pnl_pct": (
                (pnl_abs / self.initial_capital * 100)
                if self.initial_capital > 0
                else 0.0
            ),
            "win_rate": (
                (num_winning_trades / len(trade_history) * 100)
                if trade_history
                else 0.0
            ),
            "sharpe": sharpe_ratio,
            "max_drawdown": max_drawdown_pct,
            "profit_factor": profit_factor,
            "average_trade_pnl": avg_trade_pnl,
            "average_win_pnl": avg_win_pnl,
            "average_loss_pnl": avg_loss_pnl,
            "num_winning_trades": num_winning_trades,
            "num_losing_trades": num_losing_trades,
            "total_fees": total_fees,
        }
        if logger.isEnabledFor(logging.DEBUG):
            if trace_id:
                logger.debug(
                    "TraceID: %s - QAST (ID: %s): Avaliação da Estratégia ID: %s concluída. Fitness: %.4f. PnL: %.2f%%. Trades: %s. Sharpe: %.2f. Max DD: %.2f%%.",
                    trace_id,
                    id(self),
                    strategy_id_for_log,
                    fitness,
                    backtest_summary["pnl_pct"],
                    backtest_summary["trades"],
                    backtest_summary["sharpe"],
                    backtest_summary["max_drawdown"],
                )
            else:
                logger.debug(
                    "QAST (ID: %s): Avaliação da Estratégia ID: %s concluída. Fitness: %.4f. PnL: %.2f%%. Trades: %s. Sharpe: %.2f. Max DD: %.2f%%.",
                    id(self),
                    strategy_id_for_log,
                    fitness,
                    backtest_summary["pnl_pct"],
                    backtest_summary["trades"],
                    backtest_summary["sharpe"],
                    backtest_summary["max_drawdown"],
                )

        # YAA: Salvar resultados detalhados do backtest se um diretório estiver configurado
        # (Esta lógica foi movida para _save_detailed_backtest_results)

        # Salvar N melhores estratégias da geração atual
        # (Esta lógica foi movida para _save_n_best_strategies_and_feed_qpm)

        if logger.isEnabledFor(logging.DEBUG):
            if trace_id:
                logger.debug(
                    f"TraceID: {trace_id} - QAST (ID: {id(self)}): Retornando fitness {fitness:.4f} para estratégia {strategy_id_for_log}"
                )
            else:
                logger.debug(
                    f"QAST (ID: {id(self)}): Retornando fitness {fitness:.4f} para estratégia {strategy_id_for_log}"
                )
        return fitness, backtest_summary

    def _save_detailed_backtest_results(
        self,
        strategy_id: str,
        generation: int,
        backtest_details: Dict[str, Any],
        trade_history: List[Dict[str, Any]],
    ):
        # ... existing code ...
        if self.best_strategy is None:
            if not self.population:
                logger.warning(
                    f"QAST (ID: {id(self)}): Tentativa de obter melhor estratégia, mas a população está vazia."
                )
                return None

            # Avaliar população se ainda não foi feito ou se fitness não está calculado
            needs_evaluation = False
            if not self.strategy_fitness:
                needs_evaluation = True
            else:
                # Verificar se todos na população atual têm fitness para a geração atual
                for strat_repr in self.population:
                    s_id = (
                        strat_repr.get("id")
                        if isinstance(strat_repr, dict)
                        else getattr(strat_repr, "id", None)
                    )
                    if s_id and (
                        s_id not in self.strategy_fitness
                        or self.strategy_fitness[s_id].get("generation_evaluated")
                        != self.generation
                    ):
                        needs_evaluation = True
                        logger.info(
                            f"QAST (ID: {id(self)}): Estratégia {s_id} precisa de avaliação para a geração {self.generation}. Forçando reavaliação antes de get_best_strategy."
                        )
                        break

            if (
                needs_evaluation
                and self.last_market_data_for_eval is not None
                and not self.last_market_data_for_eval.empty
            ):
                logger.info(
                    f"QAST (ID: {id(self)}): Forçando avaliação da população (Geração {self.generation}) antes de determinar a melhor estratégia, usando últimos dados de mercado disponíveis."
                )
                # Esta chamada pode ser custosa se chamada muitas vezes sem necessidade.
                # A lógica acima tenta minimizar isso.
                # Temporariamente removendo a chamada direta a evolve_strategies para evitar loop se chamada de dentro dela.
                # self.evolve_strategies(market_data=self.last_market_data_for_eval, cycles=0) # cycles=0 para apenas avaliar
                # Substituído por avaliação manual se necessário:
                for strategy_repr_eval in self.population:
                    s_id_eval = (
                        strategy_repr_eval.get("id")
                        if isinstance(strategy_repr_eval, dict)
                        else getattr(strategy_repr_eval, "id", None)
                    )
                    if s_id_eval and (
                        s_id_eval not in self.strategy_fitness
                        or self.strategy_fitness[s_id_eval].get("generation_evaluated")
                        != self.generation
                    ):
                        actual_strat_obj_eval: Optional[TradingStrategy] = None
                        if isinstance(strategy_repr_eval, dict):
                            strat_params_eval = strategy_repr_eval.get("parameters")
                            if strat_params_eval:
                                if hasattr(self.strategy_template, "__call__"):
                                    actual_strat_obj_eval = self.strategy_template(
                                        **strat_params_eval
                                    )
                                elif hasattr(self.strategy_template, "reconfigure"):
                                    temp_strat_copy_eval = copy_strategy(
                                        self.strategy_template
                                    )
                                    temp_strat_copy_eval.reconfigure(
                                        **strat_params_eval
                                    )
                                    actual_strat_obj_eval = temp_strat_copy_eval
                        elif isinstance(strategy_repr_eval, TradingStrategy):
                            actual_strat_obj_eval = strategy_repr_eval

                        if actual_strat_obj_eval:
                            fitness_eval, details_eval = self._evaluate_strategy(
                                actual_strat_obj_eval,
                                self.last_market_data_for_eval,
                                trace_id=None,
                            )
                            self.strategy_fitness[s_id_eval] = {
                                "fitness": fitness_eval,
                                "details": details_eval,
                                "generation_evaluated": self.generation,
                            }
                        else:
                            self.strategy_fitness[s_id_eval] = {
                                "fitness": -float("inf"),
                                "details": "Could not get strategy object in get_best_strategy pre-eval",
                                "generation_evaluated": self.generation,
                            }

            # Ordenar novamente com base no fitness atualizado
            # Garantir que todos os IDs na população estão no strategy_fitness
            valid_fitness_entries = {
                sid: data
                for sid, data in self.strategy_fitness.items()
                if data.get("generation_evaluated") == self.generation
            }
            if not valid_fitness_entries:
                logger.warning(
                    f"QAST (ID: {id(self)}): Nenhuma entrada de fitness válida encontrada para a geração {self.generation} ao tentar obter a melhor estratégia."
                )
                return None

            sorted_population_reprs = sorted(
                self.population,
                key=lambda s_repr: valid_fitness_entries.get(
                    (
                        s_repr.get("id")
                        if isinstance(s_repr, dict)
                        else getattr(s_repr, "id", "")
                    ),
                    {},
                ).get("fitness", -float("inf")),
                reverse=True,
            )
            if sorted_population_reprs:
                self.best_strategy = copy_strategy(
                    sorted_population_reprs[0]
                )  # Retorna uma cópia da representação
                best_s_id = (
                    self.best_strategy.get("id")
                    if isinstance(self.best_strategy, dict)
                    else getattr(self.best_strategy, "id", "N/A")
                )
                best_s_fitness = valid_fitness_entries.get(best_s_id, {}).get(
                    "fitness", -float("inf")
                )
                logger.info(
                    f"QAST (ID: {id(self)}): Melhor estratégia selecionada (Geração {self.generation}): ID {best_s_id}, Fitness: {best_s_fitness:.4f}"
                )
                self.last_best_strategy_selection_generation = self.generation
            else:
                logger.warning(
                    f"QAST (ID: {id(self)}): População ordenada resultou vazia. Nenhuma melhor estratégia pôde ser determinada."
                )
                self.best_strategy = None
                self.last_best_strategy_selection_generation = self.generation
        elif self.last_best_strategy_selection_generation != self.generation:
            # Se já temos um self.best_strategy, mas é de uma geração anterior, precisamos reavaliar.
            # Esta situação pode ocorrer se get_best_strategy é chamado múltiplas vezes
            # sem um ciclo de evolve_strategies completo no meio.
            logger.info(
                f"QAST (ID: {id(self)}): Cache da melhor estratégia é da geração {self.last_best_strategy_selection_generation}, mas estamos na {self.generation}. Forçando nova seleção."
            )
            self.best_strategy = None  # Força re-seleção
            self.last_best_strategy_selection_generation = (
                self.generation
            )  # Atualiza para evitar loop aqui
            return self.get_best_strategy()  # Chama recursivamente para re-selecionar

        return self.best_strategy

    def _create_parents_pool(self, sorted_strategies: List[Any]) -> List[Any]:
        """Return a subset of ``sorted_strategies`` to serve as parents.

        The pool excludes elite individuals and is at least two elements long
        whenever possible.
        """
        if not sorted_strategies:
            return []

        population_len = len(sorted_strategies)
        num_elites = int(population_len * self.current_elite_ratio)
        candidate_count = max(2, int(population_len * 0.5))

        if candidate_count <= num_elites:
            candidate_count = max(2, population_len - num_elites)

        parents = sorted_strategies[num_elites:candidate_count]

        if len(parents) < 2:
            parents = sorted_strategies[: min(population_len, 2)]

        return parents

    def _tournament_selection(self, participants: List[Any]) -> Optional[Any]:
        """
        Seleciona um indivíduo de um grupo de participantes usando seleção por torneio.
        Retorna a representação da estratégia (objeto ou dict).
        """
        if not participants:
            logger.warning(
                f"QAST (ID: {id(self)}): Seleção por torneio chamada com lista de participantes vazia."
            )
            return None

        # Garantir que tournament_size não seja maior que o número de participantes
        actual_tournament_size = min(self.tournament_size, len(participants))
        if actual_tournament_size <= 0:
            logger.warning(
                f"QAST (ID: {id(self)}): Tamanho do torneio inválido ({actual_tournament_size}) para {len(participants)} participantes. Retornando participante aleatório."
            )
            return random.choice(participants) if participants else None

        tournament_candidates = random.sample(participants, actual_tournament_size)
        if logger.isEnabledFor(logging.DEBUG):
            logger.debug(
                f"QAST (ID: {id(self)}): Torneio com {len(tournament_candidates)} candidatos selecionados aleatoriamente."
            )

        winner_repr = None
        best_fitness_in_tournament = -float("inf")

        for candidate_repr in tournament_candidates:
            candidate_id = (
                getattr(candidate_repr, "id", None)
                if not isinstance(candidate_repr, dict)
                else candidate_repr.get("id")
            )
            if not candidate_id:
                logger.error(
                    f"QAST (ID: {id(self)}): Candidato no torneio (Tipo: {type(candidate_repr)}) sem ID. Ignorando."
                )
                continue

            # Usar fitness da geração correta
            candidate_fitness_data = self.strategy_fitness.get(candidate_id)
            if (
                candidate_fitness_data
                and candidate_fitness_data.get("generation_evaluated")
                == self.generation
            ):
                current_fitness = candidate_fitness_data.get("fitness", -float("inf"))
                if logger.isEnabledFor(logging.DEBUG):
                    logger.debug(
                        f"QAST (ID: {id(self)}): Candidato ao torneio: {candidate_id}, Fitness (Gen {self.generation}): {current_fitness:.4f}"
                    )
                if current_fitness > best_fitness_in_tournament:
                    best_fitness_in_tournament = current_fitness
                    winner_repr = candidate_repr
            else:
                logger.warning(
                    f"QAST (ID: {id(self)}): Fitness para candidato {candidate_id} não encontrado ou de geração incorreta (Esperado: {self.generation}, Encontrado: {candidate_fitness_data.get('generation_evaluated') if candidate_fitness_data else 'N/A'}). Ignorando no torneio."
                )

        if winner_repr:
            winner_id = (
                getattr(winner_repr, "id", "N/A")
                if not isinstance(winner_repr, dict)
                else winner_repr.get("id", "N/A")
            )
            logger.info(
                f"QAST (ID: {id(self)}): Estratégia {winner_id} venceu o torneio com fitness {best_fitness_in_tournament:.4f}."
            )
        else:
            # Fallback: se nenhum vencedor claro (ex: todos com fitness -inf ou problemas de dados), escolher aleatoriamente dos candidatos.
            # Isso pode acontecer se a avaliação falhou para todos os candidatos do torneio.
            if tournament_candidates:
                winner_repr = random.choice(tournament_candidates)
                winner_id_fallback = (
                    getattr(winner_repr, "id", "N/A_Fallback")
                    if not isinstance(winner_repr, dict)
                    else winner_repr.get("id", "N/A_Fallback")
                )
                logger.warning(
                    f"QAST (ID: {id(self)}): Nenhum vencedor claro no torneio (todos os fitness podem ser inválidos). Selecionado {winner_id_fallback} aleatoriamente como fallback."
                )
            else:  # Não deveria acontecer se actual_tournament_size > 0
                logger.error(
                    f"QAST (ID: {id(self)}): Pool de candidatos ao torneio ficou vazio inesperadamente. Não foi possível selecionar vencedor."
                )

        return winner_repr  # Retorna a representação

    def _perform_crossover(
        self, parent1_repr: Any, parent2_repr: Any, generation: int
    ) -> Any:
        """Delegate to implementation in :mod:`qast_evolution.genetic_ops`."""
        return _perform_crossover_impl(self, parent1_repr, parent2_repr, generation)
