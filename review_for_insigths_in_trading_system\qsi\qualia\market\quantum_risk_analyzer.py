"""
QUALIA Quantum Risk Analyzer

Este módulo implementa análise de risco de mercado utilizando modelagem probabilística quântica.
Ao contrário de métodos tradicionais de análise de risco, esta abordagem aproveita as propriedades
quânticas como superposição e emaranhamento para modelar incertezas financeiras de forma mais robusta.

Principais funcionalidades:
- Cálculo de Value-at-Risk (VaR) quântico
- Modelagem de cenários extremos usando amplificação quântica
- Análise de correlações não-lineares entre ativos
- Simulação de Monte Carlo Quântico para estresse de portfólio
"""

import numpy as np
import pandas as pd

try:  # pragma: no cover - optional dependency
    import cupy as cp  # type: ignore

    _CUPY_AVAILABLE = True
except Exception:
    cp = None  # type: ignore
    _CUPY_AVAILABLE = False
from datetime import datetime, timezone
import matplotlib.pyplot as plt
from typing import Any, Dict, List, Optional
from ..config.quantum_risk_defaults import load_quantum_risk_defaults
from ..utils.logger import get_logger
import time

# Configuração de logging
logger = get_logger(__name__)

# Configurações padrões carregadas via YAML
_DEFAULTS = load_quantum_risk_defaults()

# Constantes
CONFIDENCE_LEVELS = _DEFAULTS.get("confidence_levels", [0.90, 0.95, 0.99])
MAX_QUBITS = int(_DEFAULTS.get("max_qubits", 8))
DEFAULT_MONTE_CARLO_SAMPLES = int(_DEFAULTS.get("monte_carlo_samples", 5000))
DEFAULT_NOISE_LEVEL = float(_DEFAULTS.get("noise_level", 0.1))
DEFAULT_USE_ACCEL = bool(_DEFAULTS.get("use_hardware_acceleration", False))


class QuantumRiskAnalyzer:
    """
    Analisador de risco quântico que incorpora princípios da física quântica
    para modelar incertezas e riscos em mercados financeiros.
    """

    def __init__(
        self,
        quantum_universe=None,
        n_qubits: int = 6,
        confidence_levels: Optional[List[float]] = None,
        monte_carlo_samples: Optional[int] = None,
        use_quantum_simulation: bool = True,
        noise_level: Optional[float] = None,
        use_hardware_acceleration: bool = DEFAULT_USE_ACCEL,
    ):
        """
        Inicializa o analizador de risco quântico.

        Args:
            quantum_universe: Instância do universo quântico QUALIA (opcional)
            n_qubits: Número de qubits a utilizar na representação
            confidence_levels: Níveis de confiança para cálculos de VaR/CVaR
            monte_carlo_samples: Número de amostras para simulações Monte Carlo
            use_quantum_simulation: Se True, usa simulação quântica para cálculos de risco
            noise_level: Fator de ruído padrão aplicado nas simulações
        """
        self.quantum_universe = quantum_universe
        # Limitar número máximo de qubits
        self.n_qubits = min(n_qubits, MAX_QUBITS)
        self.confidence_levels = confidence_levels or CONFIDENCE_LEVELS
        self.monte_carlo_samples = (
            monte_carlo_samples
            if monte_carlo_samples is not None
            else DEFAULT_MONTE_CARLO_SAMPLES
        )
        self.noise_level = (
            noise_level if noise_level is not None else DEFAULT_NOISE_LEVEL
        )
        self.use_quantum_simulation = use_quantum_simulation
        self.use_hardware_acceleration = use_hardware_acceleration

        # Estado interno
        self.historical_data = {}
        self.current_prices = {}
        self.weights = {}
        self.portfolio_value = 0.0
        self.volatilities = {}
        self.correlations = np.array([])
        self.density_matrix = None
        self.risk_metrics = {}

        logger.info(f"Analisador de risco quântico inicializado com {n_qubits} qubits")

    def set_portfolio(
        self, positions: List[Dict[str, Any]], current_prices: Dict[str, float]
    ) -> None:
        """
        Define o portfólio atual para análise de risco.

        Args:
            positions: Lista de posições abertas com detalhes como símbolo, quantidade, etc.
            current_prices: Dicionário com preços atuais por símbolo
        """
        # Armazenar dados do portfólio
        self.current_prices = current_prices

        # Calcular valor e pesos do portfólio
        total_value = 0.0
        weights = {}

        for position in positions:
            symbol = position["symbol"]
            quantity = position["quantity"]

            if symbol in current_prices:
                price = current_prices[symbol]
                position_value = price * quantity
                total_value += position_value

                # Armazenar valor da posição para cálculo posterior dos pesos
                if symbol not in weights:
                    weights[symbol] = 0
                weights[symbol] += position_value

        # Calcular pesos normalizados
        self.weights = {
            symbol: value / total_value if total_value > 0 else 0
            for symbol, value in weights.items()
        }
        self.portfolio_value = total_value

        logger.info(
            f"Portfólio definido: {len(positions)} posições, valor total: ${total_value:.2f}"
        )

    def update_historical_data(
        self, market_data: Dict[str, Dict[str, Dict[str, List[float]]]]
    ) -> None:
        """
        Atualiza dados históricos para cálculos de risco.

        Args:
            market_data: Dados de mercado com estrutura {símbolo: {timeframe: {campo: valores}}}
        """
        processed_data = {}

        for symbol, timeframes in market_data.items():
            if symbol not in processed_data:
                processed_data[symbol] = {}

            # Usar timeframe de '1d' se disponível, senão usar o primeiro
            # disponível
            timeframe = (
                "1d"
                if "1d" in timeframes
                else list(timeframes.keys())[0] if timeframes else None
            )

            if timeframe:
                # Extrair preços de fechamento
                if "close" in timeframes[timeframe]:
                    close_prices = timeframes[timeframe]["close"]
                    timestamps = timeframes[timeframe].get("timestamps", [])

                    # Criar DataFrame com timestamps se disponíveis
                    if timestamps and len(timestamps) == len(close_prices):
                        df = pd.DataFrame(
                            {"timestamp": timestamps, "close": close_prices}
                        )
                    else:
                        df = pd.DataFrame({"close": close_prices})

                    # Calcular retornos
                    if len(df) > 1:
                        df["return"] = df["close"].pct_change().fillna(0)

                        # Armazenar apenas os dados relevantes
                        processed_data[symbol] = {
                            "close": df["close"].tolist(),
                            "return": df["return"].tolist(),
                        }

        self.historical_data = processed_data

        # Calcular volatilidades e correlações
        self._calculate_volatilities()
        self._calculate_correlations()

        logger.info(f"Dados históricos atualizados para {len(processed_data)} símbolos")

    def _calculate_volatilities(self) -> None:
        """
        Calcula volatilidades para cada ativo baseado nos dados históricos.
        """
        volatilities = {}

        for symbol, data in self.historical_data.items():
            if "return" in data and len(data["return"]) > 1:
                # Calcular volatilidade como desvio padrão dos retornos
                volatility = np.std(data["return"]) * np.sqrt(252)  # Anualizada
                volatilities[symbol] = volatility

        self.volatilities = volatilities

    def _calculate_correlations(self) -> None:
        """
        Calcula matriz de correlações entre os ativos do portfólio.
        """
        portfolio_symbols = list(self.weights.keys())
        num_assets = len(portfolio_symbols)

        if num_assets <= 1:
            self.correlations = np.ones((1, 1))
            return

        # Preparar retornos para cálculo de correlações
        returns_data = {}
        max_length = 0

        for symbol in portfolio_symbols:
            if (
                symbol in self.historical_data
                and "return" in self.historical_data[symbol]
            ):
                returns = self.historical_data[symbol]["return"]
                returns_data[symbol] = returns
                max_length = max(max_length, len(returns))

        # Criar matriz de retornos alinhados
        aligned_returns = np.zeros((max_length, num_assets))

        for i, symbol in enumerate(portfolio_symbols):
            if symbol in returns_data:
                returns = returns_data[symbol]
                aligned_returns[-len(returns) :, i] = returns

        # Calcular matriz de correlações
        correlation_matrix = np.corrcoef(aligned_returns.T)

        # Ajustar NaNs para 0 (sem correlação)
        correlation_matrix = np.nan_to_num(correlation_matrix)

        self.correlations = correlation_matrix

    def _encode_portfolio_quantum_state(self) -> np.ndarray:
        """
        Codifica o portfólio atual como um estado quântico de densidade.

        Returns:
            Matriz de densidade representando o estado quântico do portfólio
        """
        portfolio_symbols = list(self.weights.keys())
        n_assets = len(portfolio_symbols)

        if n_assets == 0:
            # Retornar estado vazio de dimensão 2^n_qubits
            dim = 2**self.n_qubits
            return np.eye(dim) / dim

        # Limitar o número de ativos ao número de qubits disponíveis
        n_assets = min(n_assets, self.n_qubits)
        portfolio_symbols = portfolio_symbols[:n_assets]

        # Normalizar pesos para uso nos cálculos quânticos
        total_weight = sum(self.weights[s] for s in portfolio_symbols)
        normalized_weights = [
            self.weights[s] / total_weight if total_weight > 0 else 1.0 / n_assets
            for s in portfolio_symbols
        ]

        # Preparar vetor de estado (amplitudes)
        state_dimension = 2**n_assets
        amplitudes = np.zeros(state_dimension, dtype=complex)

        # Codificar pesos do portfólio nas amplitudes
        for i in range(n_assets):
            weight = normalized_weights[i]

            # Usar raiz quadrada do peso como amplitude base
            amplitude = np.sqrt(weight)

            # Codificar em estados específicos
            state_idx = 2**i
            amplitudes[state_idx] = amplitude

        # Normalizar o vetor de estado
        norm = np.linalg.norm(amplitudes)
        if norm > 0:
            amplitudes = amplitudes / norm

        # Codificar volatilidades como fases
        for i in range(n_assets):
            symbol = portfolio_symbols[i]
            if symbol in self.volatilities:
                volatility = self.volatilities[symbol]

                # Mapear volatilidade para uma fase entre 0 e 2π
                phase = np.clip(volatility, 0, 1) * 2 * np.pi

                # Aplicar fase ao estado correspondente
                state_idx = 2**i
                amplitudes[state_idx] *= np.exp(1j * phase)

        # Criar matriz de densidade a partir do vetor de estado
        density_matrix = np.outer(amplitudes, np.conj(amplitudes))

        # Expandir para dimensão completa se necessário
        if n_assets < self.n_qubits:
            expanded_dim = 2**self.n_qubits
            expanded_matrix = np.zeros((expanded_dim, expanded_dim), dtype=complex)
            expanded_matrix[:state_dimension, :state_dimension] = density_matrix
            density_matrix = expanded_matrix

        # Verificar se matriz é Hermitiana e com traço 1
        density_matrix = (density_matrix + density_matrix.conj().T) / 2
        trace = np.trace(density_matrix)
        if abs(trace) > 0:
            density_matrix = density_matrix / trace

        self.density_matrix = density_matrix
        return density_matrix

    def _apply_quantum_noise(
        self, density_matrix: np.ndarray, noise_level: Optional[float] = None
    ) -> np.ndarray:
        """
        Aplica ruído quântico ao estado para simular incertezas de mercado.

        Args:
            density_matrix: Matriz de densidade do estado quântico
            noise_level: Nível de ruído a aplicar (0 a 1). Quando ``None`` usa
                o valor padrão configurado.

        Returns:
            Matriz de densidade com ruído
        """
        level = noise_level if noise_level is not None else self.noise_level
        dim = density_matrix.shape[0]

        # Criar matriz de ruído aleatória
        noise = np.random.normal(0, level, (dim, dim))
        noise = noise + noise.conj().T  # Tornar Hermitiana

        # Aplicar ruído
        noise_trace = np.trace(noise)
        if abs(noise_trace) == 0:
            noise_trace = 1e-12
        noisy_matrix = (1 - level) * density_matrix + level * (noise / noise_trace)

        # Garantir que ainda é matriz de densidade válida
        noisy_matrix = (noisy_matrix + noisy_matrix.conj().T) / 2
        trace = np.trace(noisy_matrix)
        if abs(trace) > 0:
            noisy_matrix = noisy_matrix / trace

        return noisy_matrix

    def calculate_quantum_var(
        self, timeframe_days: int = 1, confidence_levels: List[float] = None
    ) -> Dict[str, Any]:
        """
        Calcula Value-at-Risk (VaR) usando modelagem quântica.

        Args:
            timeframe_days: Horizonte temporal para cálculo do VaR (em dias)
            confidence_levels: Níveis de confiança para calcular (se None, usa os níveis padrão)

        Returns:
            Dicionário com resultados de VaR e métricas relacionadas
        """
        if not confidence_levels:
            confidence_levels = self.confidence_levels

        if not self.weights or self.portfolio_value == 0:
            return {"error": "Portfólio vazio ou não inicializado corretamente"}

        # Calcular (ou usar) matriz de densidade quântica para o portfólio
        if self.density_matrix is None:
            self._encode_portfolio_quantum_state()

        density_matrix = self.density_matrix

        # Simular possíveis retornos usando matriz de densidade quântica
        returns = []

        for _ in range(self.monte_carlo_samples):
            # Aplicar ruído quântico representando flutuações de mercado
            noisy_matrix = self._apply_quantum_noise(density_matrix)

            # Extrair probabilidades dos estados da matriz de densidade
            probabilities = np.diag(noisy_matrix).real

            # Normalizar probabilidades
            sum_prob = np.sum(probabilities)
            if sum_prob > 0:
                probabilities = probabilities / sum_prob

            # Calcular retorno simulado com base nas probabilidades quânticas
            portfolio_return = 0.0

            symbols = list(self.weights.keys())
            for i, symbol in enumerate(symbols[: min(len(symbols), self.n_qubits)]):
                if symbol in self.volatilities:
                    # Usar volatilidade para simular movimento
                    volatility = self.volatilities[symbol]
                    weight = self.weights[symbol]

                    # Estado base para este ativo
                    base_state = 2**i

                    # Probabilidade deste estado
                    if base_state < len(probabilities):
                        p = probabilities[base_state]
                    else:
                        p = 0

                    # Simular retorno para este ativo usando distribuição normal
                    # com volatilidade escalada pelo horizonte temporal
                    scaled_vol = volatility * np.sqrt(timeframe_days / 252)
                    asset_return = np.random.normal(0, scaled_vol)

                    # Contribuição ao retorno do portfolio (ponderada pela
                    # probabilidade quântica)
                    portfolio_return += (
                        weight * asset_return * p * 2
                    )  # Fator 2 para balancear probabilidades

            returns.append(portfolio_return)

        # Calcular VaR e CVaR para cada nível de confiança
        var_results = {}
        cvar_results = {}

        returns = np.array(returns)
        potential_losses = -returns * self.portfolio_value

        for conf_level in confidence_levels:
            percentile = 100 * (1 - conf_level)
            var = np.percentile(potential_losses, percentile)

            # Calcular CVaR (Expected Shortfall)
            beyond_var = potential_losses[potential_losses >= var]
            cvar = beyond_var.mean() if len(beyond_var) > 0 else var

            var_results[str(conf_level)] = var
            cvar_results[str(conf_level)] = cvar

        # Resultados consolidados
        results = {
            "var": var_results,
            "cvar": cvar_results,
            "portfolio_value": self.portfolio_value,
            "timeframe_days": timeframe_days,
            "monte_carlo_samples": self.monte_carlo_samples,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "mean_return": returns.mean(),
            "volatility": returns.std(),
            "skewness": 0 if len(returns) <= 1 else self._calculate_skewness(returns),
            "kurtosis": 0 if len(returns) <= 1 else self._calculate_kurtosis(returns),
            "max_loss": (
                -returns.min() * self.portfolio_value if len(returns) > 0 else 0
            ),
            "max_gain": returns.max() * self.portfolio_value if len(returns) > 0 else 0,
        }

        # Armazenar resultados
        self.risk_metrics = results

        return results

    def perform_quantum_stress_test(
        self, stress_factors: Dict[str, float] = None
    ) -> Dict[str, Any]:
        """
        Realiza teste de estresse quântico no portfólio.

        Args:
            stress_factors: Fatores de estresse a aplicar por categoria
                            (ex: {'price': -0.10, 'volatility': 2.0, 'correlation': 0.8})

        Returns:
            Resultados do teste de estresse
        """
        if not stress_factors:
            # Fatores de estresse padrão
            stress_factors = {
                "price": -0.15,  # Queda de 15% nos preços
                "volatility": 2.0,  # Dobro da volatilidade
                "correlation": 0.8,  # Aumento de correlações em 80%
            }

        if not self.weights or self.portfolio_value == 0:
            return {"error": "Portfólio vazio ou não inicializado corretamente"}

        # Salvar estado atual para restaurar depois
        original_prices = self.current_prices.copy()
        original_volatilities = self.volatilities.copy()
        original_correlations = (
            self.correlations.copy() if self.correlations.size > 0 else None
        )

        # Aplicar fatores de estresse
        # 1. Ajustar preços
        if "price" in stress_factors:
            price_factor = 1.0 + stress_factors["price"]
            self.current_prices = {
                s: p * price_factor for s, p in original_prices.items()
            }

        # 2. Ajustar volatilidades
        if "volatility" in stress_factors:
            vol_factor = stress_factors["volatility"]
            self.volatilities = {
                s: v * vol_factor for s, v in original_volatilities.items()
            }

        # 3. Ajustar correlações
        if "correlation" in stress_factors and original_correlations is not None:
            corr_factor = stress_factors["correlation"]

            # Ajustar matriz de correlações (aumentando todas as correlações)
            n = original_correlations.shape[0]
            for i in range(n):
                for j in range(n):
                    if i != j:  # Ignorar diagonal (autocorrelação)
                        # Empurrar correlações mais para 1 ou -1 dependendo do
                        # sinal
                        sign = 1 if original_correlations[i, j] >= 0 else -1
                        self.correlations[i, j] = sign * min(
                            abs(original_correlations[i, j]) + corr_factor, 0.99
                        )

        # Calcular VaR e outras métricas sob condições de estresse
        stress_var = self.calculate_quantum_var(timeframe_days=1)

        # Restaurar estado original
        self.current_prices = original_prices
        self.volatilities = original_volatilities

        if original_correlations is not None:
            self.correlations = original_correlations

        # Recalcular VaR normal para comparação
        normal_var = self.calculate_quantum_var(timeframe_days=1)

        # Calcular impacto do estresse
        stress_impact = {}

        for conf_level in self.confidence_levels:
            level_str = str(conf_level)
            if level_str in stress_var["var"] and level_str in normal_var["var"]:
                impact_pct = (
                    stress_var["var"][level_str] / normal_var["var"][level_str] - 1
                ) * 100
                stress_impact[level_str] = impact_pct

        # Resultados consolidados
        results = {
            "stress_var": stress_var["var"],
            "normal_var": normal_var["var"],
            "stress_cvar": stress_var["cvar"],
            "normal_cvar": normal_var["cvar"],
            "impact_pct": stress_impact,
            "stress_factors": stress_factors,
            "portfolio_value": self.portfolio_value,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "max_stress_loss": stress_var["max_loss"],
            "max_normal_loss": normal_var["max_loss"],
        }

        return results

    def _calculate_skewness(self, returns: np.ndarray) -> float:
        """
        Calcula a assimetria (skewness) da distribuição de retornos.

        Args:
            returns: Array de retornos

        Returns:
            Valor de skewness
        """
        n = len(returns)
        if n <= 1:
            return 0.0

        mean = np.mean(returns)
        std = np.std(returns)

        if std == 0:
            return 0.0

        skewness = np.sum(((returns - mean) / std) ** 3) / n
        return skewness

    def _calculate_kurtosis(self, returns: np.ndarray) -> float:
        """
        Calcula a curtose (kurtosis) da distribuição de retornos.

        Args:
            returns: Array de retornos

        Returns:
            Valor de kurtosis
        """
        n = len(returns)
        if n <= 1:
            return 0.0

        mean = np.mean(returns)
        std = np.std(returns)

        if std == 0:
            return 0.0

        kurtosis = (
            np.sum(((returns - mean) / std) ** 4) / n - 3
        )  # -3 para kurtosis em excesso
        return kurtosis

    def calculate_quantum_entropy(self) -> float:
        """
        Calcula a entropia quântica (von Neumann) da matriz de densidade do portfólio.

        Returns:
            Valor de entropia quântica
        """
        if self.density_matrix is None:
            self._encode_portfolio_quantum_state()

        if self.density_matrix is None:
            return 0.0

        use_gpu = self.use_hardware_acceleration and _CUPY_AVAILABLE
        xp = cp if use_gpu else np

        eigenvalues = xp.linalg.eigvalsh(self.density_matrix)
        if use_gpu:
            eigenvalues = cp.asnumpy(eigenvalues)

        # Filtrar autovalores muito pequenos
        eigenvalues = eigenvalues[eigenvalues > 1e-10]

        # Calcular entropia de von Neumann: -Tr(ρ log ρ) = -∑ λ_i log λ_i
        entropy = -np.sum(eigenvalues * np.log2(eigenvalues))
        return float(entropy)

    def calculate_risk_metrics(self) -> Dict[str, Any]:
        """
        Calcula métricas completas de risco para o portfólio atual, utilizando
        análise quântica avançada para modelar distribuições de risco mais precisas.

        Returns:
            Dicionário com todas as métricas de risco
        """
        # Verificar se há portfólio definido
        if not self.weights or self.portfolio_value == 0:
            return {
                "error": "Portfólio vazio ou não inicializado corretamente",
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

        start_time = time.time()
        logger.info("Iniciando cálculo de métricas quânticas de risco")

        # VaR quântico de 1 dia
        var_1d = self.calculate_quantum_var(timeframe_days=1)

        # VaR quântico de 10 dias
        var_10d = self.calculate_quantum_var(timeframe_days=10)

        # Teste de estresse quântico (cenários extremos)
        stress_test = self.perform_quantum_stress_test()

        # Entropia quântica (medida de diversificação/imprevisibilidade)
        quantum_entropy = self.calculate_quantum_entropy()

        # Se temos universo quântico disponível, calcular métricas adicionais
        additional_metrics = {}
        if self.quantum_universe:
            try:
                # Preparar estado quântico para análise de mercado
                if hasattr(self.quantum_universe, "set_thermal_parameter"):
                    # Usar volatilidade média como parâmetro térmico
                    avg_volatility = (
                        np.mean(list(self.volatilities.values()))
                        if self.volatilities
                        else 0.2
                    )
                    thermal_param = min(0.5, max(0.05, avg_volatility))
                    self.quantum_universe.set_thermal_parameter(thermal_param)

                # Configurar modo de simulação para análise de risco
                if hasattr(self.quantum_universe, "set_retro_mode"):
                    self.quantum_universe.set_retro_mode("risk_sensitive")

                # Executar simulação quântica especializada para análise de
                # risco
                risk_results = self.quantum_universe.run(shots=2048, risk_analysis=True)

                if risk_results and isinstance(risk_results, dict):
                    # Extrair métricas quânticas especializadas para risco
                    if "metrics" in risk_results:
                        metrics_obj = risk_results["metrics"]

                        if isinstance(metrics_obj, dict):
                            # Índice de Instabilidade Quântica (0-1)
                            if (
                                "quantum_entropy" in metrics_obj
                                and "coherence" in metrics_obj
                            ):
                                entropy_vals = metrics_obj.get("quantum_entropy", [0.5])
                                coherence_vals = metrics_obj.get("coherence", [0.5])

                                entropy = float(np.mean(entropy_vals))
                                coherence = float(np.mean(coherence_vals))

                                # Alta entropia + baixa coerência = alto risco
                                # quântico
                                additional_metrics["quantum_instability"] = float(
                                    entropy * (1 - coherence)
                                )

                            # Fator de não-gaussianidade (quanto maior, mais
                            # cauda pesada na distribuição)
                            if "page_entropy" in metrics_obj:
                                page_entropy_vals = metrics_obj.get("page_entropy", [0])
                                page_entropy = float(np.mean(page_entropy_vals))

                                # Razão entre Page entropy e entropia de
                                # Shannon
                                if "shannon_entropy" in metrics_obj:
                                    shannon_vals = metrics_obj.get(
                                        "shannon_entropy", [1]
                                    )
                                    shannon = float(np.mean(shannon_vals))
                                    if shannon > 0:
                                        additional_metrics["non_gaussianity"] = float(
                                            page_entropy / shannon
                                        )

                            # Conectividade caótica do mercado (medida de risco
                            # sistêmico)
                            if "otoc" in metrics_obj:
                                otoc_vals = metrics_obj.get("otoc", [0])
                                additional_metrics["market_chaos"] = float(
                                    np.mean(otoc_vals)
                                )

                    # Extrair informações de circuit counts para análise de
                    # probabilidade
                    if "counts" in risk_results:
                        counts = risk_results["counts"]
                        if counts and isinstance(counts, dict):
                            total_shots = sum(counts.values())

                            # Calcular probabilidade de eventos extremos
                            # (estados de alta energia)
                            if total_shots > 0:
                                # Identificar estados que representam
                                # movimentos extremos
                                extreme_counts = 0
                                for state, count in counts.items():
                                    # Estados com muitos 1s representam eventos
                                    # extremos
                                    ones_count = state.count("1")
                                    if (
                                        ones_count >= len(state) // 2 + 2
                                    ):  # Mais de 50% + 2 bits
                                        extreme_counts += count

                                # Probabilidade de eventos extremos
                                extreme_prob = extreme_counts / total_shots
                                additional_metrics["extreme_event_probability"] = float(
                                    extreme_prob
                                )

                logger.info("Análise quântica de risco concluída com sucesso")

            except Exception as e:
                logger.error(f"Erro ao calcular métricas quânticas adicionais: {e}")

        # Combinar dados de volatilidade com pesos para score de risco
        weighted_volatility = 0
        for symbol, weight in self.weights.items():
            if symbol in self.volatilities:
                weighted_volatility += weight * self.volatilities[symbol]

        # Calcular índice de diversificação de portfólio (0-1)
        diversification_score = 0
        if len(self.weights) > 1:
            # Usar entropia de Shannon normalizada dos pesos como medida de
            # diversificação
            weights_array = np.array(list(self.weights.values()))
            shannon_entropy = -np.sum(weights_array * np.log2(weights_array + 1e-10))
            max_entropy = np.log2(len(weights_array))
            if max_entropy > 0:
                diversification_score = shannon_entropy / max_entropy

        # Consolidar resultados com métricas básicas e avançadas
        results = {
            # Métricas de risco tradicionais
            "var_1d": var_1d,
            "var_10d": var_10d,
            "stress_test": stress_test,
            "quantum_entropy": quantum_entropy,
            "portfolio_value": self.portfolio_value,
            "positions_count": len(self.weights),
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "max_var_amount": var_1d.get("var", {}).get("0.99", 0),
            "max_cvar_amount": var_1d.get("cvar", {}).get("0.99", 0),
            "stress_impact_pct": stress_test.get("impact_pct", {}).get("0.99", 0),
            "volatilities": self.volatilities,
            "weighted_volatility": weighted_volatility,
            "diversification_score": diversification_score,
            # Adicionar métricas quânticas avançadas
            "quantum_metrics": additional_metrics,
        }

        # Criar um score de risco global (0-100)
        # Combina VaR, diversificação, instabilidade quântica, e outros fatores
        try:
            # Componentes do score
            var_component = (
                min(1.0, results["max_var_amount"] / results["portfolio_value"]) * 40
            )

            # Se temos métricas quânticas, usá-las para ajustar o score
            quantum_component = 0
            if "quantum_metrics" in results and results["quantum_metrics"]:
                # Componente de instabilidade quântica
                q_instability = (
                    results["quantum_metrics"].get("quantum_instability", 0.5) * 20
                )

                # Componente de caos de mercado
                market_chaos = results["quantum_metrics"].get("market_chaos", 0.3) * 15

                # Probabilidade de eventos extremos
                extreme_prob = (
                    results["quantum_metrics"].get("extreme_event_probability", 0.1)
                    * 25
                )

                quantum_component = q_instability + market_chaos + extreme_prob
            else:
                # Fallback para quando não temos métricas quânticas
                quantum_component = 30  # Valor neutro de risco

            # Ajustar com base na diversificação (maior diversificação = menor
            # risco)
            diversification_component = (1 - results["diversification_score"]) * 25

            # Score final (0-100)
            risk_score = min(
                100, var_component + diversification_component + quantum_component
            )
            results["risk_score"] = risk_score

            # Categorizar o nível de risco
            if risk_score < 30:
                risk_level = "baixo"
            elif risk_score < 60:
                risk_level = "moderado"
            elif risk_score < 80:
                risk_level = "elevado"
            else:
                risk_level = "extremo"

            results["risk_level"] = risk_level

        except Exception as e:
            logger.error(f"Erro ao calcular score de risco: {e}")
            results["risk_score"] = 50  # Valor neutro em caso de erro
            results["risk_level"] = "indefinido"

        # Registrar tempo de execução
        execution_time = time.time() - start_time
        results["execution_time"] = execution_time

        logger.info(
            f"Cálculo de métricas de risco concluído em {execution_time:.2f}s - "
            + f"Score: {results.get('risk_score', 0):.1f}, Nível: {results.get('risk_level', 'N/A')}"
        )

        return results

    def visualize_risk_distribution(
        self, num_samples: int = 1000, title: str = "Distribuição de Retornos e VaR"
    ) -> plt.Figure:
        """
        Gera visualização da distribuição de risco com VaR indicado.

        Args:
            num_samples: Número de amostras para gerar a distribuição
            title: Título do gráfico

        Returns:
            Figura matplotlib com o gráfico
        """
        if not self.weights or self.portfolio_value == 0:
            fig, ax = plt.subplots(figsize=(10, 6))
            ax.text(
                0.5,
                0.5,
                "Portfólio vazio ou não inicializado",
                horizontalalignment="center",
                verticalalignment="center",
            )
            return fig

        # Calcular (ou usar) matriz de densidade quântica para o portfólio
        if self.density_matrix is None:
            self._encode_portfolio_quantum_state()

        density_matrix = self.density_matrix

        # Simular retornos para visualização
        returns = []

        for _ in range(num_samples):
            # Aplicar ruído quântico representando flutuações de mercado
            noisy_matrix = self._apply_quantum_noise(density_matrix)

            # Extrair probabilidades dos estados
            probabilities = np.diag(noisy_matrix).real

            # Normalizar probabilidades
            sum_prob = np.sum(probabilities)
            if sum_prob > 0:
                probabilities = probabilities / sum_prob

            # Calcular retorno simulado
            portfolio_return = 0.0

            symbols = list(self.weights.keys())
            for i, symbol in enumerate(symbols[: min(len(symbols), self.n_qubits)]):
                if symbol in self.volatilities:
                    volatility = self.volatilities[symbol]
                    weight = self.weights[symbol]

                    # Estado base para este ativo
                    base_state = 2**i

                    # Probabilidade deste estado
                    if base_state < len(probabilities):
                        p = probabilities[base_state]
                    else:
                        p = 0

                    # Simular retorno para este ativo
                    scaled_vol = volatility * np.sqrt(1 / 252)  # diário
                    asset_return = np.random.normal(0, scaled_vol)

                    portfolio_return += weight * asset_return * p * 2

            returns.append(portfolio_return)

        # Converter para array NumPy para cálculos
        returns_array = np.array(returns)

        # Calcular VaR para diferentes níveis de confiança
        var_90 = np.percentile(-returns_array * self.portfolio_value, 10)
        var_95 = np.percentile(-returns_array * self.portfolio_value, 5)
        var_99 = np.percentile(-returns_array * self.portfolio_value, 1)

        # Criar figura
        fig, ax = plt.subplots(figsize=(12, 8))

        # Histograma de retornos
        monetary_impact = -returns_array * self.portfolio_value
        n, bins, patches = ax.hist(
            monetary_impact,
            bins=50,
            alpha=0.7,
            color="skyblue",
            density=True,
            label="Distribuição de P&L",
        )

        # Adicionar linhas verticais para VaR
        ax.axvline(
            var_90,
            color="yellow",
            linestyle="dashed",
            linewidth=2,
            label=f"VaR 90%: ${var_90:.2f}",
        )
        ax.axvline(
            var_95,
            color="orange",
            linestyle="dashed",
            linewidth=2,
            label=f"VaR 95%: ${var_95:.2f}",
        )
        ax.axvline(
            var_99,
            color="red",
            linestyle="dashed",
            linewidth=2,
            label=f"VaR 99%: ${var_99:.2f}",
        )

        # Adicionar linha zero
        ax.axvline(0, color="green", linestyle="-", linewidth=1)

        # Adicionar texto para estatísticas principais
        textstr = "\n".join(
            (
                f"Portfólio: ${self.portfolio_value:.2f}",
                f"Média: ${np.mean(monetary_impact):.2f}",
                f"Desvio: ${np.std(monetary_impact):.2f}",
                f"Mín: ${np.min(monetary_impact):.2f}",
                f"Máx: ${np.max(monetary_impact):.2f}",
                f"Skewness: {self._calculate_skewness(returns_array):.3f}",
                f"Kurtosis: {self._calculate_kurtosis(returns_array):.3f}",
            )
        )
        props = dict(boxstyle="round", facecolor="white", alpha=0.5)
        ax.text(
            0.05,
            0.95,
            textstr,
            transform=ax.transAxes,
            fontsize=10,
            verticalalignment="top",
            bbox=props,
        )

        # Configurações do gráfico
        ax.set_title(title, fontsize=14)
        ax.set_xlabel("Impacto Monetário (USD)", fontsize=12)
        ax.set_ylabel("Densidade de Probabilidade", fontsize=12)
        ax.legend(loc="upper right")
        ax.grid(True, linestyle="--", alpha=0.7)

        # Adicionar informações quânticas
        quantum_info = f"Entropia Quântica: {self.calculate_quantum_entropy():.4f}"
        ax.text(
            0.05,
            0.05,
            quantum_info,
            transform=ax.transAxes,
            fontsize=10,
            verticalalignment="bottom",
            bbox=props,
        )

        fig.tight_layout()
        return fig
