"""Signal arbitration utilities for QUALIA.

This module aggregates trading signals coming from multiple components
(such as the Quantum Metacognition Trading layer, strategies and
external filters) and outputs a final decision based on majority vote
and aggregated confidence.
"""

from dataclasses import dataclass
from collections import Counter
from typing import Dict, Iterable, List, Optional, Tuple, Union

from ..config import load_signal_arbiter_defaults
from ..metacognition.metacognition_trading import TradeSignal


_SIGNAL_GROUPS = {
    "BUY": "BUY",
    "SELL": "SELL",
    "REDUCE_EXPOSURE": "SELL",
    "HOLD": "HOLD",
    "NO_SIGNAL": "HOLD",
}

_DEFAULTS: Dict[str, float] = load_signal_arbiter_defaults()
_DEFAULT_THRESHOLD = float(_DEFAULTS.get("threshold", 0.4))


@dataclass
class SignalArbiter:
    """Combine signals from different modules.

    Parameters
    ----------
    threshold : float, optional
        Minimum aggregated confidence required to emit an order. Default is
        ``0.4``.
    """

    threshold: float = _DEFAULT_THRESHOLD

    def _to_trade_signal(
        self, signal: Union[TradeSignal, Tuple[str, float], Dict[str, float], None]
    ) -> Optional[TradeSignal]:
        """Normalize an arbitrary representation into a :class:`TradeSignal`."""
        if signal is None:
            return None
        if isinstance(signal, TradeSignal):
            return signal
        if isinstance(signal, tuple) and len(signal) >= 2:
            return TradeSignal(signal_type=signal[0], confidence=float(signal[1]))
        if isinstance(signal, dict):
            sig_type = signal.get("signal_type") or signal.get("signal")
            conf = float(signal.get("confidence", 0.0))
            if sig_type is not None:
                return TradeSignal(signal_type=sig_type, confidence=conf)
        raise ValueError("Unsupported signal format")

    def _majority_group(self, signals: Iterable[TradeSignal]) -> str:
        """Return the majority signal group among ``signals``."""
        sigs = [s for s in signals if s is not None]
        groups = [_SIGNAL_GROUPS.get(s.signal_type, "HOLD") for s in sigs]
        if not groups:
            return "HOLD"
        return Counter(groups).most_common(1)[0][0]

    def _aggregate_confidence(
        self, signals: Iterable[TradeSignal], group: str
    ) -> float:
        """Aggregate confidence for ``group`` balancing support and opposition."""
        sigs = list(signals)
        if not sigs:
            return 0.0
        support = sum(
            s.confidence
            for s in sigs
            if _SIGNAL_GROUPS.get(s.signal_type, "HOLD") == group
        )
        oppose = sum(
            s.confidence
            for s in sigs
            if _SIGNAL_GROUPS.get(s.signal_type, "HOLD") != group
        )
        raw = (support - oppose) / len(sigs)
        return max(0.0, min(raw, 1.0))

    def arbitrate(
        self,
        qmt_signal: Optional[Union[TradeSignal, Tuple[str, float]]] = None,
        strategy_signal: Optional[Union[TradeSignal, Tuple[str, float]]] = None,
        external_signals: Optional[List[Union[TradeSignal, Tuple[str, float]]]] = None,
    ) -> Optional[TradeSignal]:
        """Return the final signal decided by majority vote.

        Parameters
        ----------
        qmt_signal
            Signal from the Quantum Metacognition Trading layer.
        strategy_signal
            Signal produced by the trading strategy.
        external_signals
            Optional list of signals from additional filters.
        """
        external_signals = external_signals or []
        all_signals: List[TradeSignal] = []
        for s in [qmt_signal, strategy_signal, *external_signals]:
            if s is None:
                continue
            all_signals.append(self._to_trade_signal(s))

        if not all_signals:
            return None

        majority = self._majority_group(all_signals)
        agg_conf = self._aggregate_confidence(all_signals, majority)

        if agg_conf > self.threshold:
            return TradeSignal(signal_type=majority, confidence=agg_conf)
        return None
