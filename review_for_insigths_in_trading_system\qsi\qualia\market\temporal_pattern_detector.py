"""
Detector de Padrões Temporais para o Sistema de Metacognição QUALIA.

Este módulo implementa análise de séries temporais para detecção de padrões
em decisões de trading, utilizando métodos clássicos e quânticos, incluindo:
- Transformada wavelet quântica para identificar padrões em múltiplas escalas temporais
- Transformações de Fourier quânticas para detectar ciclicidade nas decisões
- Algoritmos de detecção de pontos de mudança para identificar alterações nos regimes de mercado
"""

import numpy as np
import pandas as pd
from datetime import datetime, timezone
from typing import List, Dict, Any, Optional
import time
import logging
import os
from ..utils.logger import get_logger
from ..core.encoders import (
    MAX_CIRCUIT_DEPTH,
    MAX_CIRCUIT_OPERATIONS,
)

try:
    from ..utils.quantum_utils import validate_circuit_limits
except Exception:  # pragma: no cover - optional quantum utils

    def validate_circuit_limits(*_args, **_kwargs):
        """Fallback when quantum utils are unavailable."""
        return None


import uuid
from ..config.temporal_detector_config import TemporalPatternConfig
from ..utils.cache import numpy_cache
from datadog import DogStatsd
from ..memory.event_bus import SimpleEventBus
from .event_bus import MARKET_PATTERN_EVENT, MarketPatternDetected
from ..memory.quantum_pattern_memory import QuantumPatternMemory
from ..common_types import QuantumSignaturePacket

# Importações condicionais
try:
    import pywt  # Para wavelet clássica

    HAS_PYWT = True
except ImportError:
    HAS_PYWT = False
    logging.warning(
        "PyWavelets não encontrado. A análise wavelet clássica não estará disponível."
    )

# Verificar disponibilidade de Qiskit para transformadas quânticas
try:
    from qiskit import QuantumCircuit, transpile
    from qiskit_aer import Aer
    from qiskit.circuit.library import QFT

    HAS_QISKIT = True
except ImportError:
    HAS_QISKIT = False
    logging.warning(
        "Qiskit não encontrado. A análise wavelet quântica não estará disponível."
    )


class TemporalPatternDetector:
    """
    Detector de padrões temporais em séries de decisões de trading.
    Suporta análise de wavelets clássica e quântica.
    """

    def __init__(
        self,
        config: Optional[TemporalPatternConfig] = None,
        statsd_client: Optional[DogStatsd] = None,
        trace_id: Optional[str] = None,
        event_bus: Optional[SimpleEventBus] = None,
        qpm: Optional[QuantumPatternMemory] = None,
    ) -> None:
        """Inicializa o detector de padrões temporais.

        Args:
            config: Instância de :class:`TemporalPatternConfig` com os parâmetros
                da detecção. Caso ``None`` seja fornecido, os valores padrão são
                carregados das variáveis de ambiente.
            statsd_client: Cliente opcional do DogStatsd para enviar métricas.
        """
        cfg = config or TemporalPatternConfig()
        self.wavelet_depth = cfg.wavelet_depth
        self.use_quantum_transform = cfg.use_quantum_transform
        self.min_confidence = cfg.min_confidence
        self.quantum_wavelet_shots = cfg.quantum_wavelet_shots
        self.qft_shots = cfg.qft_shots
        self.logger = get_logger(__name__)
        self.statsd = statsd_client or DogStatsd()
        self.trace_id = trace_id or uuid.uuid4().hex
        self.current_trace_id = self.trace_id
        self.event_bus = event_bus
        self.qpm = qpm
        if self.qpm is not None and self.qpm.event_bus is None:
            self.qpm.event_bus = event_bus

        # Validar disponibilidade de dependências
        if self.use_quantum_transform and not HAS_QISKIT:
            self.logger.warning(
                "TraceID: %s - Transformada quântica solicitada, mas Qiskit não está disponível. Usando wavelet clássica como fallback.",
                self.trace_id,
            )
            self.use_quantum_transform = False

        if not HAS_PYWT and not (self.use_quantum_transform and HAS_QISKIT):
            raise ImportError(
                "Nem PyWavelets nem Qiskit estão disponíveis. "
                "Instale pelo menos um deles para detecção de padrões temporais."
            )

    def detect_patterns(
        self,
        decision_history: List[Dict[str, Any]],
        trace_id: Optional[str] = None,
    ) -> List[Dict[str, Any]]:
        """
        Detecta padrões temporais na história de decisões.

        Args:
            decision_history: Lista de decisões com seus resultados
            trace_id: ID opcional para rastreabilidade dos logs

        Returns:
            Lista de insights sobre padrões temporais
        """
        trace_id = trace_id or uuid.uuid4().hex
        self.current_trace_id = trace_id
        if not decision_history:
            self.logger.info("TraceID: %s - Nenhuma decisão fornecida", trace_id)
            return []

        # Converter para DataFrame para facilitar análise
        try:
            start = time.perf_counter()
            df = self._prepare_dataframe(decision_history)
            if (
                df.empty or len(df) < 5
            ):  # Precisamos de pelo menos 5 pontos para análise
                self.logger.info(
                    "TraceID: %s - Histórico insuficiente para análise",
                    trace_id,
                )
                return []

            # Extrair séries temporais relevantes
            timestamps = df.index
            performance = df["pnl_pct"].values

            # Detectar padrões usando transformada wavelet
            if self.use_quantum_transform and HAS_QISKIT:
                patterns = self._quantum_wavelet_analysis(timestamps, performance)
            else:
                patterns = self._classical_wavelet_analysis(timestamps, performance)

            elapsed_ms = (time.perf_counter() - start) * 1000
            tags = [f"trace_id:{trace_id}"]
            self.statsd.gauge("temporal_detector.patterns", len(patterns), tags=tags)
            self.statsd.timing("temporal_detector.execution_ms", elapsed_ms, tags=tags)
            self.logger.info(
                "TraceID: %s - %d padrões detectados em %.2f ms",
                trace_id,
                len(patterns),
                elapsed_ms,
            )

            if self.event_bus:
                payload_vector = performance.tolist()
                for pattern in patterns:
                    self.event_bus.publish(
                        MARKET_PATTERN_EVENT,
                        MarketPatternDetected(
                            vector=payload_vector, metadata=pattern
                        ),
                    )
                    if self.qpm is not None:
                        packet = QuantumSignaturePacket(
                            vector=payload_vector,
                            metrics={},
                            source_details={"detector": pattern.get("type")},
                        )
                        try:
                            self.qpm.store_pattern(
                                packet,
                                {},
                                {},
                                {"pattern_id": pattern.get("id")},
                            )
                            _ = self.qpm.recall_similar_patterns(packet.vector, top_n=3)
                        except Exception:
                            self.logger.exception("Erro ao registrar padrão na QPM")

            # Converter padrões em insights para metacognição
            return self._convert_patterns_to_insights(patterns)

        except ValueError as e:
            self.logger.error(
                "TraceID: %s - ValueError na detecção de padrões: %s",
                trace_id,
                e,
            )
            return []
        except KeyError as e:
            self.logger.error(
                "TraceID: %s - KeyError na detecção de padrões: %s",
                trace_id,
                e,
            )
            return []
        except TypeError as e:
            self.logger.error(
                "TraceID: %s - TypeError na detecção de padrões: %s",
                trace_id,
                e,
            )
            return []
        except Exception:
            self.logger.exception(
                "TraceID: %s - Erro inesperado na detecção de padrões",
                trace_id,
            )
            if os.environ.get("PYTEST_CURRENT_TEST"):
                raise
            return []

    @numpy_cache(maxsize=32)
    def _prepare_dataframe(
        self, decision_history: List[Dict[str, Any]]
    ) -> pd.DataFrame:
        """
        Prepara um DataFrame a partir do histórico de decisões.

        Args:
            decision_history: Lista de decisões com seus resultados

        Returns:
            DataFrame com dados temporais organizados
        """
        # Filtrar apenas decisões que têm resultados
        decisions_with_outcome = [
            d for d in decision_history if "outcome" in d and "performance" in d
        ]

        if not decisions_with_outcome:
            return pd.DataFrame()

        # Extrair dados relevantes
        data = []
        for d in decisions_with_outcome:
            # Garantir que temos timestamp em formato consistente
            try:
                timestamp = pd.to_datetime(d["decision"]["timestamp"])

                # Extrair performance
                pnl = d["performance"].get("pnl", 0)
                pnl_pct = d["performance"].get("pnl_pct", 0)
                duration = d["performance"].get("duration", 0)

                # Extrair outros dados relevantes
                symbol = d["decision"].get("symbol", "unknown")
                action = d["decision"].get("action", "unknown")

                # Métricas quânticas (se disponíveis)
                quantum_entropy = None
                quantum_coherence = None
                if "quantum_state" in d:
                    quantum_entropy = d["quantum_state"].get("entropy", None)
                    quantum_coherence = d["quantum_state"].get("coherence", None)

                data.append(
                    {
                        "timestamp": timestamp,
                        "symbol": symbol,
                        "action": action,
                        "pnl": pnl,
                        "pnl_pct": pnl_pct,
                        "duration": duration,
                        "entropy": quantum_entropy,
                        "coherence": quantum_coherence,
                    }
                )
            except (ValueError, KeyError) as e:
                self.logger.warning(
                    "TraceID: %s - Dados inválidos na decisão: %s",
                    self.current_trace_id,
                    e,
                )
                continue
            except Exception:  # pragma: no cover - erro inesperado
                self.logger.exception(
                    "TraceID: %s - Erro inesperado ao processar decisão",
                    self.current_trace_id,
                )
                if os.environ.get("PYTEST_CURRENT_TEST"):
                    raise
                continue

        if not data:
            return pd.DataFrame()

        df = pd.DataFrame(data)

        # Definir timestamp como índice
        df.set_index("timestamp", inplace=True)
        df.sort_index(inplace=True)

        return df

    @numpy_cache(maxsize=32)
    def _classical_wavelet_analysis(
        self, timestamps: pd.DatetimeIndex, performance: np.ndarray
    ) -> List[Dict[str, Any]]:
        """
        Realiza análise wavelet clássica para detecção de padrões.

        Args:
            timestamps: Índice temporal das decisões
            performance: Array com desempenho (PNL) das decisões

        Returns:
            Lista de padrões detectados
        """
        if not HAS_PYWT:
            return []

        patterns = []

        try:
            # Usar wavelet de Daubechies para decomposição
            wavelet = "db4"

            # Aplicar transformada wavelet discreta
            coeffs = pywt.wavedec(
                performance,
                wavelet,
                level=min(
                    self.wavelet_depth, pywt.dwt_max_level(len(performance), wavelet)
                ),
            )

            # Analisar coeficientes para padrões
            approx_coeffs = coeffs[0]
            detail_coeffs = coeffs[1:]

            # 1. Detectar tendências (usando coeficientes de aproximação)
            trend_pattern = self._analyze_approximation_coeffs(approx_coeffs)
            if trend_pattern:
                patterns.append(trend_pattern)

            # 2. Detectar reversões (usando coeficientes de detalhe)
            for i, detail in enumerate(detail_coeffs):
                reversal_patterns = self._analyze_detail_coeffs(detail, level=i + 1)
                patterns.extend(reversal_patterns)

            # 3. Detectar sazonalidade (análise de Fourier nos coeficientes)
            seasonality_pattern = self._analyze_seasonality(performance)
            if seasonality_pattern:
                patterns.append(seasonality_pattern)

        except Exception as e:
            self.logger.error(
                "TraceID: %s - Erro na análise wavelet clássica: %s",
                self.current_trace_id,
                e,
            )
            import traceback

            self.logger.debug(
                "TraceID: %s - %s", self.current_trace_id, traceback.format_exc()
            )

        return patterns

    @numpy_cache(maxsize=32)
    def _quantum_wavelet_analysis(
        self, timestamps: pd.DatetimeIndex, performance: np.ndarray
    ) -> List[Dict[str, Any]]:
        """
        Realiza análise wavelet quântica avançada para detecção de padrões temporais complexos.

        Implementa:
        1. Transformada wavelet quântica para análise multi-escala
        2. Transformada de Fourier quântica avançada para detecção de ciclicidade
        3. Detecção de pontos de mudança baseada em entropia quântica
        4. Análise de coerência de fase para identificar regimes de mercado

        Args:
            timestamps: Índice temporal das decisões
            performance: Array com desempenho (PNL) das decisões

        Returns:
            Lista de padrões detectados com metadados detalhados
        """
        if not HAS_QISKIT:
            self.logger.warning(
                "TraceID: %s - Qiskit não encontrado. A análise wavelet quântica não estará disponível",
                self.current_trace_id,
            )
            return []

        patterns = []

        try:
            # Verificação de dados mínimos
            if len(performance) < 4:
                return []

            # Normalizar valores para amplitude quântica [0, π/2] com
            # tratamento de outliers
            normalized = self._normalize_for_quantum(performance)

            # Calcular número adequado de qubits baseado no tamanho da amostra
            # Usar mais qubits para séries temporais maiores para capturar mais
            # detalhes
            n_qubits = min(10, max(3, int(np.log2(len(performance)) + 1)))

            while True:
                qc_wavelet = self._build_wavelet_circuit(normalized, n_qubits)
                qc_qft = self._build_qft_circuit(normalized, n_qubits)

                depth = max(qc_wavelet.depth(), qc_qft.depth())
                operations = max(qc_wavelet.size(), qc_qft.size())
                if depth > MAX_CIRCUIT_DEPTH or operations > MAX_CIRCUIT_OPERATIONS:
                    self.logger.warning(
                        "TraceID: %s - Circuito excede limites (depth=%d, ops=%d). Reconstruindo com %d qubits",
                        self.current_trace_id,
                        depth,
                        operations,
                        max(1, n_qubits - 1),
                    )
                    if n_qubits > 1:
                        n_qubits = max(1, n_qubits - 1)
                        continue
                    break

                wavelet_statevector = self._simulate_circuit(
                    qc_wavelet, self.quantum_wavelet_shots
                )
                magnitudes = np.abs(wavelet_statevector) ** 2
                phases = np.angle(wavelet_statevector)

                qft_statevector = self._simulate_circuit(qc_qft, self.qft_shots)
                break

            # Analisar resultados da QFT para extração de espectro de
            # frequência detalhado
            qft_magnitudes = np.abs(qft_statevector) ** 2
            qft_phases = np.angle(qft_statevector)

            patterns.extend(
                self._extract_change_points(phases, timestamps, performance)
            )

            # Padrão de alta frequência (reversão)
            reversal_confidence = float(np.std(phases))
            if reversal_confidence > 0.5:
                patterns.append(
                    {
                        "id": str(uuid.uuid4()),
                        "type": "quantum_reversal",
                        "description": "Padrão quântico de reversão detectado via transformada wavelet",
                        "confidence": float(min(0.95, reversal_confidence)),
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                    }
                )

            # ETAPA 5: DETECÇÃO AVANÇADA DE CICLICIDADE
            # --------------------------------------
            # Analisar espectro QFT para identificar ciclicidade e sazonalidade

            # Cálculo mais sofisticado para identificar frequências dominantes
            # Ignorar frequência zero (componente DC) e frequências muito altas
            start_idx = 1
            end_idx = min(len(qft_magnitudes) // 3, len(qft_magnitudes) - 1)

            if end_idx > start_idx:
                # Extrair o espectro de potência, excluindo componente DC
                spectrum = qft_magnitudes[start_idx:end_idx]

                # Aplicar suavização ao espectro para reduzir ruído
                if len(spectrum) >= 3:
                    smoothed_spectrum = np.convolve(
                        spectrum, np.ones(3) / 3, mode="valid"
                    )

                    # Limiar adaptativo para detecção de picos significativos
                    spectrum_mean = np.mean(smoothed_spectrum)
                    spectrum_std = np.std(smoothed_spectrum)
                    peak_threshold = spectrum_mean + 1.5 * spectrum_std

                    # Encontrar picos no espectro suavizado
                    peak_indices = []
                    for i in range(1, len(smoothed_spectrum) - 1):
                        if (
                            smoothed_spectrum[i] > peak_threshold
                            and smoothed_spectrum[i] > smoothed_spectrum[i - 1]
                            and smoothed_spectrum[i] > smoothed_spectrum[i + 1]
                        ):
                            # Ajustar índice para o espectro original (+1 para
                            # o offset de suavização, +start_idx para o offset
                            # de início)
                            peak_indices.append(i + 1 + start_idx)

                    # Analisar picos se encontrados
                    if peak_indices:
                        # Ordenar picos por potência
                        peak_indices = sorted(
                            peak_indices,
                            key=lambda idx: qft_magnitudes[idx],
                            reverse=True,
                        )

                        # Selecionar os 3 picos mais fortes (ou menos se não
                        # houver 3)
                        top_peaks = peak_indices[: min(3, len(peak_indices))]

                        # Calcular períodos e criar estruturas de dados
                        # enriquecidas
                        dominant_freqs = []
                        estimated_periods = []
                        for peak_idx in top_peaks:
                            # Cálculo refinado do período
                            if peak_idx > 0:  # Evitar divisão por zero
                                # Período = tamanho_amostra / índice_frequência
                                period = len(performance) / peak_idx

                                # Filtrar períodos irrelevantes (muito curtos
                                # ou muito longos)
                                if 2 < period < len(performance) / 2:
                                    estimated_periods.append(float(period))

                                    # Adicionar informações detalhadas sobre o
                                    # pico
                                    dominant_freqs.append(
                                        {
                                            "freq_idx": int(peak_idx),
                                            "magnitude": float(
                                                qft_magnitudes[peak_idx]
                                            ),
                                            "normalized_freq": float(
                                                peak_idx / len(qft_magnitudes)
                                            ),
                                            "period": float(period),
                                            "phase": float(qft_phases[peak_idx]),
                                            "relative_power": float(
                                                qft_magnitudes[peak_idx] / spectrum_mean
                                            ),
                                        }
                                    )

                        # Se encontramos períodos relevantes, relatar padrão de
                        # ciclicidade
                        if dominant_freqs:
                            # Calcular período médio ponderado pela força do
                            # sinal
                            total_magnitude = sum(
                                df["magnitude"] for df in dominant_freqs
                            )
                            weighted_period = (
                                sum(
                                    df["period"] * df["magnitude"]
                                    for df in dominant_freqs
                                )
                                / total_magnitude
                                if total_magnitude > 0
                                else 0
                            )

                            # Calcular confiança baseada na força relativa do
                            # pico dominante
                            peak_dominance = dominant_freqs[0]["relative_power"]
                            cycle_confidence = min(0.95, 0.5 + 0.3 * peak_dominance)

                            # Criar registro de ciclicidade com metadados
                            # enriquecidos
                            patterns.append(
                                {
                                    "id": str(uuid.uuid4()),
                                    "type": "quantum_cyclicality",
                                    "description": f"Ciclos de periodicidade ({len(dominant_freqs)}) detectados via análise espectral quântica",
                                    "confidence": float(cycle_confidence),
                                    "metadata": {
                                        "dominant_frequencies": dominant_freqs,
                                        "estimated_periods": [
                                            float(p) for p in estimated_periods
                                        ],
                                        "weighted_period": float(weighted_period),
                                        "spectrum_stats": {
                                            "mean": float(spectrum_mean),
                                            "std": float(spectrum_std),
                                            "peak_threshold": float(peak_threshold),
                                        },
                                    },
                                    "timestamp": datetime.now(timezone.utc).isoformat(),
                                }
                            )

            # Padrão de baixa frequência (tendência)
            trend_strength = float(
                np.sum(magnitudes[: 2**n_qubits // 2])
                / np.sum(magnitudes[2**n_qubits // 2 :])
            )
            if abs(trend_strength - 1.0) > 0.2:  # Desvio significativo do equilíbrio
                trend_direction = "positiva" if trend_strength > 1.0 else "negativa"
                patterns.append(
                    {
                        "id": str(uuid.uuid4()),
                        "type": "quantum_trend",
                        "description": f"Padrão quântico de tendência {trend_direction} detectado",
                        "confidence": float(min(0.95, abs(trend_strength - 1.0) * 2)),
                        "metadata": {
                            "trend_strength": trend_strength,
                            "low_freq_power": float(
                                np.sum(magnitudes[: 2**n_qubits // 2])
                            ),
                            "high_freq_power": float(
                                np.sum(magnitudes[2**n_qubits // 2 :])
                            ),
                        },
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                    }
                )

        except Exception as e:
            self.logger.error(
                "TraceID: %s - Erro na análise wavelet quântica: %s",
                self.current_trace_id,
                e,
            )
            import traceback

            self.logger.debug(
                "TraceID: %s - %s", self.current_trace_id, traceback.format_exc()
            )

        return patterns

    def _build_wavelet_circuit(
        self, normalized: np.ndarray, n_qubits: int
    ) -> "QuantumCircuit":
        """Create the quantum wavelet circuit.

        Parameters
        ----------
        normalized : np.ndarray
            Normalized series encoded as rotation angles.
        n_qubits : int
            Number of qubits in the circuit.

        Returns
        -------
        QuantumCircuit
            Configured circuit for the wavelet transform.
        """
        qc_wavelet = QuantumCircuit(n_qubits)
        for i, value in enumerate(normalized[: min(2**n_qubits, len(normalized))]):
            qubit_idx = i % n_qubits
            qc_wavelet.ry(value, qubit_idx)
            if qubit_idx > 0:
                qc_wavelet.cx(qubit_idx - 1, qubit_idx)

        for scale in range(1, n_qubits):
            qc_wavelet.h(scale - 1)
            for j in range(scale, n_qubits):
                qc_wavelet.cx(scale - 1, j)
            qc_wavelet.p(np.pi / (scale + 1), scale - 1)

        return qc_wavelet

    def _build_qft_circuit(
        self, normalized: np.ndarray, n_qubits: int
    ) -> "QuantumCircuit":
        """Create the quantum Fourier transform circuit.

        Parameters
        ----------
        normalized : np.ndarray
            Normalized series encoded as rotation angles.
        n_qubits : int
            Number of qubits in the circuit.

        Returns
        -------
        QuantumCircuit
            Circuit configured for the QFT.
        """
        qc_qft = QuantumCircuit(n_qubits)
        for i, value in enumerate(normalized[: min(2**n_qubits, len(normalized))]):
            qubit_idx = i % n_qubits
            qc_qft.ry(value, qubit_idx)
            if i > 0:
                diff = normalized[i] - normalized[i - 1] if i > 0 else 0
                qc_qft.rz(diff * np.pi, qubit_idx)

        qft = QFT(n_qubits)
        qc_qft.compose(qft, inplace=True)
        return qc_qft

    def _simulate_circuit(self, circuit: "QuantumCircuit", shots: int) -> np.ndarray:
        """Run the circuit simulation and return the statevector.

        Parameters
        ----------
        circuit : QuantumCircuit
            Circuit to execute.
        shots : int
            Number of simulation runs.

        Returns
        -------
        np.ndarray
            Statevector produced by the simulator.
        """
        validate_circuit_limits(circuit)
        simulator = Aer.get_backend("statevector_simulator")
        transpiled = transpile(circuit, simulator)
        job = simulator.run(transpiled, shots=shots)
        result = job.result()
        return result.get_statevector()

    def _extract_change_points(
        self,
        phases: np.ndarray,
        timestamps: pd.DatetimeIndex,
        performance: np.ndarray,
    ) -> List[Dict[str, Any]]:
        """Detect regime change points from phase information.

        Parameters
        ----------
        phases : np.ndarray
            Phases extracted from the wavelet statevector.
        timestamps : pandas.DatetimeIndex
            Timestamp index of the original series.
        performance : np.ndarray
            Original performance values used for window sizing.

        Returns
        -------
        List[Dict[str, Any]]
            Structured information on detected change points.
        """
        patterns: List[Dict[str, Any]] = []
        entropy_values: List[float] = []
        window_size = min(max(5, len(performance) // 10), 20)

        for i in range(len(phases) - window_size):
            window = phases[i : i + window_size]
            n_bins = min(16, window_size)
            phase_bins = np.histogram(window, bins=n_bins, range=(-np.pi, np.pi))[0]
            phase_probs = (
                phase_bins / np.sum(phase_bins)
                if np.sum(phase_bins) > 0
                else np.zeros_like(phase_bins)
            )
            entropy = -np.sum(p * np.log2(p) if p > 0 else 0 for p in phase_probs)
            entropy_values.append(entropy)

        if len(entropy_values) > 5:
            entropy_median = np.median(entropy_values)
            entropy_mad = np.median(np.abs(np.array(entropy_values) - entropy_median))
            entropy_threshold = (
                entropy_median + (2.0 + 0.5 * np.log(len(entropy_values))) * entropy_mad
            )

            change_points = []
            for i in range(2, len(entropy_values) - 2):
                is_peak = (
                    entropy_values[i] > entropy_threshold
                    and entropy_values[i] > entropy_values[i - 1]
                    and entropy_values[i] > entropy_values[i - 2]
                    and entropy_values[i] > entropy_values[i + 1]
                    and entropy_values[i] > entropy_values[i + 2]
                )
                if is_peak:
                    change_points.append(i)

            if change_points:
                change_point_details = []
                for cp in change_points:
                    orig_index = cp + window_size // 2
                    if orig_index < len(timestamps):
                        segment_size = min(
                            window_size, orig_index, len(performance) - orig_index
                        )
                        if segment_size > 2:
                            change_point_details.append(
                                {
                                    "index": int(orig_index),
                                    "timestamp": (
                                        timestamps[orig_index].isoformat()
                                        if isinstance(timestamps[orig_index], datetime)
                                        else str(timestamps[orig_index])
                                    ),
                                    "entropy": float(entropy_values[cp]),
                                    "normalized_entropy": float(
                                        (entropy_values[cp] - entropy_median)
                                        / entropy_mad
                                        if entropy_mad > 0
                                        else 0
                                    ),
                                }
                            )

                if change_point_details:
                    patterns.append(
                        {
                            "id": str(uuid.uuid4()),
                            "type": "quantum_regime_change",
                            "description": f"Detecção de {len(change_point_details)} pontos de mudança de regime via análise de entropia quântica",
                            "confidence": float(
                                min(
                                    0.95,
                                    0.6
                                    + 0.3
                                    * (
                                        np.max(
                                            [
                                                d["normalized_entropy"]
                                                for d in change_point_details
                                            ]
                                        )
                                        / 5.0
                                    ),
                                )
                            ),
                            "metadata": {
                                "change_points": change_point_details,
                                "entropy_threshold": float(entropy_threshold),
                                "entropy_median": float(entropy_median),
                                "entropy_mad": float(entropy_mad),
                            },
                            "timestamp": datetime.now(timezone.utc).isoformat(),
                        }
                    )

        return patterns

    def _analyze_approximation_coeffs(
        self, approx_coeffs: np.ndarray
    ) -> Optional[Dict[str, Any]]:
        """
        Analisa coeficientes de aproximação para detectar tendências.

        Args:
            approx_coeffs: Coeficientes de aproximação da transformada wavelet

        Returns:
            Padrão de tendência detectado ou None
        """
        if len(approx_coeffs) < 3:
            return None

        # Calcular inclinação da linha de tendência
        x = np.arange(len(approx_coeffs))
        slope, _ = np.polyfit(x, approx_coeffs, 1)

        # Calcular confiança com base na força da tendência e R²
        r_squared = np.corrcoef(x, approx_coeffs)[0, 1] ** 2
        confidence = min(0.95, abs(slope) * 10 * r_squared)

        if confidence < self.min_confidence:
            return None

        # Determinar tipo de tendência
        if slope > 0:
            trend_type = "uptrend"
            description = "Tendência de alta consistente detectada na série temporal"
        else:
            trend_type = "downtrend"
            description = "Tendência de baixa consistente detectada na série temporal"

        return {
            "id": str(uuid.uuid4()),
            "type": trend_type,
            "description": description,
            "confidence": float(confidence),
            "metadata": {"slope": float(slope), "r_squared": float(r_squared)},
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

    def _analyze_detail_coeffs(
        self, detail_coeffs: np.ndarray, level: int
    ) -> List[Dict[str, Any]]:
        """
        Analisa coeficientes de detalhe para detectar reversões.

        Args:
            detail_coeffs: Coeficientes de detalhe da transformada wavelet
            level: Nível da decomposição wavelet

        Returns:
            Lista de padrões de reversão detectados
        """
        patterns = []

        if len(detail_coeffs) < 5:
            return patterns

        # Identificar picos nos coeficientes de detalhe
        # (valores altos indicam mudanças bruscas)
        threshold = np.mean(detail_coeffs) + 1.5 * np.std(detail_coeffs)
        peaks = np.where(np.abs(detail_coeffs) > threshold)[0]

        for peak in peaks:
            # Calcular confiança com base na amplitude do pico
            peak_amplitude = abs(detail_coeffs[peak])
            confidence = min(
                0.95, peak_amplitude / (2 * np.mean(np.abs(detail_coeffs)))
            )

            if confidence < self.min_confidence:
                continue

            # Determinar tipo de reversão
            if detail_coeffs[peak] > 0:
                reversal_type = "bullish_reversal"
                description = "Possível reversão de baixa para alta detectada"
            else:
                reversal_type = "bearish_reversal"
                description = "Possível reversão de alta para baixa detectada"

            patterns.append(
                {
                    "id": str(uuid.uuid4()),
                    "type": reversal_type,
                    "description": description,
                    "confidence": float(confidence),
                    "metadata": {
                        "wavelet_level": level,
                        "peak_position": int(peak),
                        "peak_amplitude": float(peak_amplitude),
                    },
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                }
            )

        return patterns

    def _analyze_seasonality(self, performance: np.ndarray) -> Optional[Dict[str, Any]]:
        """
        Analisa a série temporal para detectar padrões sazonais.

        Args:
            performance: Array com desempenho (PNL) das decisões

        Returns:
            Padrão de sazonalidade detectado ou None
        """
        if len(performance) < 10:
            return None

        try:
            # Aplicar FFT para detectar frequências dominantes
            fft_values = np.fft.rfft(performance)
            fft_magnitudes = np.abs(fft_values)

            # Ignorar componente DC (índice 0)
            fft_magnitudes[0] = 0

            # Encontrar frequência dominante
            dominant_freq_idx = np.argmax(fft_magnitudes)

            if dominant_freq_idx == 0:
                return None  # Sem frequência dominante

            # Calcular período do ciclo
            cycle_period = len(performance) / dominant_freq_idx

            # Calcular a força relativa do ciclo
            relative_strength = fft_magnitudes[dominant_freq_idx] / np.sum(
                fft_magnitudes
            )
            confidence = min(0.95, relative_strength * 3)

            if confidence < self.min_confidence:
                return None

            return {
                "id": str(uuid.uuid4()),
                "type": "seasonality",
                "description": f"Padrão sazonal detectado com período aproximado de {cycle_period:.1f} pontos",
                "confidence": float(confidence),
                "metadata": {
                    "cycle_period": float(cycle_period),
                    "relative_strength": float(relative_strength),
                },
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

        except Exception as e:
            self.logger.warning(
                "TraceID: %s - Erro na análise de sazonalidade: %s",
                self.current_trace_id,
                e,
            )
            return None

    @numpy_cache(maxsize=32)
    def _normalize_for_quantum(self, values: np.ndarray) -> np.ndarray:
        """
        Normaliza valores para representação quântica com tratamento robusto de outliers.
        Otimizado para dados financeiros que podem conter valores extremos.

        Args:
            values: Array de valores a serem normalizados

        Returns:
            Array normalizado para intervalo [0, π/2]
        """
        if len(values) == 0:
            return np.array([])

        # ETAPA 1: Detecção e tratamento robusto de outliers

        # Calcular estatísticas robustas
        median_val = np.median(values)

        # Usar MAD (Median Absolute Deviation) para estimar a dispersão
        # MAD é mais robusto que o desvio padrão na presença de outliers
        # extremos
        mad = np.median(np.abs(values - median_val))

        # Aplicar um fator de escala para aproximar o MAD do desvio padrão
        # Para uma distribuição normal, MAD * 1.4826 ≈ desvio padrão
        mad_scaled = mad * 1.4826

        # Definir limites adaptativos baseados no tamanho da amostra
        # Para amostras maiores, podemos ser mais estritos com outliers
        if len(values) > 100:
            lower_bound = median_val - 5.0 * mad_scaled
            upper_bound = median_val + 5.0 * mad_scaled
        elif len(values) > 30:
            lower_bound = median_val - 7.0 * mad_scaled
            upper_bound = median_val + 7.0 * mad_scaled
        else:
            # Para amostras pequenas, ser mais conservador
            lower_bound = median_val - 10.0 * mad_scaled
            upper_bound = median_val + 10.0 * mad_scaled

        # ETAPA 2: Clipping com limites adaptativos
        clipped = np.clip(values, lower_bound, upper_bound)

        # ETAPA 3: Normalização de duas etapas para melhor distribuição

        # Etapa 3.1: Normalização linear inicial para [0, 1]
        min_clipped = np.min(clipped)
        max_clipped = np.max(clipped)

        # Verificar se temos um range significativo após o clipping
        if max_clipped - min_clipped > 1e-10:
            linear_normalized = (clipped - min_clipped) / (max_clipped - min_clipped)
        else:
            # Se todos os valores são praticamente iguais, usar um valor constante
            # que representa a posição central do espaço de estados
            return np.ones_like(values) * (np.pi / 4)

        # Etapa 3.2: Aplicar transformação não-linear para melhorar a distribuição
        # A operação de raiz quadrada amplia a região próxima de zero,
        # permitindo discriminar melhor valores pequenos e compactar os grandes
        nonlinear_normalized = np.sqrt(linear_normalized)

        # ETAPA 4: Mapear para o intervalo [0, π/2] para ângulos de rotação
        # quântica
        return nonlinear_normalized * (np.pi / 2)

    def _convert_patterns_to_insights(
        self, patterns: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        Converte padrões detectados em insights metacognitivos.

        Args:
            patterns: Lista de padrões detectados

        Returns:
            Lista de insights formatados para o sistema metacognitivo
        """
        insights = []

        for pattern in patterns:
            if pattern["confidence"] < self.min_confidence:
                continue

            # Mapear tipos de padrão para ajustes metacognitivos
            pattern_type = pattern["type"]

            if pattern_type == "uptrend":
                insight = {
                    "id": pattern["id"],
                    "type": "trend_following_adjustment",
                    "description": "Tendência de alta detectada - Priorizar estratégias de seguimento de tendência",
                    "confidence": pattern["confidence"],
                    "suggested_adjustments": [
                        {
                            "parameter": "strategy_weight.trend_following",
                            "value": 0.7,
                            "reason": "Tendência de alta consistente identificada na análise temporal",
                        }
                    ],
                    "timestamp": pattern["timestamp"],
                }
                insights.append(insight)

            elif pattern_type == "downtrend":
                insight = {
                    "id": pattern["id"],
                    "type": "trend_following_adjustment",
                    "description": "Tendência de baixa detectada - Priorizar estratégias de seguimento de tendência",
                    "confidence": pattern["confidence"],
                    "suggested_adjustments": [
                        {
                            "parameter": "strategy_weight.trend_following",
                            "value": 0.7,
                            "reason": "Tendência de baixa consistente identificada na análise temporal",
                        }
                    ],
                    "timestamp": pattern["timestamp"],
                }
                insights.append(insight)

            elif pattern_type in ("bullish_reversal", "bearish_reversal"):
                insight = {
                    "id": pattern["id"],
                    "type": "mean_reversion_adjustment",
                    "description": "Padrão de reversão detectado - Priorizar estratégias de reversão à média",
                    "confidence": pattern["confidence"],
                    "suggested_adjustments": [
                        {
                            "parameter": "strategy_weight.mean_reversion",
                            "value": 0.7,
                            "reason": "Padrão de reversão identificado na análise wavelet temporal",
                        }
                    ],
                    "timestamp": pattern["timestamp"],
                }
                insights.append(insight)

            elif pattern_type == "seasonality":
                insight = {
                    "id": pattern["id"],
                    "type": "cycle_trading_adjustment",
                    "description": f'Ciclo temporal detectado - Ajustar periodicidade para ciclos de {pattern["metadata"]["cycle_period"]:.1f} pontos',
                    "confidence": pattern["confidence"],
                    "suggested_adjustments": [
                        {
                            "parameter": "analysis_cycle_period",
                            "value": max(
                                1, int(pattern["metadata"]["cycle_period"] / 3)
                            ),
                            "reason": "Ciclo sazonal detectado na análise temporal",
                        }
                    ],
                    "timestamp": pattern["timestamp"],
                }
                insights.append(insight)

            elif pattern_type == "quantum_reversal":
                insight = {
                    "id": pattern["id"],
                    "type": "quantum_sensitivity_adjustment",
                    "description": "Reversão quântica detectada - Aumentar sensibilidade de entropia",
                    "confidence": pattern["confidence"],
                    "suggested_adjustments": [
                        {
                            "parameter": "entropy_sensitivity",
                            "value": 0.8,
                            "reason": "Padrão de reversão quântica identificado na análise wavelet",
                        }
                    ],
                    "timestamp": pattern["timestamp"],
                }
                insights.append(insight)

            elif pattern_type == "quantum_trend":
                insight = {
                    "id": pattern["id"],
                    "type": "quantum_coherence_adjustment",
                    "description": "Tendência quântica detectada - Aumentar profundidade de percepção",
                    "confidence": pattern["confidence"],
                    "suggested_adjustments": [
                        {
                            "parameter": "perception_depth",
                            "value": 5,
                            "reason": "Padrão de tendência quântica identificado na análise wavelet",
                        }
                    ],
                    "timestamp": pattern["timestamp"],
                }
                insights.append(insight)

        return insights


# Wrapper utilizado pelos subsistemas de mercado para evitar ambiguidade
class MarketTemporalPatternDetector(TemporalPatternDetector):
    """Versão nomeada do detector de mercado."""

    pass


# Manter o nome antigo por compatibilidade
TemporalPatternDetector = MarketTemporalPatternDetector
