"""
Motor Principal do QUALIA Trading System

Este módulo contém o motor central que integra todos os componentes:
- Análise quântica do QUALIA
- Estratégias de trading
- Gerenciamento de risco
- Execução de ordens via Kraken

O motor é responsável pela orquestração do fluxo completo, desde a análise
dos dados de mercado, processamento quântico, geração de sinais e execução
de ordens com gerenciamento de risco adequado.
"""

from ..adaptive_evolution import AdaptiveConsciousnessEvolution
from ..market.kraken_integration import KrakenIntegration as UnifiedExchange
from ..market.qast_evolutionary_strategy import QASTEvolutionaryStrategy
from ..market.qualia_market_pattern_detector import QUALIAMarketPatternDetector
from ..market.qualia_metacognition_trading import QUALIAMetacognitionTrading
from ..market.self_evolving_trading_system import SelfEvolvingTradingSystem
from ..metacognition import MetacognitiveContext

try:
    from ..market.quantum_risk_position_controller import (
        QuantumRiskPositionController,
    )
except ImportError:
    QuantumRiskPositionController = None  # type: ignore
try:
    from ..market.qualia_trading_channel import QUALIATradingChannel
except ImportError:
    QUALIATradingChannel = None  # type: ignore
import base64
import hashlib
import json
import os
import asyncio
import queue
import stat
import threading
import time
import traceback
from datetime import datetime, timedelta, timezone
from enum import Enum
from typing import Any, Dict, List, Optional, Tuple

import numpy as np
import pandas as pd
import psutil
from cryptography.fernet import Fernet

from ..common_types import Position
from ..config import get_global_config_loader
from ..config.settings import get_env
from ..core.pid_optimizer import load_coefficients, record_pid_performance

# YAA: Removendo a definição global de quantum_metrics_calculator pois agora é um atributo de instância.
# quantum_metrics_calculator = None  # YAA: Instância global removida, código existente que verifica isso ainda funcionará (será False)
from ..core.universe import QUALIAQuantumUniverse
from ..market.quantum_metrics_calculator import QuantumMetricsCalculator
from ..market.quantum_risk_analyzer import QuantumRiskAnalyzer
from ..risk.manager import QUALIARiskManager

# Use ``ScalpingStrategy`` from the consolidated scalping package.
from ..strategies.scalping.core import ScalpingStrategy
from ..utils.logging_config import get_qualia_logger
from ..utils.logger import get_logger
from .base_integration import CryptoDataFetcher
from ..common.specs import MarketSpec


class OrderSide(str, Enum):
    """Enumerates trade directions."""

    BUY = "buy"
    SELL = "sell"


class OrderType(str, Enum):
    """Enumerates order types supported by the engine."""

    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"


class SecurityManager:
    """Gerencia aspectos de segurança do sistema de trading."""

    def __init__(self, key: Optional[bytes] = None):
        """Inicializa o gerenciador carregando ou gerando a chave de criptografia."""
        env_key = get_env("QUALIA_SECRET_KEY", None)
        key_path = get_env("QUALIA_SECRET_KEY_PATH", None)

        if key is not None:
            self.key = key
        elif env_key:
            if env_key == "dev_secret_key":
                raise EnvironmentError(
                    "QUALIA_SECRET_KEY não deve ser 'dev_secret_key'"
                )
            try:
                decoded = base64.urlsafe_b64decode(env_key)
            except Exception as exc:
                raise EnvironmentError(
                    "QUALIA_SECRET_KEY deve ser uma chave URL-safe base64 de 32 bytes"
                ) from exc
            if len(decoded) != 32:
                raise EnvironmentError(
                    "QUALIA_SECRET_KEY deve ser uma chave URL-safe base64 de 32 bytes"
                )
            self.key = env_key.encode()
        elif key_path:
            os.makedirs(os.path.dirname(key_path), exist_ok=True)
            if os.path.exists(key_path):
                with open(key_path, "rb") as f:
                    self.key = f.read().strip()
                current_mode = stat.S_IMODE(os.stat(key_path).st_mode)
                if current_mode != 0o600:
                    os.chmod(key_path, 0o600)
            else:
                self.key = Fernet.generate_key()
                with open(key_path, "wb") as f:
                    os.chmod(key_path, 0o600)
                    f.write(self.key)
        else:
            self.key = Fernet.generate_key()

        self.cipher_suite = Fernet(self.key)

    def encrypt_credentials(self, api_key: str, api_secret: str) -> Dict[str, bytes]:
        """Criptografa credenciais sensíveis."""
        return {
            "api_key": self.cipher_suite.encrypt(api_key.encode()),
            "api_secret": self.cipher_suite.encrypt(api_secret.encode()),
        }

    def decrypt_credentials(self, encrypted_data: Dict[str, bytes]) -> Tuple[str, str]:
        """Descriptografa credenciais."""
        return (
            self.cipher_suite.decrypt(encrypted_data["api_key"]).decode(),
            self.cipher_suite.decrypt(encrypted_data["api_secret"]).decode(),
        )

    @staticmethod
    def hash_sensitive_data(
        data: str, salt: Optional[bytes] = None
    ) -> Tuple[bytes, bytes]:
        """Gera um hash seguro para dados sensíveis."""
        if salt is None:
            salt = os.urandom(32)
        key = hashlib.pbkdf2_hmac("sha256", data.encode("utf-8"), salt, 100000)
        return key, salt


# Loggers principais do módulo
logger = get_qualia_logger("market.trading_engine")
risk_logger = get_qualia_logger("risk_management")


class TradingEngine:
    """Core controller coordinating market analysis and execution."""

    DEFAULT_PRELOAD_CANDLES_1H = 1500

    @staticmethod
    def _load_preload_candles() -> int:
        """Carrega do JSON a quantidade de candles de 1h a serem pré-carregados."""
        loader = get_global_config_loader()
        try:
            data = loader.load()
            return int(
                data.get("strategy_config", {}).get(
                    "preload_candles_1h",
                    TradingEngine.DEFAULT_PRELOAD_CANDLES_1H,
                )
            )
        except FileNotFoundError:
            return TradingEngine.DEFAULT_PRELOAD_CANDLES_1H
        except json.JSONDecodeError:
            return TradingEngine.DEFAULT_PRELOAD_CANDLES_1H

    @staticmethod
    def _ohlcv_df_to_dict_of_lists(df) -> dict:
        if df is None or df.empty:
            return {
                "timestamps": [],
                "open": [],
                "high": [],
                "low": [],
                "close": [],
                "volume": [],
            }

        # Ensure 'timestamp' column is in datetime format if not already
        # Convert to milliseconds for consistency with expected format
        if not pd.api.types.is_datetime64_any_dtype(df["timestamp"]):
            df["timestamp"] = pd.to_datetime(df["timestamp"], unit="ms")

        return {
            "timestamps": (df["timestamp"].astype(np.int64) // 10**6).tolist(),
            "open": df["open"].tolist(),
            "high": df["high"].tolist(),
            "low": df["low"].tolist(),
            "close": df["close"].tolist(),
            "volume": df["volume"].tolist(),
        }

    @staticmethod
    def _ohlcv_dict_to_df(data: Dict[str, List[Any]]) -> pd.DataFrame:
        """Converte um dicionário de listas OHLCV em DataFrame."""
        if (
            not data
            or "close" not in data
            or data.get("close") is None
            or len(data.get("close", [])) == 0
        ):
            return pd.DataFrame()

        df = pd.DataFrame(
            {
                "timestamp": pd.to_datetime(data["timestamps"], unit="ms"),
                "open": data["open"],
                "high": data["high"],
                "low": data["low"],
                "close": data["close"],
                "volume": data.get("volume", [0.0] * len(data["close"])),
            }
        )
        df.set_index("timestamp", inplace=True)
        return df

    """
    Motor principal do sistema QUALIA Trading.

    Coordena todas as atividades de trading, incluindo:
    - Coleta e processamento de dados de mercado
    - Análise quântica via framework QUALIA
    - Geração de sinais de trading
    - Gerenciamento de risco e sizing de posições
    - Execução de ordens
    - Monitoramento de posições
    """

    def __init__(
        self,
        api_key: str,
        api_secret: str,
        symbols: List[str],
        initial_capital: float = 10000.0,
        risk_profile: str = "balanced",
        use_quantum_analysis: bool = True,
        live_mode: bool = False,
        trading_fee_pct: float = 0.0026,
        mass_threshold: float = 0.05,
    ):
        """
        Inicializa o motor de trading.

        Args:
            api_key: Chave da API da Kraken
            api_secret: Secret da API da Kraken
            symbols: Lista de símbolos para operar (ex: ['BTC/USD', 'ETH/USD'])
            initial_capital: Capital inicial em USD
            risk_profile: Perfil de risco ('conservative', 'balanced', 'aggressive')
            use_quantum_analysis: Se True, utiliza análise quântica do QUALIA
            live_mode: Se True, executa ordens reais; se False, modo simulação
            trading_fee_pct: Percentual de taxa cobrada pela exchange por trade.
                Pode ser sobrescrito pela variável de ambiente ``TRADING_FEE_PCT``.
            mass_threshold: Limiar mínimo de ``informational_mass`` do universo
                abaixo do qual novas posições não são abertas.
        """
        # Configuração de segurança
        self.security = SecurityManager()

        # Armazenar credenciais de forma segura
        self._secure_config = {
            "encrypted_credentials": self.security.encrypt_credentials(
                api_key, api_secret
            ),
            "credentials_salt": os.urandom(32),
        }

        # Hashear informações sensíveis para logging seguro
        self._api_key_hash, _ = self.security.hash_sensitive_data(
            api_key, self._secure_config["credentials_salt"]
        )

        # Configurações básicas
        self.symbols = symbols
        self.initial_capital = initial_capital
        self.risk_profile = risk_profile
        self.use_quantum_analysis = use_quantum_analysis
        self.live_mode = live_mode

        fee_env = get_env("TRADING_FEE_PCT", None, warn=False)
        if fee_env is not None:
            try:
                trading_fee_pct = float(fee_env)
            except ValueError:
                logger.warning(
                    "Valor inválido para TRADING_FEE_PCT '%s'. Usando %s.",
                    fee_env,
                    trading_fee_pct,
                )
        self.trading_fee_pct = trading_fee_pct
        self.mass_threshold = mass_threshold
        self.quantum_metrics_calculator = (
            None  # YAA: Inicializando como atributo de instância
        )

        # Parâmetros configuráveis via ambiente
        self.REDUCE_EXPOSURE_MIN_CONFIDENCE = float(
            get_env("REDUCE_EXPOSURE_MIN_CONFIDENCE", "0.6", warn=False)
        )
        self.REDUCE_EXPOSURE_MIN_POSITION_AGE_MINUTES = float(
            get_env("REDUCE_EXPOSURE_MIN_POSITION_AGE_MINUTES", "5", warn=False)
        )
        self.REDUCE_EXPOSURE_CLOSE_SINGLE_POSITION = str(
            get_env("REDUCE_EXPOSURE_CLOSE_SINGLE_POSITION", "false", warn=False)
        ).lower() in {"1", "true", "yes", "y", "t"}
        self.REDUCE_EXPOSURE_DEFAULT_PCT = float(
            get_env("REDUCE_EXPOSURE_DEFAULT_PCT", "50", warn=False)
        )

        # Estado do sistema
        # 'initialized', 'running', 'stopping', 'stopped', 'error'
        self.status = "initialized"
        self.last_update = datetime.now(timezone.utc)
        self.error_message = None
        self.start_time = time.time()

        # Componentes do sistema (inicializados como None)
        self.exchange = None
        self.strategy = None
        self.risk_manager = None
        self.quantum_universe = None
        self.quantum_risk_analyzer = None

        # Componentes avançados do sistema QUALIA
        self.qualia_trading_channel = None
        self.quantum_risk_position_controller = None
        self.qast_evolutionary_strategy = None
        self.qualia_market_pattern_detector = None
        self.self_evolving_trading_system = None
        self.metacognition_system = None
        self.adaptive_consciousness = None
        self._last_metacognitive_ctx: Optional[MetacognitiveContext] = None

        # Estado de trading
        self.market_data = {symbol: {} for symbol in symbols}
        self.positions = []
        self.trade_history = []
        self.performance_metrics = {
            "total_trades": 0,
            "winning_trades": 0,
            "losing_trades": 0,
            "total_pnl": 0.0,
            "win_rate": 0.0,
            "max_drawdown": 0.0,
            "sharpe_ratio": 0.0,
        }
        self.quantum_metrics = {}

        # Threads e controle
        self.data_thread = None
        self.analysis_thread = None
        self.trade_thread = None
        self.monitor_thread = None
        self.health_monitor_thread = None
        self.stop_event = threading.Event()
        self.data_queue = queue.Queue(maxsize=100)
        self.signal_queue = queue.Queue(maxsize=100)
        self._pos_lock = threading.Lock()

        # Intervalos de tempo (em segundos)
        self.data_interval = 5  # Coleta de dados a cada 5 segundos
        self.analysis_interval = 30  # Análise a cada 30 segundos
        self.trade_interval = 10  # Verificação de sinais a cada 10 segundos
        self.monitor_interval = 5  # Intervalo base de monitoramento de posições
        self.health_check_interval = 60  # Verificação de saúde a cada minuto

        # Intervalos dinâmicos de monitoramento
        self.monitor_min_interval = self.monitor_interval
        self.monitor_max_interval = self.monitor_interval * 8
        self._current_monitor_interval = self.monitor_min_interval
        self._monitor_backoff_level = 0
        self._consecutive_monitor_failures = 0
        self.monitor_close_threshold = 0.005

        # Timeframes para análise
        self.timeframes = ["1m", "5m", "15m", "1h"]

        # Métricas de desempenho
        self._last_health_check = time.time()
        self._consecutive_errors = 0
        self._last_error = None

        # Inicializar componentes
        try:
            self._initialize_components()
            logger.info(
                "Motor de Trading inicializado",
                extra={
                    "context": {
                        "symbols_count": len(symbols),
                        "risk_profile": risk_profile,
                        "mode": "LIVE" if live_mode else "SIMULAÇÃO",
                        "quantum_analysis": use_quantum_analysis,
                        "initial_capital": initial_capital,
                    }
                },
            )
        except Exception as e:
            self.status = "error"
            self.error_message = str(e)
            logger.critical(
                "Falha crítica na inicialização do motor de trading",
                exc_info=True,
                extra={"context": {"error": str(e)}},
            )
            raise

    def _wait_for_stop(self, seconds: float) -> bool:
        """Waits for the given duration or until ``stop_event`` is set.

        Parameters
        ----------
        seconds : float
            Interval to wait.

        Returns
        -------
        bool
            ``True`` if the stop event was triggered.
        """

        return self.stop_event.wait(timeout=seconds)

    def _ensure_initialized(self) -> bool:
        """
        Verifica se o motor está em um estado válido para operação.

        Returns:
            bool: True se o sistema estiver pronto para operar, False caso contrário
        """
        if not hasattr(self, "exchange") or not self.exchange:
            logger.error("Exchange não inicializada")
            return False

        if not hasattr(self, "risk_manager") or not self.risk_manager:
            logger.error("Gerenciador de risco não inicializado")
            return False

        # Verificar conexão com a exchange
        try:
            if not self.exchange.check_connection():
                logger.error("Sem conexão com a exchange")
                return False
        except Exception as e:
            logger.error(f"Falha ao verificar conexão com a exchange: {e}")
            return False

        return True

    def _initialize_components(self) -> None:
        """
        Inicializa todos os componentes do sistema.

        Raises:
            RuntimeError: Se ocorrer um erro crítico na inicialização
        """
        try:
            # 1. Decriptografar credenciais
            try:
                api_key, api_secret = self.security.decrypt_credentials(
                    self._secure_config["encrypted_credentials"]
                )
            except Exception as e:
                error_msg = f"Falha ao descriptografar credenciais: {e}"
                logger.critical(error_msg)
                raise RuntimeError(error_msg) from e

            # 2. Inicializar conexão com a Exchange
            try:
                self.exchange = UnifiedExchange(
                    api_key=api_key, api_secret=api_secret, test_mode=not self.live_mode
                )

                # Verificar conexão
                if not self.exchange.check_connection():
                    error_msg = "Não foi possível estabelecer conexão com a exchange"
                    logger.error(error_msg)
                    raise ConnectionError(error_msg)

                logger.info("Conexão com a exchange estabelecida com sucesso")

            except Exception as e:
                error_msg = f"Falha ao conectar à exchange: {e}"
                logger.critical(error_msg, exc_info=True)
                raise RuntimeError(error_msg) from e

            # 3. Inicializar estratégia de trading
            try:
                self.strategy = ScalpingStrategy(
                    risk_profile=self.risk_profile,
                    use_quantum_metrics=self.use_quantum_analysis,
                )
                logger.info(
                    "Estratégia de trading inicializada",
                    extra={"context": {"risk_profile": self.risk_profile}},
                )

            except Exception as e:
                error_msg = f"Falha ao inicializar estratégia de trading: {e}"
                logger.critical(error_msg, exc_info=True)
                raise RuntimeError(error_msg) from e

            # 4. Inicializar gerenciamento de risco
            try:
                self.risk_manager = QUALIARiskManager(
                    initial_capital=self.initial_capital, risk_profile=self.risk_profile
                )
                logger.info(
                    "Gerenciador de risco inicializado",
                    extra={
                        "context": {
                            "risk_profile": self.risk_profile,
                            "initial_capital": self.initial_capital,
                        }
                    },
                )

            except Exception as e:
                error_msg = f"Falha ao inicializar gerenciador de risco: {e}"
                logger.critical(error_msg, exc_info=True)
                raise RuntimeError(error_msg) from e

            # Inicializar universo quântico QUALIA (se habilitado)
            if self.use_quantum_analysis:
                try:
                    self.quantum_universe = QUALIAQuantumUniverse(
                        n_qubits=8,
                        scr_depth=10,
                        base_lambda=0.5,
                        alpha=0.1,
                        retro_strength=0.0,
                        num_ctc_qubits=0,
                        thermal_coefficient=0.1,
                        initial_state_type="qft",
                    )
                    logger.info("Universo Quântico QUALIA inicializado com 8 qubits")

                    # YAA: Instanciar QuantumMetricsCalculator aqui
                    if QuantumMetricsCalculator:
                        self.quantum_metrics_calculator = QuantumMetricsCalculator(
                            qualia_universe=self.quantum_universe
                            # Nota: Se QuantumMetricsCalculator requerer outros parâmetros
                            # (ex: qast_cycle_manager, config), eles precisarão ser fornecidos aqui.
                        )
                        logger.info(
                            "QuantumMetricsCalculator inicializado com sucesso."
                        )
                    else:
                        logger.warning(
                            "QuantumMetricsCalculator não pôde ser importado ou inicializado. "
                            "Funcionalidades quânticas avançadas podem estar limitadas."
                        )
                        self.quantum_metrics_calculator = None

                    # Inicializar analisador de risco quântico
                    self.quantum_risk_analyzer = QuantumRiskAnalyzer(
                        quantum_universe=self.quantum_universe,
                        n_qubits=8,
                        monte_carlo_samples=10000,
                        use_quantum_simulation=True,
                    )
                    logger.info("Analisador de Risco Quântico inicializado")

                    # Inicializar controlador de posições baseado em dinâmica
                    # quântica
                    self.quantum_risk_position_controller = (
                        QuantumRiskPositionController(
                            risk_analyzer=self.quantum_risk_analyzer,
                            capital_allocation=self.initial_capital,
                            max_risk_per_trade=(
                                0.02
                                if self.risk_profile == "conservative"
                                else (0.03 if self.risk_profile == "balanced" else 0.05)
                            ),
                        )
                    )
                    logger.info(
                        f"Controlador de Posições Quântico inicializado (risco máximo por trade: {self.quantum_risk_position_controller.max_risk_per_trade * 100:.1f}%)"
                    )
                    risk_logger.info(
                        "Sistema de análise de risco com modelagem probabilística quântica ativado"
                    )

                    # Inicializar componentes avançados QUALIA
                    self._initialize_qualia_advanced_components()

                except Exception as e:
                    logger.warning(
                        f"Não foi possível inicializar Universo Quântico QUALIA: {e}. Continuando sem análise quântica."
                    )
                    self.use_quantum_analysis = False

            # Inicializar dados de mercado para cada símbolo e timeframe
            for symbol in self.symbols:
                self.market_data[symbol] = {}
                for tf in self.timeframes:
                    self.market_data[symbol][tf] = {
                        "timestamps": [],
                        "open": [],
                        "high": [],
                        "low": [],
                        "close": [],
                        "volume": [],
                    }

            # Buscar dados iniciais e posições abertas
            self._fetch_initial_market_data()
            self._fetch_existing_positions()

        except Exception as e:
            logger.error(f"Erro ao inicializar componentes: {e}")
            self.status = "error"
            self.error_message = str(e)
            raise

    def _fetch_initial_market_data(self) -> None:
        """
        Busca dados iniciais de mercado para todos os símbolos e timeframes.
        """
        logger.info("Buscando dados de mercado iniciais...")

        preload_1h = self._load_preload_candles()

        for symbol in self.symbols:
            try:
                for tf in self.timeframes:
                    limit = preload_1h if tf == "1h" else 100
                    df_data = asyncio.run(
                        self.exchange.market_data.fetch_ohlcv(symbol, tf, limit=limit)
                    )
                    data = TradingEngine._ohlcv_df_to_dict_of_lists(df_data)
                    if data and len(data.get("close", [])) > 0:
                        self.market_data[symbol][tf] = data
                        logger.info(
                            f"Dados iniciais obtidos: {symbol} {tf}, {len(data['close'])} candles"
                        )
                    else:
                        logger.warning(
                            f"Não foi possível obter dados iniciais para {symbol} {tf}"
                        )
            except Exception as e:
                logger.error(f"Erro ao buscar dados iniciais para {symbol}: {e}")

    def _fetch_existing_positions(self) -> None:
        """Busca posições abertas na exchange."""
        if not hasattr(self.exchange, "fetch_positions"):
            return
        try:
            raw_positions = asyncio.run(self.exchange.fetch_positions(self.symbols))
            positions: List[Position] = []
            for p in raw_positions:
                qty = abs(p.get("contracts") or p.get("size") or p.get("quantity", 0.0))
                entry_price = p.get("entryPrice") or p.get("price") or 0.0
                positions.append(
                    Position(
                        symbol=p.get("symbol", ""),
                        side=p.get("side", "buy"),
                        quantity=qty,
                        entry_price=entry_price,
                        entry_time=datetime.now(timezone.utc).isoformat(),
                        position_size=qty * entry_price,
                    )
                )
            self.positions = positions
            logger.info("%d posições carregadas da exchange", len(positions))
        except Exception as e:  # pragma: no cover - external dependency
            logger.error("Erro ao buscar posições: %s", e)

    def start(self) -> bool:
        """
        Inicia o motor de trading e todas as threads de processamento.

        Returns:
            True se iniciado com sucesso, False caso contrário
        """
        if self.status == "running":
            logger.warning("Motor de trading já está em execução")
            return False

        if self.status == "error" and self.error_message:
            logger.error(
                f"Não é possível iniciar motor em estado de erro: {self.error_message}"
            )
            return False

        try:
            # Resetar evento de parada
            self.stop_event.clear()

            # Iniciar threads
            self.data_thread = threading.Thread(
                target=self._data_collection_loop, name="DataCollection"
            )
            self.analysis_thread = threading.Thread(
                target=self._analysis_loop, name="Analysis"
            )
            self.trade_thread = threading.Thread(
                target=self._trade_execution_loop, name="TradeExecution"
            )
            self.monitor_thread = threading.Thread(
                target=self._position_monitoring_loop, name="PositionMonitoring"
            )

            # Configurar threads como daemon para terminarem quando o programa
            # principal encerrar
            self.data_thread.daemon = True
            self.analysis_thread.daemon = True
            self.trade_thread.daemon = True
            self.monitor_thread.daemon = True

            # Iniciar threads
            self.data_thread.start()
            self.analysis_thread.start()
            self.trade_thread.start()
            self.monitor_thread.start()

            self.status = "running"
            self.last_update = datetime.now(timezone.utc)
            logger.info("Motor de trading iniciado com sucesso")

            return True

        except Exception as e:
            logger.error(f"Erro ao iniciar motor de trading: {e}")
            self.status = "error"
            self.error_message = str(e)
            return False

    def initialize(self) -> bool:
        """
        Inicializa o sistema sem iniciar as threads de processamento.

        Returns:
            True se inicializado com sucesso, False caso contrário
        """
        if self.status != "initialized":
            logger.info("Inicializando componentes do sistema de trading")
            try:
                # Buscar dados iniciais de mercado para todos os símbolos
                self._fetch_initial_market_data()

                self.status = "initialized"
                self.last_update = datetime.now(timezone.utc)
                return True

            except Exception as e:
                logger.error(f"Erro ao inicializar sistema de trading: {e}")
                self.status = "error"
                self.error_message = str(e)
                return False
        else:
            logger.warning("Sistema já está inicializado")
            return True

    def stop(self) -> bool:
        """
        Para o motor de trading e todas as threads de processamento.

        Returns:
            True se parado com sucesso, False caso contrário
        """
        if self.status != "running":
            logger.warning(f"Motor de trading não está em execução: {self.status}")
            return False

        try:
            # Sinalizar evento de parada
            self.status = "stopping"
            self.stop_event.set()

            # Aguardar threads terminarem (com timeout)
            timeout = 30  # 30 segundos de timeout

            if self.data_thread and self.data_thread.is_alive():
                self.data_thread.join(timeout)

            if self.analysis_thread and self.analysis_thread.is_alive():
                self.analysis_thread.join(timeout)

            if self.trade_thread and self.trade_thread.is_alive():
                self.trade_thread.join(timeout)

            if self.monitor_thread and self.monitor_thread.is_alive():
                self.monitor_thread.join(timeout)

            # Verificar se alguma thread ainda está rodando
            threads_alive = []
            if self.data_thread and self.data_thread.is_alive():
                threads_alive.append("DataCollection")
            if self.analysis_thread and self.analysis_thread.is_alive():
                threads_alive.append("Analysis")
            if self.trade_thread and self.trade_thread.is_alive():
                threads_alive.append("TradeExecution")
            if self.monitor_thread and self.monitor_thread.is_alive():
                threads_alive.append("PositionMonitoring")

            if threads_alive:
                logger.warning(
                    f"Threads ainda em execução após timeout: {', '.join(threads_alive)}"
                )

            self.status = "stopped"
            self.last_update = datetime.now(timezone.utc)
            logger.info("Motor de trading parado com sucesso")

            return True

        except Exception as e:
            logger.error(f"Erro ao parar motor de trading: {e}")
            self.status = "error"
            self.error_message = str(e)
            return False

    def get_status(self) -> Dict[str, Any]:
        """
        Obtém o status atual do motor de trading.

        Returns:
            Dicionário com informações de status
        """
        return {
            "status": self.status,
            "last_update": self.last_update.isoformat(),
            "error_message": self.error_message,
            "api_configured": self.exchange is not None,
            "live_mode": self.live_mode,
            "symbols": self.symbols,
            "risk_profile": self.risk_profile,
            "performance_metrics": self.performance_metrics,
            "quantum_metrics": self.quantum_metrics,
        }

    def get_market_data(self) -> Dict[str, Any]:
        """
        Obtém os dados de mercado atuais.

        Returns:
            Dicionário com dados de mercado por símbolo e timeframe
        """
        return self.market_data

    def get_positions(self) -> List[Position]:
        """
        Obtém as posições abertas atuais.

        Returns:
            Lista de posições abertas
        """
        return self.positions

    def get_trade_history(self) -> List[Dict[str, Any]]:
        """
        Obtém o histórico de trades.

        Returns:
            Lista com histórico de trades
        """
        return self.trade_history

    def _initialize_qualia_advanced_components(self) -> None:
        """
        Inicializa os componentes avançados de integração com o QUALIA Universe.

        Este método configura:
        1. Canal bidirecional para comunicação com o QUALIA Universe
        2. Controlador de posições baseado em risco quântico
        3. Estratégias evolucionárias baseadas no ciclo QAST
        4. Detector de padrões quânticos multiescala
        5. Sistema de trading auto-adaptativo
        """
        try:
            # 1. Inicializar canal bidirecional
            if QUALIATradingChannel is not None:
                self.qualia_trading_channel = QUALIATradingChannel(
                    trading_engine=self, qualia_universe=self.quantum_universe
                )
                logger.info("Canal Bidirecional QUALIA inicializado com sucesso")
            else:
                self.qualia_trading_channel = None
                logger.warning(
                    "QUALIATradingChannel não pôde ser importado. Funcionalidade do canal bidirecional estará desabilitada."
                )

            # 2. Verificar se controlador de posições baseado em risco quântico
            # já foi inicializado
            if (
                not hasattr(self, "quantum_risk_position_controller")
                or not self.quantum_risk_position_controller
            ):
                self.quantum_risk_position_controller = QuantumRiskPositionController(
                    risk_analyzer=self.quantum_risk_analyzer,
                    capital_allocation=self.initial_capital,
                    max_risk_per_trade=(
                        0.02
                        if self.risk_profile == "conservative"
                        else (0.03 if self.risk_profile == "balanced" else 0.05)
                    ),
                )
                risk_logger.info(
                    "Controlador de Posições baseado em Risco Quântico inicializado"
                )

            # 3. Inicializar estratégias evolucionárias baseadas no ciclo QAST
            if hasattr(self.strategy, "get_base_strategy"):
                base_strategy = self.strategy.get_base_strategy()
                self.qast_evolutionary_strategy = QASTEvolutionaryStrategy(
                    strategy_template=base_strategy,
                    qualia_consciousness=(
                        self.quantum_universe.consciousness
                        if hasattr(self.quantum_universe, "consciousness")
                        else None
                    ),
                    population_size=32,
                )
                logger.info("Estratégia Evolucionária baseada em QAST inicializada")

                # Inicializar população inicial de estratégias
                self.qast_evolutionary_strategy.initialize_population()
                logger.info("População inicial de estratégias criada (10 variantes)")
            else:
                logger.warning(
                    "Não foi possível inicializar Estratégia Evolucionária QAST: strategy não fornece método get_base_strategy()"
                )

            # 4. Inicializar detector de padrões quânticos multiescala
            self.qualia_market_pattern_detector = QUALIAMarketPatternDetector(
                quantum_metrics_calculator=self.quantum_metrics_calculator,  # YAA: Corrigido
                timeframes=self.timeframes,
            )
            logger.info(
                f"Detector de Padrões Quânticos Multiescala inicializado com {len(self.timeframes)} timeframes"
            )

            # 5. Inicializar sistema de trading auto-adaptativo
            self.self_evolving_trading_system = SelfEvolvingTradingSystem(
                trading_engine=self, qualia_universe=self.quantum_universe
            )
            logger.info("Sistema de Trading Auto-Adaptativo via QAST inicializado")

            # 6. Inicializar sistema de metacognição quântica para trading
            self.metacognition_system = QUALIAMetacognitionTrading(
                qualia_universe=self.quantum_universe,
                trading_engine=self,
                max_history_size=1000,
            )
            logger.info("Sistema de Metacognição Quântica para Trading inicializado")

            # 7. Inicializar sistema de adaptação dinâmica de consciência
            # quântica
            self.adaptive_consciousness = AdaptiveConsciousnessEvolution(
                qualia_universe=self.quantum_universe
            )
            logger.info(
                "Sistema de Adaptação Dinâmica de Consciência Quântica inicializado"
            )
            risk_logger.info(
                "Sistema adaptativo de escalabilidade baseada em complexidade de mercado ativado"
            )

            # Registrar no log
            logger.info(
                "Todos os componentes avançados QUALIA inicializados com sucesso"
            )
            risk_logger.info(
                "Sistema de trading neural-quântico completo ativado com capacidade metacognitiva"
            )

        except Exception as e:
            logger.error(f"Erro ao inicializar componentes avançados QUALIA: {e}")
            logger.debug(traceback.format_exc())

            # Continuar mesmo com erro, apenas registrar
            risk_logger.warning(
                "Alguns componentes avançados QUALIA não puderam ser inicializados. Sistema operando em modo de capacidade reduzida."
            )

    def _data_collection_loop(self) -> None:
        """
        Loop contínuo para coleta de dados de mercado.
        """
        logger.info("Iniciando thread de coleta de dados")

        while not self.stop_event.is_set():
            try:
                # Para cada símbolo, buscar dados mais recentes
                for symbol in self.symbols:
                    # Buscar apenas o timeframe de 1 minuto (os outros serão
                    # derivados)
                    try:
                        data = self.exchange.fetch_symbol_price_data(symbol, "1m", 100)
                        if data and len(data.get("close", [])) > 0:
                            # Atualizar dados de mercado
                            self.market_data[symbol]["1m"] = data

                            # Colocar na fila para processamento
                            self.data_queue.put(
                                {
                                    "symbol": symbol,
                                    "timeframe": "1m",
                                    "data": data,
                                    "timestamp": datetime.now(timezone.utc).isoformat(),
                                },
                                block=False,
                            )

                    except queue.Full:
                        logger.warning(
                            f"Fila de dados cheia, ignorando atualização para {symbol}"
                        )
                    except Exception as e:
                        logger.error(f"Erro ao buscar dados para {symbol}: {e}")

                # Atualizar tempo da última atualização
                self.last_update = datetime.now(timezone.utc)

                # Aguardar próximo ciclo respeitando o sinal de parada
                self._wait_for_stop(self.data_interval)

            except Exception as e:
                logger.error(f"Erro no loop de coleta de dados: {e}")
                logger.debug(traceback.format_exc())

                # Aguardar um pouco antes de tentar novamente
                self._wait_for_stop(5)

        logger.info("Thread de coleta de dados encerrada")

    def _generate_quantum_enhanced_signal(
        self, symbol: str, symbol_data: Dict[str, Dict[str, Any]]
    ) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """
        Gera sinais de trading aprimorados com análise quântica.

        Esta função realiza uma análise técnica tradicional e a aprimora
        com métricas quânticas do framework QUALIA para melhorar a precisão
        dos sinais de trading e a identificação de padrões ocultos nos dados
        de mercado.

        Args:
            symbol: Símbolo do ativo
            symbol_data: Dados de mercado para múltiplos timeframes

        Returns:
            Tupla contendo (sinal gerado, métricas quânticas)
        """
        # Inicializar
        quantum_metrics = None
        quantum_score = None

        # Realizar análise quântica (se habilitada)
        if self.use_quantum_analysis and self.quantum_universe is not None:
            try:
                # Verificar se temos sistema adaptativo ativo e configurações
                # aplicadas
                adaptive_config = None
                if (
                    hasattr(self, "adaptive_consciousness")
                    and self.adaptive_consciousness
                ):
                    if (
                        "quantum_adaptations" in self.quantum_metrics
                        and self.quantum_metrics["quantum_adaptations"]
                    ):
                        # Última configuração
                        adaptive_config = self.quantum_metrics["quantum_adaptations"][
                            -1
                        ]["config"]
                        logger.debug(
                            f"Usando configuração adaptativa para análise de {symbol}: qubits={adaptive_config.get('n_qubits', '?')}"
                        )

                # Executar análise quântica completa
                quantum_metrics = self._perform_quantum_analysis(symbol, symbol_data)

                if quantum_metrics and len(quantum_metrics) > 0:
                    # Calcular um score quântico que representa a "qualidade" do sinal
                    # baseado em métricas quânticas específicas do QUALIA
                    quantum_score = self._calculate_quantum_signal_score(
                        quantum_metrics
                    )
                    logger.info(f"Score quântico para {symbol}: {quantum_score:.4f}")
            except Exception as e:
                logger.error(f"Erro na análise quântica para sinais de {symbol}: {e}")
                logger.debug(traceback.format_exc())

        # Gerar sinal de trading usando estratégia padrão
        try:
            main_tf = "1m" if "1m" in symbol_data else list(symbol_data.keys())[0]
            df_main = TradingEngine._ohlcv_dict_to_df(symbol_data[main_tf])

            action, base_conf = self.strategy.generate_signal(df_main, quantum_metrics)

            if isinstance(action, str):
                action = action.lower()

            signal = {
                "action": action,
                "confidence": base_conf,
                "reasons": [],
                "price": df_main["close"].iloc[-1] if not df_main.empty else np.nan,
            }

            # Normalize action for consistent downstream processing
            signal["action"] = str(signal.get("action", "")).lower()

            # Se temos métricas quânticas, aprimorar o sinal
            if quantum_metrics and quantum_score is not None:
                # Ajustar confiança do sinal com base no score quântico
                if "confidence" in signal:
                    # Ponderar: 70% estratégia tradicional, 30% análise
                    # quântica
                    original_confidence = signal["confidence"]
                    quantum_adjusted = original_confidence * 0.7 + quantum_score * 0.3

                    # Atualizar confiança
                    signal["confidence"] = quantum_adjusted
                    signal["original_confidence"] = original_confidence
                    signal["quantum_score"] = quantum_score

                    # Adicionar informações de adaptação se disponíveis
                    if adaptive_config:
                        if "adaptive_info" not in signal:
                            signal["adaptive_info"] = {}
                        signal["adaptive_info"].update(
                            {
                                "n_qubits": adaptive_config.get("n_qubits", None),
                                "circuit_depth": adaptive_config.get("scr_depth", None),
                                "entanglement": adaptive_config.get(
                                    "entanglement", None
                                ),
                            }
                        )

                # Possivelmente inverter sinais fracos se métricas quânticas indicarem
                # forte reversão de tendência
                action_normalized = signal.get("action", "").lower()
                if action_normalized in ["buy", "sell"]:
                    # Verificar se existe indicador de reversão nas métricas
                    # quânticas
                    if (
                        quantum_metrics.get("reversal_prediction", 0) > 0.7
                        and quantum_metrics.get("quantum_confidence", 0) > 0.6
                    ):
                        # Forte indicação de possível reversão
                        if (
                            signal["confidence"] < 0.55
                        ):  # Sinais fracos podem ser invertidos
                            # Inverter sinal
                            signal["action"] = (
                                "sell" if action_normalized == "buy" else "buy"
                            )
                            action_normalized = signal["action"]
                            signal["confidence"] = max(
                                0.55, quantum_metrics.get("quantum_confidence", 0) * 0.8
                            )
                            signal["reasons"].append(
                                "Invertido por forte indicação quântica de reversão"
                            )
                else:
                    logger.warning(
                        f"Ação não reconhecida '{signal.get('action')}' para {symbol}"
                    )

                # Aprimorar sinal com padrões detectados, se disponíveis
                if (
                    "detected_patterns" in quantum_metrics
                    and quantum_metrics["detected_patterns"]
                ):
                    patterns = quantum_metrics["detected_patterns"]

                    # Identificar padrões de alta confiança
                    high_confidence_patterns = [
                        p for p in patterns if p.get("confidence", 0) > 0.7
                    ]

                    if (
                        high_confidence_patterns
                        and "reasons" in signal
                        and isinstance(signal["reasons"], list)
                    ):
                        # Adicionar padrões importantes ao sinal
                        # Limitar a 2 padrões principais
                        for pattern in high_confidence_patterns[:2]:
                            signal["reasons"].append(
                                f"Padrão quântico detectado: {pattern.get('name', 'Desconhecido')} "
                                f"(confiança: {pattern.get('confidence', 0):.2f})"
                            )

                # Incorporar sinais de trading baseados em padrões
                if (
                    "pattern_signals" in quantum_metrics
                    and quantum_metrics["pattern_signals"]
                ):
                    pattern_signals = quantum_metrics["pattern_signals"]

                    # Verificar se há sinais de padrões de alta confiança
                    for signal_type, pattern_data in pattern_signals.items():
                        if pattern_data.get("confidence", 0) > 0.7:
                            # Adicionar informação do padrão detectado
                            if "reasons" in signal and isinstance(
                                signal["reasons"], list
                            ):
                                signal["reasons"].append(
                                    f"Sinal de {signal_type} detectado "
                                    f"(confiança: {pattern_data.get('confidence', 0):.2f})"
                                )

                            # Fortalecer confiança do sinal quando alinhado com
                            # padrões
                            action_normalized = signal.get("action", "").lower()
                            if (
                                action_normalized == "buy"
                                and signal_type
                                in ["bullish", "reversal_up", "breakout"]
                            ) or (
                                action_normalized == "sell"
                                and signal_type
                                in ["bearish", "reversal_down", "breakdown"]
                            ):
                                signal["confidence"] = min(
                                    0.95, signal["confidence"] * 1.15
                                )
                                if "enhancement_factors" not in signal:
                                    signal["enhancement_factors"] = []
                                signal["enhancement_factors"].append(
                                    {
                                        "type": "pattern_reinforcement",
                                        "pattern": signal_type,
                                        "confidence": pattern_data.get("confidence", 0),
                                    }
                                )

                # Aplicar resultados do ciclo QAST de estratégias
                # evolucionárias, se disponíveis
                if hasattr(self, "qast_evolutionary_strategy"):
                    try:
                        # Obter a melhor estratégia evoluída pelo ciclo QAST
                        best_strategy = (
                            self.qast_evolutionary_strategy.get_best_strategy()
                        )

                        if best_strategy:
                            # Verificar se a estratégia tem recomendações para
                            # este símbolo
                            if hasattr(best_strategy, "get_recommendation_for_symbol"):
                                qast_recommendation = (
                                    best_strategy.get_recommendation_for_symbol(
                                        symbol, quantum_metrics
                                    )
                                )

                                if qast_recommendation and isinstance(
                                    qast_recommendation, dict
                                ):
                                    # Adicionar informações do QAST às razões
                                    if "reasons" in signal and isinstance(
                                        signal["reasons"], list
                                    ):
                                        if "description" in qast_recommendation:
                                            signal["reasons"].append(
                                                f"QAST: {qast_recommendation['description']}"
                                            )
                                        else:
                                            signal["reasons"].append(
                                                "Adaptado pelo ciclo QAST"
                                            )

                                    # Possivelmente ajustar ação com base na
                                    # recomendação do QAST
                                    if (
                                        "action" in qast_recommendation
                                        and qast_recommendation.get("confidence", 0)
                                        > 0.65
                                    ):
                                        qast_action = qast_recommendation[
                                            "action"
                                        ].lower()
                                        qast_confidence = qast_recommendation.get(
                                            "confidence", 0
                                        )

                                        if qast_action != signal.get(
                                            "action"
                                        ) and qast_confidence > signal.get(
                                            "confidence", 0
                                        ):
                                            # QAST está sugerindo ação
                                            # diferente com maior confiança
                                            signal["action"] = qast_action
                                            signal["confidence"] = qast_confidence
                                            signal["qast_override"] = True

                                            if "reasons" in signal:
                                                signal["reasons"].append(
                                                    f"Ação alterada por estratégia QAST (confiança: {qast_confidence:.2f})"
                                                )
                    except Exception as e:
                        logger.error(
                            f"Erro ao aplicar recomendações QAST para {symbol}: {e}"
                        )

                # Adicionar razões específicas de quantum ao sinal
                if "reasons" in signal and isinstance(signal["reasons"], list):
                    # Adicionar explicações sobre a contribuição quântica
                    entropy = quantum_metrics.get("entropy", 0)
                    coherence = quantum_metrics.get("coherence", 0)

                    # Métricas avançadas (se disponíveis)
                    if "quantum_metrics" in quantum_metrics:
                        qm = quantum_metrics["quantum_metrics"]

                        # Reportar instabilidade quântica
                        instability = qm.get("quantum_instability", 0)
                        if instability > 0.6:
                            signal["reasons"].append(
                                f"Alta instabilidade quântica ({instability:.2f}) - Mercado potencialmente volátil"
                            )

                        # Reportar probabilidade de eventos extremos
                        extreme_prob = qm.get("extreme_event_probability", 0)
                        if extreme_prob > 0.1:
                            signal["reasons"].append(
                                f"Probabilidade de eventos extremos: {extreme_prob:.1%}"
                            )

                        # Reportar confiança quântica
                        q_confidence = qm.get("quantum_confidence", 0)
                        signal["reasons"].append(
                            f"Confiança quântica: {q_confidence:.2f}"
                        )
                    else:
                        # Métricas básicas
                        signal["reasons"].append(
                            f"QUALIA Entropia={entropy:.2f}, Coerência={coherence:.2f}"
                        )

                # Adicionar flag de sinal aprimorado com quantum
                signal["quantum_enhanced"] = True

            return signal, quantum_metrics

        except Exception as e:
            logger.error(f"Erro ao gerar sinal para {symbol}: {e}")
            logger.debug(traceback.format_exc())
            return {}, quantum_metrics

    def _calculate_quantum_signal_score(self, quantum_metrics: Dict[str, Any]) -> float:
        """
        Calcula um score para a qualidade do sinal de trading com base nas métricas quânticas.

        Este score representa a confiança que podemos ter no sinal com base na análise
        quântica do QUALIA, considerando múltiplos fatores como entropia, coerência,
        informação mútua, estabilidade do sistema e correlações adicionais.

        Os pesos padrões dos componentes são:

        ``[0.15, 0.12, 0.15, 0.12, 0.15, 0.10, 0.08, 0.08, 0.05]``

        Correspondendo a
        ``[coherence, entropy, stability, mutual_information, quantum_confidence,
        direction_strength, otoc, informational_mass, fidelity_to_initial,
        lambda_gravity]``.

        Args:
            quantum_metrics: Métricas quânticas do QUALIA

        Returns:
            Score entre 0 e 1 representando a qualidade do sinal
        """
        score = 0.5  # Valor neutro/padrão

        try:
            # Verificar se temos métricas essenciais
            if not quantum_metrics:
                return score

            # Componentes básicos (sempre devem estar presentes)
            entropy = quantum_metrics.get("entropy", 0.5)
            coherence = quantum_metrics.get("coherence", 0.5)

            # Componentes avançados (podem estar em um subnível)
            metrics_dict = quantum_metrics.get("quantum_metrics", {})

            # Extrair métricas relevantes para score de sinal
            quantum_confidence = metrics_dict.get("quantum_confidence", 0.5)
            mutual_information = metrics_dict.get("mutual_information", 0.0)
            quantum_direction = metrics_dict.get("quantum_direction", 0.0)
            quantum_instability = metrics_dict.get("quantum_instability", 0.5)
            otoc = quantum_metrics.get("otoc", 0.5)
            informational_mass = quantum_metrics.get("informational_mass", 0.5)
            fidelity_to_initial = quantum_metrics.get("fidelity_to_initial", 0.5)
            lambda_gravity = quantum_metrics.get("lambda_gravity")

            # Componentes base do score (ponderação heurística baseada em
            # testes empíricos)

            # 1. Coerência alta geralmente indica sinal mais claro
            coherence_component = coherence

            # 2. Entropia moderada (nem muito alta nem muito baixa) é ideal
            # Muito baixa: sistema preso em mínimo local
            # Muito alta: caos, imprevisibilidade
            # Valor ideal em torno de 0.5
            entropy_component = 1.0 - abs(0.5 - entropy) * 2.0

            # 3. Instabilidade baixa favorece sinais mais confiáveis
            stability_component = 1.0 - quantum_instability

            # 4. Informação mútua alta indica correlações quânticas
            # significativas
            information_component = (
                mutual_information if mutual_information > 0 else 0.1
            )

            # 5. Confiança quântica (se disponível)
            confidence_component = quantum_confidence

            # 6. Força direcional quântica (valor absoluto)
            direction_strength = (
                abs(quantum_direction) if quantum_direction is not None else 0.1
            )

            # 7. Baixo OTOC indica menor scrambling (mais previsibilidade)
            otoc_component = 1.0 - otoc

            # 8. Massa informacional adequada indica boa quantidade de dados
            mass_component = informational_mass if informational_mass >= 0 else 0.0

            # 9. Fidelidade ao estado inicial (Loschmidt Echo)
            fidelity_component = fidelity_to_initial

            # 10. Nível de ruído (lambda_gravity mais alto reduz confiança)
            if lambda_gravity is not None:
                lambda_component = max(0.0, 1.0 - min(1.0, float(lambda_gravity)))
            else:
                lambda_component = 0.5

            # Calcular score final - média ponderada dos componentes
            weights = [0.14, 0.11, 0.14, 0.11, 0.14, 0.09, 0.07, 0.07, 0.05, 0.08]
            components = [
                coherence_component,
                entropy_component,
                stability_component,
                information_component,
                confidence_component,
                direction_strength,
                otoc_component,
                mass_component,
                fidelity_component,
                lambda_component,
            ]

            # Calcular score ponderado
            weighted_sum = sum(w * c for w, c in zip(weights, components))
            score = min(1.0, max(0.0, weighted_sum))

            # Normalizar para garantir faixa 0.1-0.9 (evitar extremos)
            score = 0.1 + score * 0.8

            # Registrar componentes no log para diagnóstico
            logger.debug(
                "Componentes do score quântico: "
                + f"Coerência={coherence_component:.2f}, "
                + f"Entropia={entropy_component:.2f}, "
                + f"Estabilidade={stability_component:.2f}, "
                + f"InfoMútua={information_component:.2f}, "
                + f"Confiança={confidence_component:.2f}, "
                + f"DireçãoAbs={direction_strength:.2f}, "
                + f"OTOC={otoc_component:.2f}, "
                + f"MassaInfo={mass_component:.2f}, "
                + f"Fidelidade={fidelity_component:.2f}, "
                + f"Lambda={lambda_component:.2f}"
            )

        except Exception as e:
            logger.error(f"Erro ao calcular score quântico de sinal: {e}")
            logger.debug(traceback.format_exc())

        return score

    def _analysis_loop(self) -> None:
        """
        Loop contínuo para análise de dados e geração de sinais de trading.

        Esta função monitora continuamente os dados de mercado, realizando
        análise técnica tradicional e quântica para gerar sinais de trading
        de alta precisão.
        """
        logger.info("Iniciando thread de análise de mercado")

        # Contador para análise periódica de risco
        risk_analysis_counter = 0
        # Contador para ciclos de análise (usado para metacognição periódica)
        analysis_cycles = 0

        while not self.stop_event.is_set():
            try:
                # Para cada símbolo, realizar análise completa
                for symbol in self.symbols:
                    try:
                        # Consolidar dados de todos os timeframes
                        symbol_data = {}
                        for tf in self.timeframes:
                            if tf in self.market_data[symbol]:
                                symbol_data[tf] = self.market_data[symbol][tf]

                        if not symbol_data:
                            logger.warning(
                                f"Sem dados suficientes para analisar {symbol}"
                            )
                            continue

                        # Gerar sinal aprimorado com análise quântica
                        (
                            signal,
                            quantum_metrics,
                        ) = self._generate_quantum_enhanced_signal(symbol, symbol_data)

                        # Se sinal gerado for 'buy' ou 'sell', colocar na fila
                        # de sinais
                        action_normalized = (
                            signal.get("action", "").lower() if signal else ""
                        )
                        if signal and action_normalized in ["buy", "sell"]:
                            signal["action"] = action_normalized
                            # Adicionar métricas quânticas completas ao sinal
                            # para referência
                            signal["quantum_metrics_full"] = quantum_metrics

                            # Registrar análise e razões para o sinal
                            reasons_text = ", ".join(
                                signal.get("reasons", ["Sem razões específicas"])
                            )
                            logger.info(
                                f"Análise de {symbol}: Ação={signal['action']}, Confiança={signal['confidence']:.2f}"
                            )
                            logger.info(f"Razões: {reasons_text}")

                            # Verificar estado atual de risco
                            if "risk" in self.quantum_metrics:
                                risk_level = self.quantum_metrics["risk"].get(
                                    "risk_level", 0
                                )
                                risk_category = self.quantum_metrics["risk"].get(
                                    "risk_category", "DESCONHECIDO"
                                )

                                # Ajustar confiança com base no nível de risco
                                if risk_level > 75:  # Risco ALTO ou EXTREMO
                                    # Reduzir confiança significativamente em
                                    # ambiente de alto risco
                                    signal["confidence"] *= 0.6
                                    signal["reasons"].append(
                                        f"Redução por risco {risk_category} ({risk_level:.0f}/100)"
                                    )
                                    logger.warning(
                                        f"Sinal para {symbol} com confiança reduzida devido a risco {risk_category}"
                                    )
                                elif risk_level > 50:  # Risco ELEVADO
                                    # Reduzir confiança moderadamente
                                    signal["confidence"] *= 0.8
                                    signal["reasons"].append(
                                        f"Ajuste por risco {risk_category} ({risk_level:.0f}/100)"
                                    )

                            # Adicionar dados de mercado resumidos para
                            # referência na execução
                            if "market_state" in self.quantum_metrics.get("risk", {}):
                                # Extrair volatilidade específica do símbolo
                                market_state = self.quantum_metrics["risk"][
                                    "market_state"
                                ]
                                if (
                                    "volatilities" in market_state
                                    and symbol in market_state["volatilities"]
                                ):
                                    signal["symbol_volatility"] = market_state[
                                        "volatilities"
                                    ][symbol]

                            # Colocar na fila para execução somente se confiança acima do mínimo
                            # após todos os ajustes
                            if signal["confidence"] >= 0.5:
                                self.signal_queue.put(
                                    {
                                        "symbol": symbol,
                                        "signal": signal,
                                        "timestamp": datetime.now(
                                            timezone.utc
                                        ).isoformat(),
                                    },
                                    block=False,
                                )

                                logger.info(
                                    f"Sinal gerado: {symbol} {signal['action']}, confiança final: {signal['confidence']:.2f}"
                                )
                            else:
                                logger.info(
                                    f"Sinal descartado para {symbol}: confiança final ({signal['confidence']:.2f}) abaixo do mínimo"
                                )
                        else:
                            logger.warning(
                                f"Ação não reconhecida '{signal.get('action')}' para {symbol}"
                            )

                    except queue.Full:
                        logger.warning(
                            f"Fila de sinais cheia, ignorando sinal para {symbol}"
                        )
                    except Exception as e:
                        logger.error(f"Erro ao analisar {symbol}: {e}")
                        logger.debug(traceback.format_exc())

                # Realizar análise de risco periódica (a cada 5 ciclos)
                risk_analysis_counter += 1
                analysis_cycles += 1
                if risk_analysis_counter >= 5:
                    # Calcular métricas de risco atualizadas
                    try:
                        risk_metrics = self._calculate_quantum_risk_metrics()
                        risk_level = risk_metrics.get("risk_level", 50)
                        risk_category = risk_metrics.get("risk_category", "MODERADO")

                        logger.info(
                            f"Análise de risco atualizada: {risk_category} ({risk_level:.0f}/100)"
                        )

                        # Verificar se há alertas críticos que exigem atenção
                        # imediata
                        if risk_metrics.get("extreme_risk_alert", False):
                            logger.warning("ALERTA CRÍTICO DE RISCO DETECTADO")
                            if "risk_alerts" in risk_metrics:
                                for alert in risk_metrics["risk_alerts"]:
                                    if alert.get("severity") == "high":
                                        logger.warning(
                                            f"ALERTA: {alert.get('message')}"
                                        )

                        # Usar o sistema de adaptação dinâmica para reconfigurar o universo quântico
                        # baseado na análise de complexidade do mercado atual
                        if (
                            self.use_quantum_analysis
                            and self.adaptive_consciousness
                            and self.quantum_universe
                        ):
                            # Criar um dicionário consolidado com dados de
                            # mercado de todos os símbolos
                            consolidated_market_data = {}
                            try:
                                # Consolidar dados de mercado de todos os
                                # símbolos para análise de complexidade
                                for symbol in self.symbols:
                                    if "1h" in self.market_data[symbol]:
                                        consolidated_market_data[symbol] = (
                                            self.market_data[symbol]["1h"]
                                        )

                                if consolidated_market_data:
                                    # Selecionar o primeiro símbolo como
                                    # referência principal (tipicamente BTC)
                                    main_symbol = (
                                        self.symbols[0] if self.symbols else None
                                    )

                                    if (
                                        main_symbol
                                        and main_symbol in consolidated_market_data
                                    ):
                                        # Chamar o sistema de adaptação
                                        # dinâmica para reconfigurar o universo
                                        # quântico
                                        logger.info(
                                            f"Adaptando complexidade do universo quântico baseado na análise de mercado de {main_symbol}"
                                        )

                                        # Usar os dados de mercado para a
                                        # adaptação
                                        new_config = self.adaptive_consciousness.reconfigure_quantum_universe(
                                            consolidated_market_data[main_symbol]
                                        )

                                        if new_config:
                                            logger.info(
                                                f"QUALIA Universe reconfigurado com: qubits={new_config.get('n_qubits', '?')}, "
                                                f"profundidade={new_config.get('scr_depth', '?')}, "
                                                f"entrelaçamento={new_config.get('entanglement', '?')}"
                                            )

                                            # Adicionar configuração às
                                            # métricas de risco para
                                            # rastreamento
                                            if (
                                                "quantum_adaptations"
                                                not in self.quantum_metrics
                                            ):
                                                self.quantum_metrics[
                                                    "quantum_adaptations"
                                                ] = []

                                            # Armazenar histórico das
                                            # adaptações
                                            self.quantum_metrics[
                                                "quantum_adaptations"
                                            ].append(
                                                {
                                                    "timestamp": datetime.now(
                                                        timezone.utc
                                                    ).isoformat(),
                                                    "config": new_config,
                                                    "market_state": {
                                                        "risk_level": risk_level,
                                                        "risk_category": risk_category,
                                                    },
                                                }
                                            )
                            except Exception as e:
                                logger.error(
                                    f"Erro durante adaptação dinâmica do universo quântico: {e}"
                                )
                                logger.debug(traceback.format_exc())
                    except Exception as e:
                        logger.error(
                            f"Erro ao realizar análise periódica de risco: {e}"
                        )

                    # Executar ciclo metacognitivo para análise e
                    # auto-aprimoramento
                    if (
                        hasattr(self, "metacognition_system")
                        and self.metacognition_system
                    ):
                        try:
                            logger.info(
                                "Iniciando ciclo de metacognição quântica para trading..."
                            )

                            # Executar análise metacognitiva (em thread
                            # separada para não bloquear o sistema)
                            metacognition_thread = threading.Thread(
                                target=self._perform_metacognition_cycle,
                                name="MetacognitionCycle",
                            )
                            metacognition_thread.daemon = True
                            metacognition_thread.start()
                            logger.info("Thread de metacognição quântica iniciada")

                        except Exception as e:
                            logger.error(f"Erro ao iniciar ciclo de metacognição: {e}")

                    # Resetar contador
                    risk_analysis_counter = 0

                    # Executar evolução de estratégias via QAST a cada 5 ciclos
                    # de análise de risco
                    if (
                        hasattr(self, "qast_evolutionary_strategy")
                        and self.qast_evolutionary_strategy
                    ):
                        logger.info("Verificando necessidade de evolução QAST...")
                        # Executar evolução de estratégias em thread separada
                        # para não bloquear análise
                        evolution_thread = threading.Thread(
                            target=self._evolve_trading_strategies, name="QASTEvolution"
                        )
                        evolution_thread.daemon = True
                        evolution_thread.start()
                        logger.info("Thread de evolução QAST iniciada")

                    # Executar ciclo de metacognição periódico (a cada 3 ciclos
                    # de análise)
                    if (
                        analysis_cycles % 3 == 0
                        and hasattr(self, "metacognition_system")
                        and self.metacognition_system
                    ):
                        logger.info("Iniciando ciclo de metacognição periódico...")
                        # Executar metacognição em thread separada para não
                        # bloquear análise
                        metacognition_thread = threading.Thread(
                            target=self._perform_metacognition_cycle,
                            name="MetacognitionCycle",
                        )
                        metacognition_thread.daemon = True
                        metacognition_thread.start()
                        logger.info("Thread de metacognição iniciada")

                # Aguardar próximo ciclo respeitando o sinal de parada
                self._wait_for_stop(self.analysis_interval)

            except Exception as e:
                logger.error(f"Erro no loop de análise: {e}")
                logger.debug(traceback.format_exc())
                self._wait_for_stop(5)

        logger.info("Thread de análise encerrada")

    def _perform_quantum_analysis(
        self, symbol: str, market_data: Dict[str, Any]
    ) -> Dict[str, float]:
        """
        Realiza análise quântica dos dados de mercado usando o framework QUALIA.

        Esta implementação agora utiliza o QuantumMetricsCalculator que se integra
        diretamente com o QUALIA Universe e ciclo QAST para análises avançadas.

        Args:
            symbol: Símbolo a ser analisado
            market_data: Dados de mercado por timeframe

        Returns:
            Dicionário com métricas quânticas
        """
        try:
            # Verificar se análise quântica está disponível
            if not self.use_quantum_analysis:
                logger.info(f"Análise quântica desabilitada para {symbol}")
                return {
                    "entropy": 0.5,
                    "coherence": 0.5,
                    "active_states": 0,
                    "quantum_enabled": False,
                }

            # Formatar dados para o calculador de métricas quânticas
            formatted_data = {symbol: {}}

            # Primeiro determinar quais timeframes estão disponíveis
            available_timeframes = []
            for tf in ["1m", "5m", "15m", "1h"]:
                if tf in market_data and len(market_data[tf].get("close", [])) > 5:
                    available_timeframes.append(tf)
                    formatted_data[symbol][tf] = market_data[tf]

            if not available_timeframes:
                logger.warning(
                    f"Sem dados disponíveis para análise quântica de {symbol}"
                )
                return {
                    "entropy": 0.5,
                    "coherence": 0.5,
                    "active_states": 0,
                    "error": "Sem dados suficientes",
                }

            # Usar o calculador de métricas quânticas avançado
            try:
                # Calcular volatilidade para definir nível térmico apropriado
                shortest_tf = min(available_timeframes)
                # Últimos 30 preços
                price_data = market_data[shortest_tf]["close"][-30:]

                price_array = np.asarray(price_data, dtype=float)
                recent_returns = np.diff(price_array) / price_array[:-1]
                volatility = np.std(recent_returns) if recent_returns.size > 0 else 0.01

                # Definir nível térmico com base na volatilidade
                thermal_level = min(0.3, max(0.05, volatility * 10))

                # Configurar características do calculador quântico
                # YAA: Adicionar verificação para self.quantum_metrics_calculator
                if not self.quantum_metrics_calculator:
                    logger.warning(
                        f"QuantumMetricsCalculator não está inicializado. Análise quântica para {symbol} será limitada."
                    )
                    return {
                        "entropy": 0.5,
                        "coherence": 0.5,
                        "active_states": 0,
                        "error": "QuantumMetricsCalculator não inicializado",
                        "quantum_enabled": False,
                    }

                self.quantum_metrics_calculator.set_thermal_coefficient(
                    thermal_level
                )  # YAA: Corrigido

                # Calcular métricas quânticas usando o novo calculador
                metrics_result = (
                    self.quantum_metrics_calculator.calculate_metrics(  # YAA: Corrigido
                        formatted_data
                    )
                )

                # Extrair e formatar métricas
                metrics = {}

                # Copiar todas as métricas da resposta
                for key, value in metrics_result.items():
                    if key not in [
                        "timestamp",
                        "n_qubits",
                        "thermal_coefficient",
                        "execution_time",
                    ]:
                        # Converter listas para valores médios quando
                        # necessário
                        if isinstance(value, list):
                            if len(value) > 0:
                                try:
                                    # Para valores numéricos, calcular média
                                    metrics[key] = float(np.mean(value))
                                except BaseException as exc:
                                    logger.exception(
                                        "Erro ao calcular média para a métrica %s: %s",
                                        key,
                                        exc,
                                    )
                                    # Para valores não numéricos, usar o mais
                                    # recente
                                    metrics[key] = value[-1]
                            else:
                                metrics[key] = 0
                        else:
                            metrics[key] = value

                # Descompactar métricas finais do QUALIA se disponíveis
                if "final_metrics" in metrics and isinstance(
                    metrics["final_metrics"], dict
                ):
                    fm = metrics.pop("final_metrics")
                    if "fidelity_to_initial" in fm:
                        metrics["fidelity_to_initial"] = float(
                            fm["fidelity_to_initial"]
                        )
                    if "otoc_calculated" in fm:
                        metrics["otoc"] = float(fm["otoc_calculated"])
                    if "informational_mass" in fm:
                        val = fm["informational_mass"]
                        if isinstance(val, list):
                            metrics["informational_mass"] = (
                                float(np.mean(val)) if val else 0.0
                            )
                        else:
                            metrics["informational_mass"] = val

                # Calcular momentum para métricas derivadas
                if len(recent_returns) >= 5:
                    momentum = np.mean(recent_returns[-5:]) / (
                        np.std(recent_returns[-5:]) + 1e-6
                    )
                else:
                    momentum = 0

                # Adicionar métricas clássicas importantes
                metrics["volatility"] = float(volatility)
                metrics["momentum"] = float(momentum)

                # Derivar índices quânticos compostos se não estiverem
                # presentes:

                # 1. Índice de Instabilidade Quântica (0-1): combina entropia e
                # queda de coerência
                if "quantum_instability" not in metrics:
                    entropy = metrics.get("entropy", 0.5)
                    coherence = metrics.get("coherence", 0.5)
                    metrics["quantum_instability"] = float(entropy * (1 - coherence))

                # 2. Índice de Predição de Reversão (0-1): entropia alta +
                # informação mútua alta
                if "reversal_prediction" not in metrics:
                    entropy = metrics.get("entropy", 0.5)
                    mi = metrics.get(
                        "mutual_information", metrics.get("reflection_result", 0.1)
                    )
                    metrics["reversal_prediction"] = float(entropy * mi)

                # 3. Índice de Confiança Quântica (0-1): qualidade da previsão
                if "quantum_confidence" not in metrics:
                    coherence = metrics.get("coherence", 0.5)
                    metrics["quantum_confidence"] = float(
                        0.7 * coherence
                        + 0.3 * (1 - metrics.get("quantum_instability", 0.5))
                    )

                # 4. Direção Quântica (-1 a 1): sinal bearish/bullish
                if "quantum_direction" not in metrics:
                    entropy = metrics.get("entropy", 0.5)
                    coherence = metrics.get("coherence", 0.5)
                    mi = metrics.get(
                        "mutual_information", metrics.get("reflection_result", 0.1)
                    )

                    if abs(momentum) > 0.1:
                        # Quando há informação mútua, o sinal momentum se torna
                        # mais confiável
                        if entropy > 0.8 and coherence < 0.3:
                            # Possível ponto de reversão (oposto ao momentum)
                            metrics["quantum_direction"] = float(
                                -1 * np.sign(momentum) * mi
                            )
                        else:
                            # Continuação da tendência (mesma direção do
                            # momentum)
                            metrics["quantum_direction"] = float(np.sign(momentum) * mi)
                    else:
                        # Sem sinal claro
                        metrics["quantum_direction"] = 0.0

                # Registrar resultados no log
                logger.info(
                    f"Análise quântica para {symbol}: "
                    f"E={metrics.get('entropy', 0):.2f}, "
                    f"C={metrics.get('coherence', 0):.2f}, "
                    f"QD={metrics.get('quantum_direction', 0):.2f}, "
                    f"QC={metrics.get('quantum_confidence', 0):.2f}"
                )

                # Adicionar flag de uso do QUALIA
                metrics["qualia_universe_used"] = (
                    metrics_result.get("qualia_mode", "unavailable") == "full"
                )

                # Aplicar detector de padrões quânticos multiescala, se
                # disponível
                if hasattr(self, "qualia_market_pattern_detector"):
                    try:
                        # Usar detector de padrões quânticos multiescala
                        logger.info(
                            f"Aplicando detector de padrões quânticos multiescala para {symbol}"
                        )

                        # Preparar dados para detecção de padrões
                        pattern_detection_results = (
                            self.qualia_market_pattern_detector.detect_patterns(
                                market_data=formatted_data[symbol][shortest_tf],
                                timeframe_data=formatted_data[symbol],
                            )
                        )

                        if pattern_detection_results:
                            # Registrar padrões importantes detectados
                            if (
                                "patterns" in pattern_detection_results
                                and pattern_detection_results["patterns"]
                            ):
                                patterns = pattern_detection_results["patterns"]
                                logger.info(
                                    f"Detectados {len(patterns)} padrões quânticos para {symbol}"
                                )

                                # Mostrar padrões de confiança alta
                                high_confidence_patterns = [
                                    p for p in patterns if p.get("confidence", 0) > 0.7
                                ]
                                if high_confidence_patterns:
                                    # Mostrar até 3 padrões de alta confiança
                                    for pattern in high_confidence_patterns[:3]:
                                        logger.info(
                                            f"Padrão de alta confiança: {pattern.get('name')} - "
                                            f"Conf: {pattern.get('confidence', 0):.2f}, "
                                            f"Tipo: {pattern.get('type', 'desconhecido')}"
                                        )

                                # Adicionar padrões detectados às métricas
                                metrics["detected_patterns"] = patterns

                            # Adicionar sinais de trading baseados em padrões
                            if (
                                "trading_signals" in pattern_detection_results
                                and pattern_detection_results["trading_signals"]
                            ):
                                signals = pattern_detection_results["trading_signals"]
                                for signal_type, signal_data in signals.items():
                                    if signal_data.get("confidence", 0) > 0.6:
                                        logger.info(
                                            f"Sinal de trading detectado para {symbol}: {signal_type} - "
                                            f"Confiança: {signal_data.get('confidence', 0):.2f}"
                                        )

                                # Adicionar sinais às métricas
                                metrics["pattern_signals"] = pattern_detection_results[
                                    "trading_signals"
                                ]

                            # Adicionar confiança geral da detecção de padrões
                            if "confidence" in pattern_detection_results:
                                metrics["pattern_confidence"] = (
                                    pattern_detection_results["confidence"]
                                )

                                # Influenciar a confiança quântica geral com
                                # base nos padrões detectados
                                pattern_confidence = pattern_detection_results[
                                    "confidence"
                                ]
                                current_confidence = metrics.get(
                                    "quantum_confidence", 0.5
                                )

                                # Ponderar 70% confiança atual, 30% confiança
                                # dos padrões
                                metrics["quantum_confidence"] = (
                                    0.7 * current_confidence + 0.3 * pattern_confidence
                                )
                    except Exception as e:
                        logger.error(
                            f"Erro ao aplicar detector de padrões quânticos para {symbol}: {e}"
                        )
                        logger.debug(traceback.format_exc())

                # Manter histórico das métricas quânticas
                if symbol not in self.quantum_metrics:
                    self.quantum_metrics[symbol] = {}

                # Salvar métricas mais recentes
                self.quantum_metrics[symbol]["latest"] = metrics

                # Manter histórico (últimas 20 leituras)
                if "history" not in self.quantum_metrics[symbol]:
                    self.quantum_metrics[symbol]["history"] = []

                metrics["timestamp"] = datetime.now(timezone.utc).isoformat()
                history = self.quantum_metrics[symbol]["history"]
                history.append(metrics.copy())

                # Limitar tamanho do histórico
                if len(history) > 20:
                    self.quantum_metrics[symbol]["history"] = history[-20:]

                return metrics

            except Exception as e:
                logger.error(
                    f"Erro ao usar calculador de métricas quânticas para {symbol}: {e}"
                )
                logger.debug(traceback.format_exc())

                # Tentar fallback para o universo quântico tradicional
                if self.quantum_universe:
                    try:
                        logger.info(
                            f"Tentando fallback para universo quântico tradicional para {symbol}"
                        )
                        # Configurações básicas
                        self.quantum_universe.thermal = thermal_level

                        # Executar simulação básica
                        results = self.quantum_universe.run(shots=1024)

                        # Extrair métricas básicas
                        if (
                            results
                            and isinstance(results, dict)
                            and "metrics" in results
                        ):
                            metrics = {}
                            metrics_obj = results["metrics"]

                            # Extrair principais métricas
                            if isinstance(metrics_obj, dict):
                                metrics["entropy"] = float(
                                    np.mean(metrics_obj.get("quantum_entropy", [0.5]))
                                )
                                metrics["coherence"] = float(
                                    np.mean(metrics_obj.get("coherence", [0.5]))
                                )
                            else:
                                metrics["entropy"] = 0.5
                                metrics["coherence"] = 0.5

                            # Adicionar métricas básicas
                            metrics["volatility"] = float(volatility)
                            metrics["momentum"] = float(momentum)
                            metrics["quantum_direction"] = float(
                                np.sign(momentum) * 0.1
                            )
                            metrics["fallback_mode"] = True

                            # Registrar uso do fallback
                            logger.info(
                                f"Usando fallback do universo quântico para {symbol}"
                            )

                            return metrics
                    except Exception as fallback_error:
                        logger.error(
                            f"Erro no fallback do universo quântico para {symbol}: {fallback_error}"
                        )

                # Se chegou aqui, todos os métodos falharam
                return {
                    "entropy": 0.5,
                    "coherence": 0.5,
                    "active_states": 0,
                    "error": str(e),
                    "fallback_mode": True,
                }

        except Exception as e:
            logger.error(f"Erro na análise quântica para {symbol}: {e}")
            logger.debug(traceback.format_exc())
            return {
                "entropy": 0.5,  # Valor neutro/médio
                "coherence": 0.5,
                "active_states": 0,
                "error": str(e),
            }

    def _trade_execution_loop(self) -> None:
        """
        Loop contínuo para execução de sinais de trading.
        """
        logger.info("Iniciando thread de execução de trades")

        while not self.stop_event.is_set():
            try:
                # Verificar se há sinais pendentes
                if not self.signal_queue.empty():
                    # Obter próximo sinal
                    signal_data = self.signal_queue.get(block=False)
                    self.signal_queue.task_done()

                    # Extrair informações do sinal
                    symbol = signal_data["symbol"]
                    signal = signal_data["signal"]
                    action = signal.get("action", "").lower()
                    signal["action"] = action
                    if action not in ["buy", "sell"]:
                        logger.warning(
                            f"Ação não reconhecida '{signal_data['signal'].get('action')}' para {symbol}"
                        )

                    # Verificar se já temos posição aberta neste símbolo
                    existing_position = next(
                        (p for p in self.positions if p.symbol == symbol), None
                    )

                    # Se já temos posição e sinal é na mesma direção, ajustar quantidade
                    if existing_position and existing_position.side == action:
                        desired_size = self._calculate_position_size(symbol, signal)
                        additional = desired_size - existing_position.position_size
                        if additional > 0:
                            self._add_to_position(existing_position, additional, signal)
                        else:
                            logger.info(
                                f"Sinal em mesma direção para {symbol} não exige ajuste"
                            )
                        continue

                    # Se já temos posição na direção oposta, fechar primeiro
                    if existing_position and existing_position.side != action:
                        try:
                            # Fechar posição existente
                            self._close_position(
                                existing_position, reason=f"Sinal contrário: {action}"
                            )
                            # Dar um tempo para o mercado processar
                            self._wait_for_stop(1)
                        except Exception as e:
                            logger.error(
                                f"Erro ao fechar posição existente em {symbol}: {e}"
                            )
                            continue

                    # Verificar se podemos abrir nova posição
                    can_open, reason = self.risk_manager.can_open_new_position(
                        len(self.positions)
                    )
                    if not can_open:
                        logger.warning(
                            f"Não é possível abrir posição em {symbol}: {reason}"
                        )
                        continue

                    # Verificar diretivas de metacognição
                    if not self._check_metacognition_before_entry():
                        logger.info(
                            "Abertura de posição cancelada por diretiva metacognitiva"
                        )
                        continue

                    # Checar a massa informacional do universo
                    if (
                        self.quantum_universe
                        and hasattr(self.quantum_universe, "informational_mass")
                        and self.quantum_universe.informational_mass
                        < self.mass_threshold
                    ):
                        logger.info(
                            "Nova posição ignorada em %s: informational_mass %.4f < limiar %.4f",
                            symbol,
                            self.quantum_universe.informational_mass,
                            self.mass_threshold,
                        )
                        continue

                    # Calcular tamanho da posição
                    position_size = self._calculate_position_size(symbol, signal)
                    if position_size <= 0:
                        logger.warning(
                            f"Tamanho da posição para {symbol} é zero ou negativo"
                        )
                        continue

                    # Abrir nova posição
                    try:
                        self._open_position(symbol, action, position_size, signal)
                    except Exception as e:
                        logger.error(f"Erro ao abrir posição em {symbol}: {e}")

                # Aguardar próximo ciclo respeitando o sinal de parada
                self._wait_for_stop(self.trade_interval)

            except queue.Empty:
                # Fila vazia, continuar loop
                self._wait_for_stop(1)
            except Exception as e:
                logger.error(f"Erro no loop de execução de trades: {e}")
                logger.debug(traceback.format_exc())

                # Aguardar um pouco antes de tentar novamente
                self._wait_for_stop(5)

        logger.info("Thread de execução de trades encerrada")

    def _perform_metacognition_cycle(self) -> None:
        """
        Executa o ciclo completo de metacognição quântica para trading.

        Este método realiza:
        1. Análise de padrões nas decisões de trading passadas
        2. Geração de insights metacognitivos
        3. Aplicação de ajustes ao sistema com base nos insights

        O ciclo permite que o sistema QUALIA observe seu próprio processo
        decisório e resultados, melhorando continuamente suas capacidades.
        """
        try:
            logger.info("Iniciando ciclo completo de metacognição quântica")

            # 1. Realizar análise metacognitiva das decisões passadas
            insights = self.metacognition_system.perform_metacognitive_analysis()

            if insights:
                logger.info(
                    f"Análise metacognitiva concluída: {len(insights)} insights gerados"
                )

                # Listar principais insights no log
                for i, insight in enumerate(
                    insights[:3]
                ):  # Listar apenas os 3 principais
                    logger.info(
                        f"Insight #{i+1}: {insight.get('type')}, conf={insight.get('confidence', 0):.2f}"
                    )
                    if "description" in insight:
                        logger.info(f"  {insight['description']}")

                # 2. Aplicar ajustes com base nos insights (usando limiar de
                # confiança)
                applied_adjustments = (
                    self.metacognition_system.apply_metacognitive_feedback(
                        confidence_threshold=0.65
                    )
                )

                if applied_adjustments:
                    logger.info(
                        f"Metacognição: {len(applied_adjustments)} ajustes aplicados ao sistema"
                    )
                    for adj in applied_adjustments:
                        logger.info(
                            f"  Ajuste: {adj.get('parameter')} = {adj.get('value')}, conf={adj.get('confidence', 0):.2f}"
                        )
                else:
                    logger.info("Metacognição: Nenhum ajuste aplicado desta vez")
            else:
                logger.info("Análise metacognitiva concluída: nenhum insight gerado")

            # 3. Registrar status final no log para monitoramento
            metacog_state = self.metacognition_system.get_metacognitive_state()
            decisions_count = metacog_state.get("decision_history_size", 0)
            outcomes_count = metacog_state.get("decisions_with_outcomes", 0)

            logger.info(
                f"Estado da metacognição: {decisions_count} decisões ({outcomes_count} com resultados)"
            )

            # Registrar correlações importantes no log
            correlations = metacog_state.get("performance_correlations", {})
            if correlations:
                top_correlation = max(correlations.items(), key=lambda x: abs(x[1]))
                logger.info(
                    f"Correlação mais forte: {top_correlation[0]} = {top_correlation[1]:.2f}"
                )

        except Exception as e:
            logger.error(f"Erro durante ciclo de metacognição: {e}")
            logger.debug(traceback.format_exc())

    def _evolve_trading_strategies(self) -> None:
        """
        Evolui estratégias de trading usando o ciclo QAST do QUALIA.

        Este método aplica o ciclo de Quantum Awareness, Self-reflection, e Transformation
        para evoluir estratégias de trading com base nos dados de mercado recentes
        e no desempenho histórico das estratégias.
        """
        if (
            not hasattr(self, "qast_evolutionary_strategy")
            or not self.qast_evolutionary_strategy
        ):
            logger.warning("Estratégia evolucionária QAST não disponível")
            return

        try:
            logger.info("Iniciando evolução de estratégias via ciclo QAST...")

            # Verificar se temos dados de mercado suficientes
            if not self.market_data:
                logger.warning("Sem dados de mercado para evoluir estratégias")
                return

            # Selecionar dados para o ciclo evolutivo e convertê-los para DataFrame
            market_frames = []
            for symbol in self.symbols:
                if symbol in self.market_data:
                    for tf in self.timeframes:
                        if tf in self.market_data[symbol]:
                            sliced = {
                                "close": self.market_data[symbol][tf].get("close", [])[
                                    -100:
                                ],
                                "high": self.market_data[symbol][tf].get("high", [])[
                                    -100:
                                ],
                                "low": self.market_data[symbol][tf].get("low", [])[
                                    -100:
                                ],
                                "volume": self.market_data[symbol][tf].get(
                                    "volume", []
                                )[-100:],
                                "timestamp": self.market_data[symbol][tf].get(
                                    "timestamp", []
                                )[-100:],
                            }
                            df = pd.DataFrame(sliced)
                            df["symbol"] = symbol
                            df["timeframe"] = tf
                            market_frames.append(df)

            market_data_for_evolution = (
                pd.concat(market_frames, ignore_index=True)
                if market_frames
                else pd.DataFrame()
            )

            n_trades_last_hour = self._count_trades_last_hour()
            cycles = max(1, n_trades_last_hour)

            if hasattr(self.qast_evolutionary_strategy, "generations_per_call"):
                self.qast_evolutionary_strategy.generations_per_call = cycles

            best_strategy = self.qast_evolutionary_strategy.evolve_strategies(
                market_data=market_data_for_evolution,
                cycles=cycles,
            )

            # Obter estatísticas da população após evolução
            population_stats = self.qast_evolutionary_strategy.get_population_stats()

            # Registrar resultados
            logger.info(
                f"Evolução via QAST concluída: {population_stats.get('generation', 0)} gerações"
            )
            logger.info(
                f"Melhor fitness: {population_stats.get('best_fitness', 0):.4f}, "
                f"Média: {population_stats.get('mean_fitness', 0):.4f}, "
                f"Diversidade: {population_stats.get('diversity', 0):.4f}"
            )

            # Atualizar estratégia principal se a evolução produziu uma
            # estratégia superior
            if best_strategy and hasattr(self.strategy, "update_from_evolved"):
                self.strategy.update_from_evolved(best_strategy)
                logger.info(
                    "Estratégia principal atualizada com variante evoluída via QAST"
                )

        except Exception as e:
            logger.error(f"Erro ao evoluir estratégias via QAST: {e}")
            logger.debug(traceback.format_exc())

    def _position_monitoring_loop(self) -> None:
        """
        Loop contínuo para monitoramento de posições abertas.
        """
        logger.info("Iniciando thread de monitoramento de posições")

        # Contador para análise de risco periódica
        risk_cycle_counter = 0

        while not self.stop_event.is_set():
            try:
                with self._pos_lock:
                    if not self.positions:
                        self._monitor_backoff_level = 0
                        self._current_monitor_interval = self.monitor_min_interval
                        positions_snapshot: List[Position] = []
                    else:
                        positions_snapshot = list(self.positions)
                if not positions_snapshot:
                    self._wait_for_stop(self._current_monitor_interval)
                    continue

                # Aplicar diretivas de metacognição e limites de posição
                self._close_positions_on_metacognition()

                # Para cada posição aberta, verificar status
                for position in positions_snapshot:
                    symbol = position.symbol

                    # Obter preço atual
                    current_price = self._get_current_price(symbol)
                    if current_price is None:
                        logger.warning(
                            f"Não foi possível obter preço atual para {symbol}"
                        )
                        continue

                    # Atualizar P&L não realizado
                    position.current_price = current_price
                    if position.side == "buy":
                        position.unrealized_pnl = (
                            current_price - position.entry_price
                        ) * position.quantity
                        position.unrealized_pnl_pct = (
                            (current_price / position.entry_price) - 1
                        ) * 100
                    else:  # sell
                        position.unrealized_pnl = (
                            position.entry_price - current_price
                        ) * position.quantity
                        position.unrealized_pnl_pct = (
                            (position.entry_price / current_price) - 1
                        ) * 100

                    # Atualizar duração da posição
                    entry_time = datetime.fromisoformat(position.entry_time)
                    if entry_time.tzinfo is None:
                        entry_time = entry_time.replace(tzinfo=timezone.utc)
                    position.duration = (
                        datetime.now(timezone.utc) - entry_time
                    ).total_seconds()

                    # Aplicar gerenciamento dinâmico de stop via controlador
                    # quântico, se disponível
                    if (
                        hasattr(self, "quantum_risk_position_controller")
                        and self.quantum_risk_position_controller
                    ):
                        try:
                            # Preparar dados de preço recente para análise
                            price_action = {}
                            if (
                                symbol in self.market_data
                                and "1m" in self.market_data[symbol]
                            ):
                                price_data = self.market_data[symbol]["1m"]
                                if price_data and len(price_data.get("close", [])) > 10:
                                    price_action = {
                                        "close": price_data["close"][-10:],
                                        "high": price_data["high"][-10:],
                                        "low": price_data["low"][-10:],
                                        "volume": price_data["volume"][-10:],
                                        "timestamp": price_data["timestamp"][-10:],
                                        "current_price": current_price,
                                    }

                            # Obter métricas de risco atuais para o símbolo
                            risk_metrics = {}
                            if (
                                self.use_quantum_analysis
                                and self.quantum_metrics
                                and "risk" in self.quantum_metrics
                            ):
                                risk_metrics = self.quantum_metrics["risk"]

                            # Executar gerenciamento dinâmico de stops
                            if price_action:
                                stop_adjustments = self.quantum_risk_position_controller.dynamic_stop_management(
                                    position=position,
                                    risk_metrics=risk_metrics,
                                    price_action=price_action,
                                )

                                # Aplicar ajustes recomendados
                                if stop_adjustments and isinstance(
                                    stop_adjustments, dict
                                ):
                                    # Ajustar stop loss se recomendado
                                    if "new_stop_loss" in stop_adjustments:
                                        new_stop = stop_adjustments["new_stop_loss"]
                                        old_stop = position.stop_loss

                                        # Registrar ajuste no log apenas se for
                                        # significativo
                                        if (
                                            old_stop is not None
                                            and abs((new_stop / old_stop) - 1) > 0.005
                                        ):  # >0.5% de mudança
                                            logger.info(
                                                f"Ajuste dinâmico de stop loss para {symbol}: "
                                                f"{old_stop:.2f} → {new_stop:.2f}"
                                            )

                                        # Atualizar stop loss
                                        position.stop_loss = new_stop

                                        # Registrar motivo do ajuste
                                        if "adjustment_reason" in stop_adjustments:
                                            logger.info(
                                                f"Motivo: {stop_adjustments['adjustment_reason']}"
                                            )

                                    # Ajustar take profit se recomendado
                                    if "new_take_profit" in stop_adjustments:
                                        new_target = stop_adjustments["new_take_profit"]
                                        old_target = position.target_price

                                        # Registrar ajuste no log
                                        if (
                                            old_target is not None
                                            and abs((new_target / old_target) - 1)
                                            > 0.005
                                        ):
                                            logger.info(
                                                f"Ajuste dinâmico de take profit para {symbol}: "
                                                f"{old_target:.2f} → {new_target:.2f}"
                                            )

                                        # Atualizar take profit
                                        position.target_price = new_target
                        except Exception as e:
                            logger.warning(
                                f"Erro ao aplicar gerenciamento dinâmico de stops para {symbol}: {e}"
                            )

                    # Verificar se deve fechar a posição
                    quantum_metrics = (
                        self.quantum_metrics if self.use_quantum_analysis else None
                    )
                    should_close, reason = self.strategy.should_close_position(
                        position, current_price, quantum_metrics
                    )

                    if should_close:
                        try:
                            # Fechar posição
                            self._close_position(position, reason=reason)
                        except Exception as e:
                            logger.error(f"Erro ao fechar posição em {symbol}: {e}")

                # Análise de risco quântica periódica (a cada 5 ciclos)
                risk_cycle_counter += 1
                with self._pos_lock:
                    has_positions = len(self.positions) > 0

                if (
                    risk_cycle_counter >= 5
                    and self.quantum_risk_analyzer is not None
                    and has_positions
                ):
                    risk_cycle_counter = 0

                    # Executar análise de risco quântica
                    risk_logger.info(
                        "Executando análise de risco quântica do portfólio"
                    )
                    risk_metrics = self._calculate_quantum_risk_metrics()

                    # Registrar principais métricas
                    if risk_metrics:
                        portfolio_value = risk_metrics.get("portfolio_metrics", {}).get(
                            "value", 0
                        )
                        var_99 = (
                            risk_metrics.get("var_1d", {}).get("var", {}).get("0.99", 0)
                        )
                        var_99_pct = (
                            risk_metrics.get("var_1d", {})
                            .get("var_pct", {})
                            .get("0.99", 0)
                        )
                        quantum_entropy = risk_metrics.get("quantum_entropy", 0)
                        coherence_risk = risk_metrics.get("coherence_risk_indicator", 0)

                        risk_logger.info(f"Valor do portfólio: ${portfolio_value:.2f}")
                        risk_logger.info(
                            f"VaR (99%): ${var_99:.2f} ({var_99_pct:.2f}%)"
                        )
                        risk_logger.info(f"Entropia quântica: {quantum_entropy:.4f}")
                        risk_logger.info(
                            f"Indicador de risco de coerência: {coherence_risk:.4f}"
                        )

                    # Verificar se há alertas de risco extremo
                    if risk_metrics and risk_metrics.get("extreme_risk_alert", False):
                        risk_logger.warning("⚠️ ALERTA DE RISCO EXTREMO ⚠️")

                        # Identificar os fatores que causaram o alerta
                        high_entropy = risk_metrics.get("quantum_entropy", 0) > 0.8
                        high_var = (
                            risk_metrics.get("var_1d", {})
                            .get("var_pct", {})
                            .get("0.99", 0)
                            > 15
                        )
                        high_stress = (
                            risk_metrics.get("stress_test", {})
                            .get("impact_pct", {})
                            .get("0.99", 0)
                            > 50
                        )

                        if high_entropy:
                            risk_logger.warning(
                                "Causa: Alta entropia quântica detectada - Volatilidade extrema do mercado"
                            )
                        if high_var:
                            risk_logger.warning(
                                "Causa: VaR elevado - Risco de perda significativa"
                            )
                        if high_stress:
                            risk_logger.warning(
                                "Causa: Teste de estresse com impacto elevado"
                            )

                        # Determinar nível de reação baseado na severidade
                        severity_factors = sum([high_entropy, high_var, high_stress])

                        if severity_factors >= 3:
                            # Cenário crítico: fechar todas as posições
                            risk_logger.warning(
                                "Múltiplos fatores de risco extremos - Fechando TODAS as posições"
                            )

                            for position in list(
                                self.positions
                            ):  # Usar cópia para permitir modificações
                                try:
                                    self._close_position(
                                        position,
                                        reason="Alerta de Risco Extremo",
                                    )
                                except Exception as e:
                                    logger.error(
                                        f"Erro ao fechar posição em {position.symbol}: {e}"
                                    )
                        elif severity_factors == 2:
                            # Situação severa: reduzir exposição de forma gradativa
                            risk_logger.warning(
                                "Fatores de risco elevados - Reduzindo exposição em 75%"
                            )
                            self._reduce_portfolio_exposure(75.0)
                        elif severity_factors == 1:
                            # Um único fator aciona redução mais leve
                            risk_logger.warning(
                                "Risco detectado - Reduzindo exposição nas posições mais relevantes"
                            )

                            # Ordenar posições por tamanho (maiores primeiro)
                            positions_by_risk = sorted(
                                self.positions,
                                key=lambda p: p.position_size,
                                reverse=True,
                            )

                            # Fechar as maiores posições (50% do total)
                            positions_to_close = positions_by_risk[
                                : max(1, len(positions_by_risk) // 2)
                            ]

                            for position in positions_to_close:
                                try:
                                    self._close_position(
                                        position,
                                        reason="Redução de Exposição por Risco",
                                    )
                                except Exception as e:
                                    logger.error(
                                        f"Erro ao fechar posição em {position.symbol}: {e}"
                                    )

                    # Registrar conclusão da análise de risco
                    risk_logger.info("Análise de risco concluída")

                # Atualizar performance metrics
                self._update_performance_metrics()

                self._consecutive_monitor_failures = 0
                near_triggers = self._positions_close_to_triggers()
                if near_triggers:
                    self._monitor_backoff_level = 0
                    self._current_monitor_interval = self.monitor_min_interval
                else:
                    self._monitor_backoff_level += 1
                    backoff = self.monitor_min_interval * (
                        2**self._monitor_backoff_level
                    )
                    self._current_monitor_interval = min(
                        backoff, self.monitor_max_interval
                    )

                self._wait_for_stop(self._current_monitor_interval)

            except Exception as e:
                logger.error(f"Erro no loop de monitoramento de posições: {e}")
                logger.debug(traceback.format_exc())
                self._consecutive_monitor_failures += 1
                self._monitor_backoff_level += 1
                backoff = self.monitor_min_interval * (2**self._monitor_backoff_level)
                self._current_monitor_interval = min(backoff, self.monitor_max_interval)

                # Aguardar um pouco antes de tentar novamente
                self._wait_for_stop(self._current_monitor_interval)

        logger.info("Thread de monitoramento de posições encerrada")

    def _get_current_price(self, symbol: str) -> Optional[float]:
        """
        Obtém o preço atual de um símbolo.

        Args:
            symbol: Símbolo para obter o preço

        Returns:
            Preço atual ou None se não disponível
        """
        # Verificar se temos dados recentes para este símbolo
        if symbol in self.market_data and "1m" in self.market_data[symbol]:
            data = self.market_data[symbol]["1m"]
            if data and len(data["close"]) > 0:
                return data["close"][-1]

        # Se não temos dados recentes, tentar buscar da exchange
        try:
            # Buscar ticker para obter preço mais atualizado
            ticker_data = self.exchange.get_ticker([symbol.replace("/", "")])
            if ticker_data and symbol.replace("/", "") in ticker_data:
                # Formato do ticker varia por exchange
                ticker = ticker_data[symbol.replace("/", "")]
                if "c" in ticker and len(ticker["c"]) > 0:
                    # Kraken usa 'c' para preço de fechamento/último
                    return float(ticker["c"][0])

            return None
        except Exception as e:
            logger.error(f"Erro ao obter preço atual para {symbol}: {e}")
            return None

    def _positions_close_to_triggers(self) -> bool:
        """Return ``True`` if any position is near or beyond its SL/TP."""

        threshold = self.monitor_close_threshold
        with self._pos_lock:
            positions_snapshot = list(self.positions)
        for pos in positions_snapshot:
            price = pos.current_price
            if price is None:
                continue

            if pos.stop_loss is not None:
                crossed = (pos.side == "buy" and price <= pos.stop_loss) or (
                    pos.side == "sell" and price >= pos.stop_loss
                )
                near = abs(price - pos.stop_loss) / price <= threshold
                if crossed or near:
                    return True

            if pos.take_profit is not None:
                crossed = (pos.side == "buy" and price >= pos.take_profit) or (
                    pos.side == "sell" and price <= pos.take_profit
                )
                near = abs(price - pos.take_profit) / price <= threshold
                if crossed or near:
                    return True

        return False

    def _calculate_quantum_risk_metrics(self) -> Dict[str, Any]:
        """
        Calcula métricas de risco avançadas usando o calculador de métricas quânticas.

        Esta função integra múltiplas camadas de análise quântica para:
        - Avaliar riscos conhecidos (VaR, volatilidade)
        - Detectar eventos extremos não capturados por modelos tradicionais
        - Calcular correlações quânticas entre ativos
        - Identificar instabilidades no mercado através de análise de caos quântico
        - Aplicar o controlador de posições baseado em dinâmica quântica
        - Detectar padrões emergentes com o detector de padrões quânticos multiescala

        Returns:
            Dicionário com métricas de risco quântico avançadas
        """
        start_time = time.time()

        # Verificar se temos dados suficientes para cálculo de métricas
        with self._pos_lock:
            positions_snapshot = list(self.positions)
        if not positions_snapshot:
            risk_logger.warning(
                "Impossível calcular métricas de risco: portfólio vazio"
            )
            return {}

        # Se temos mercado vazio, também não podemos calcular métricas
        # quânticas reais
        if not self.market_data:
            risk_logger.warning(
                "Impossível calcular métricas quânticas: dados de mercado indisponíveis"
            )
            return {}

        # Extrair símbolos das posições atuais para foco da análise
        position_symbols = [p.symbol for p in positions_snapshot]

        # Calcular volatilidade de mercado para configuração do coeficiente
        # térmico
        volatilities = {}
        for symbol in position_symbols:
            if symbol in self.market_data:
                timeframes = list(self.market_data[symbol].keys())
                if timeframes:
                    # Usar o timeframe mais curto disponível
                    tf = min(timeframes)
                    prices = self.market_data[symbol][tf].get("close", [])
                    if len(prices) > 5:
                        # Calcular volatilidade com base nos últimos preços
                        returns = np.diff(prices) / prices[:-1]
                        volatilities[symbol] = np.std(returns)

        # Definir volatilidade média para configurar coeficiente térmico
        avg_volatility = np.mean(list(volatilities.values())) if volatilities else 0.1
        # Manter entre 0.05 e 0.3
        thermal_coef = min(0.3, max(0.05, avg_volatility * 10))

        # Preferir usar o calculador de métricas quânticas integrado com QUALIA
        try:
            risk_logger.info(
                "Calculando métricas quânticas usando self.quantum_metrics_calculator"  # YAA: Corrigido nome da variável no log
            )

            # YAA: Adicionar verificação para self.quantum_metrics_calculator
            if not self.quantum_metrics_calculator:
                risk_logger.warning(
                    "QuantumMetricsCalculator não está inicializado. Análise de risco quântico via QMC não pode prosseguir."
                )
                # A lógica de fallback será executada naturalmente se esta parte for pulada
            else:  # Somente prosseguir se self.quantum_metrics_calculator estiver disponível
                # Configurar coeficiente térmico com base na volatilidade do
                # mercado
                self.quantum_metrics_calculator.set_thermal_coefficient(
                    thermal_coef
                )  # YAA: Corrigido
                risk_logger.info(
                    f"Coeficiente térmico definido como {thermal_coef:.3f} com base na volatilidade"
                )

                # Calcular métricas quânticas, focando em análise de risco
                metrics = self.quantum_metrics_calculator.calculate_metrics(
                    self.market_data
                )  # YAA: Corrigido

                if metrics and isinstance(metrics, dict):
                    # Criar estrutura de métricas de risco
                    risk_metrics = {
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                        "thermal_coefficient": thermal_coef,
                        "portfolio_symbols": position_symbols,
                        "quantum_metrics": {},
                    }

                    # Extrair métricas para o dicionário de quantum_metrics
                    for key, value in metrics.items():
                        # Converter listas para valores médios quando necessário
                        if isinstance(value, list):
                            if len(value) > 0:
                                try:
                                    # Para valores numéricos, calcular média
                                    risk_metrics["quantum_metrics"][key] = float(
                                        np.mean(value)
                                    )
                                except BaseException as exc:
                                    logger.exception(
                                        "Erro ao calcular média para a métrica de risco %s: %s",
                                        key,
                                        exc,
                                    )
                                    # Para valores não numéricos, usar o mais
                                    # recente
                                    risk_metrics["quantum_metrics"][key] = value[-1]
                            else:
                                risk_metrics["quantum_metrics"][key] = 0
                        elif key not in [
                            "timestamp",
                            "n_qubits",
                            "thermal_coefficient",
                            "execution_time",
                        ]:
                            # Copiar apenas métricas relevantes
                            risk_metrics["quantum_metrics"][key] = value

                    # Calcular indicadores de risco baseados nas métricas quânticas
                    # Usando pesos específicos para cada componente
                    risk_level = 0
                    risk_components = []
                    risk_weights = []

                    # Extrair métricas importantes para análise de risco
                    entropy = risk_metrics["quantum_metrics"].get(
                        "entropy",
                        risk_metrics["quantum_metrics"].get("quantum_entropy", 0.5),
                    )
                    coherence = risk_metrics["quantum_metrics"].get("coherence", 0.5)
                    instability = risk_metrics["quantum_metrics"].get(
                        "quantum_instability", entropy * (1 - coherence)
                    )

                    # 1. Componente de entropia quântica (principal indicador de
                    # caos/imprevisibilidade)
                    entropy_score = entropy * 100  # Entropia de 1.0 = score 100
                    risk_components.append(entropy_score)
                    risk_weights.append(0.35)  # Peso de 35%

                    # 2. Componente de instabilidade quântica
                    instability_score = instability * 100
                    risk_components.append(instability_score)
                    risk_weights.append(0.30)  # Peso de 30%

                    # 3. Componente de direcionalidade quântica
                    direction_confidence = abs(
                        risk_metrics["quantum_metrics"].get("quantum_direction", 0.0)
                    )
                    if entropy > 0.75 and direction_confidence < 0.3:
                        # Alta entropia e baixa confiança na direção indica alto
                        # risco
                        direction_risk_score = 90
                    else:
                        # Do contrário, maior confiança na direção reduz o risco
                        direction_risk_score = 100 * (1 - direction_confidence)
                    risk_components.append(direction_risk_score)
                    risk_weights.append(0.25)  # Peso de 25%

                    # 4. Componente de volatilidade de mercado
                    # Volatilidade de 0.1 = score 100
                    volatility_score = min(100, avg_volatility * 1000)
                    risk_components.append(volatility_score)
                    risk_weights.append(0.10)  # Peso de 10%

                    # Calcular pontuação de risco ponderada (0-100)
                    if risk_components and sum(risk_weights) > 0:
                        weighted_scores = [
                            score * weight
                            for score, weight in zip(risk_components, risk_weights)
                        ]
                        risk_level = sum(weighted_scores) / sum(risk_weights)
                    else:
                        # Se não temos componentes suficientes, usar cálculo
                        # simplificado
                        risk_level = max(
                            30, min(80, entropy * 70 + volatility_score * 0.3)
                        )

                    # Determinar categoria de risco e recomendação
                    if risk_level < 25:
                        risk_category = "BAIXO"
                        risk_action = "Manter estratégia normal"
                        alert_level = "info"
                    elif risk_level < 50:
                        risk_category = "MODERADO"
                        risk_action = "Monitorar de perto, sem ajustes imediatos"
                        alert_level = "info"
                    elif risk_level < 75:
                        risk_category = "ELEVADO"
                        risk_action = (
                            "Considerar redução de tamanho de posições em 25-50%"
                        )
                        alert_level = "warning"
                    elif risk_level < 90:
                        risk_category = "ALTO"
                        risk_action = "Reduzir exposição em 50-75% e aumentar stops"
                        alert_level = "danger"
                    else:
                        risk_category = "EXTREMO"
                        risk_action = (
                            "Considerar fechamento de todas as posições imediatamente"
                        )
                        alert_level = "danger"

                    # Armazenar categorização de risco
                    risk_metrics["risk_level"] = risk_level
                    risk_metrics["risk_category"] = risk_category
                    risk_metrics["recommended_action"] = risk_action
                    risk_metrics["alert_level"] = alert_level

                    # Determinar alertas de risco específicos
                    risk_alerts = []

                    # 1. Alerta de alta entropia
                    if entropy > 0.8:
                        risk_alerts.append(
                            {
                                "type": "quantum_entropy",
                                "message": f"Alta entropia quântica ({entropy:.4f}) indica mercado caótico e imprevisível",
                                "severity": "high" if entropy > 0.9 else "medium",
                            }
                        )

                    # 2. Alerta de baixa coerência
                    if coherence < 0.3 and entropy > 0.6:
                        risk_alerts.append(
                            {
                                "type": "quantum_coherence",
                                "message": f"Baixa coerência quântica ({coherence:.4f}) com alta entropia indica instabilidade",
                                "severity": "high" if coherence < 0.2 else "medium",
                            }
                        )

                    # 3. Alerta de volatilidade
                    if avg_volatility > 0.05:
                        risk_alerts.append(
                            {
                                "type": "volatility",
                                "message": f"Volatilidade elevada ({avg_volatility:.4f}) detectada no mercado",
                                "severity": (
                                    "high" if avg_volatility > 0.1 else "medium"
                                ),
                            }
                        )

                    # 4. Alerta de possível reversão
                    if (
                        risk_metrics["quantum_metrics"].get("reversal_prediction", 0)
                        > 0.6
                    ):
                        risk_alerts.append(
                            {
                                "type": "reversal",
                                "message": "Alta probabilidade de reversão de tendência detectada",
                                "severity": "high",
                            }
                        )

                    # Adicionar alertas ao resultado
                    risk_metrics["risk_alerts"] = risk_alerts

                    # Registrar tempo de execução
                    execution_time = time.time() - start_time
                    risk_metrics["execution_time"] = execution_time

                    # Armazenar métricas na memória do sistema
                    self.quantum_metrics["risk"] = risk_metrics

                    # Registrar principais métricas no log
                    risk_logger.info(
                        f"Entropia Quântica: {entropy:.4f}, Coerência: {coherence:.4f}"
                    )
                    risk_logger.info(
                        f"Nível de Risco: {risk_level:.1f}/100 - Categoria: {risk_category}"
                    )

                    # Registrar alertas no log se houver
                    if risk_alerts:
                        for alert in risk_alerts:
                            if alert["severity"] == "high":
                                risk_logger.warning(
                                    f"ALERTA CRÍTICO: {alert['message']}"
                                )
                            else:
                                risk_logger.info(f"Alerta: {alert['message']}")

                    # Registrar recomendação
                    risk_logger.info(f"Recomendação: {risk_action}")

                    # Aplicar controlador de posições baseado em dinâmica quântica,
                    # se disponível
                    if hasattr(self, "quantum_risk_position_controller"):
                        try:
                            # Aplicar controlador de posições para gerenciamento
                            # ativo
                            active_positions = self.positions
                            if active_positions:
                                # Criar dados necessários para o controlador
                                price_action = {}
                                for position in active_positions:
                                    symbol = position.symbol
                                    if symbol in self.market_data:
                                        price_action[symbol] = {
                                            "recent_prices": self.market_data[symbol]
                                            .get("1m", {})
                                            .get("close", [])[-20:],
                                            "current_price": self._get_current_price(
                                                symbol
                                            ),
                                            "timestamp": datetime.now(
                                                timezone.utc
                                            ).isoformat(),
                                        }

                                # Obter recomendações do controlador para cada
                                # posição
                                position_adjustments = []
                                for position in active_positions:
                                    if position.symbol in price_action:
                                        try:
                                            # Obter recomendações de ajuste
                                            # dinâmico
                                            adjustment = self.quantum_risk_position_controller.dynamic_stop_management(
                                                position=position,
                                                risk_metrics=risk_metrics,
                                                price_action=price_action[
                                                    position.symbol
                                                ],
                                            )

                                            if adjustment:
                                                position_adjustments.append(adjustment)

                                                # Aplicar ajustes recomendados
                                                # (stops, alvos, etc.)
                                                if (
                                                    adjustment.get("adjust_stop_loss")
                                                    and "new_stop_loss" in adjustment
                                                ):
                                                    old_stop = position.stop_loss
                                                    position.stop_loss = adjustment[
                                                        "new_stop_loss"
                                                    ]
                                                    risk_logger.info(
                                                        f"Ajuste dinâmico de stop-loss para {position.symbol}: "
                                                        f"{old_stop or 'Nenhum'} → {adjustment['new_stop_loss']}"
                                                    )

                                                # Ajustar take-profit
                                                if (
                                                    adjustment.get("adjust_take_profit")
                                                    and "new_take_profit" in adjustment
                                                ):
                                                    old_target = position.take_profit
                                                    position.take_profit = adjustment[
                                                        "new_take_profit"
                                                    ]
                                                    risk_logger.info(
                                                        f"Ajuste dinâmico de take-profit para {position.symbol}: "
                                                        f"{old_target or 'Nenhum'} → {adjustment['new_take_profit']}"
                                                    )
                                        except Exception as e:
                                            logger.error(
                                                f"Erro ao aplicar ajuste dinâmico para {position.symbol}: {e}"
                                            )

                                # Registrar ajustes realizados
                                if position_adjustments:
                                    risk_metrics["position_adjustments"] = (
                                        position_adjustments
                                    )
                                    risk_logger.info(
                                        f"Realizados {len(position_adjustments)} ajustes dinâmicos de posições"
                                    )
                                else:
                                    risk_logger.info(
                                        "Nenhum ajuste dinâmico de posição necessário"
                                    )

                                # Avaliação de risco do portfólio completo
                                try:
                                    portfolio_assessment = self.quantum_risk_position_controller.portfolio_risk_assessment(
                                        positions=active_positions,
                                        risk_metrics=risk_metrics,
                                    )

                                    if portfolio_assessment:
                                        risk_metrics["portfolio_assessment"] = (
                                            portfolio_assessment
                                        )

                                        # Verificar se há recomendação de redução
                                        # de exposição
                                        if (
                                            portfolio_assessment.get("recommendation")
                                            == "reduce_exposure"
                                        ):
                                            reduction_pct = portfolio_assessment.get(
                                                "reduction_percentage", 0
                                            )
                                            if reduction_pct > 0:
                                                risk_logger.warning(
                                                    f"Recomendação de redução de exposição: {reduction_pct:.0f}% do portfólio"
                                                )

                                                # Implementar redução se risco for
                                                # muito alto
                                                if (
                                                    reduction_pct > 75
                                                    and risk_category
                                                    in ["ALTO", "EXTREMO"]
                                                ):
                                                    # Ordenar posições por tamanho
                                                    # (maiores primeiro)
                                                    positions_by_size = sorted(
                                                        active_positions,
                                                        key=lambda p: p.position_size
                                                        * p.current_price,
                                                        reverse=True,
                                                    )

                                                    # Fechar posições mais
                                                    # arriscadas
                                                    positions_to_close = positions_by_size[
                                                        : max(
                                                            1,
                                                            int(
                                                                len(positions_by_size)
                                                                * reduction_pct
                                                                / 100
                                                            ),
                                                        )
                                                    ]
                                                    for pos in positions_to_close:
                                                        try:
                                                            self._close_position(
                                                                pos,
                                                                reason="Redução estratégica de exposição (risco quântico)",
                                                            )
                                                            risk_logger.warning(
                                                                f"Posição fechada automaticamente: {pos.symbol}"
                                                            )
                                                        except Exception as e:
                                                            logger.error(
                                                                f"Erro ao fechar posição {pos.symbol}: {e}"
                                                            )
                                except Exception as e:
                                    logger.error(
                                        f"Erro na avaliação de risco do portfólio: {e}"
                                    )
                        except Exception as e:
                            logger.error(
                                f"Erro ao aplicar controlador de posições quântico: {e}"
                            )
                    risk_logger.info(
                        f"Análise de risco quântico concluída em {execution_time:.2f}s"
                    )
                    return risk_metrics
        except Exception as e:
            risk_logger.error(
                f"Erro ao usar calculador de métricas quânticas para análise de risco: {e}"
            )
            risk_logger.debug(traceback.format_exc())
            # Continuar para o método alternativo caso o calculador falhe
            # YAA: A lógica de fallback já existe abaixo, então não retornar/alterar fluxo aqui
            # return None # Removido para permitir que o código de fallback seja alcançado

        # Se o calculador de métricas quânticas não estiver disponível ou
        # falhar, tentar o método convencional
        if not self.quantum_risk_analyzer or not self.positions:
            risk_logger.warning(
                "Analisador de risco quântico não disponível ou sem posições. Usando método alternativo ou pulando análise de risco."  # YAA: Log ligeiramente ajustado
            )
            return {}

        start_time = time.time()
        risk_logger.info("Iniciando análise de risco quântico do portfólio...")

        try:
            # Preparar dados para análise de risco
            current_prices = {}
            for position in positions_snapshot:
                symbol = position.symbol
                if position.current_price:
                    current_prices[symbol] = position.current_price
                else:
                    # Fallback: tentar buscar o preço atual via método
                    price = self._get_current_price(symbol)
                    if price is not None:
                        current_prices[symbol] = price

            # Verificar se temos preços para todas as posições
            if len(current_prices) < len(positions_snapshot):
                missing_symbols = [
                    p.symbol
                    for p in positions_snapshot
                    if p.symbol not in current_prices
                ]
                risk_logger.warning(
                    f"Preços ausentes para: {', '.join(missing_symbols)}. Análise de risco será parcial."
                )

            # Definir portfólio atual para análise de risco
            self.quantum_risk_analyzer.set_portfolio(positions_snapshot, current_prices)

            # Atualizar dados históricos (fundamentais para análise quântica
            # precisa)
            self.quantum_risk_analyzer.update_historical_data(self.market_data)

            # Se temos universo quântico ativo, configurá-lo para análise de
            # risco otimizada
            if self.quantum_universe and hasattr(
                self.quantum_risk_analyzer, "quantum_universe"
            ):
                # Passar referência do universo quântico para o analisador de
                # risco
                self.quantum_risk_analyzer.quantum_universe = self.quantum_universe

                # Configurar universo quântico para análise específica de risco
                if hasattr(self.quantum_universe, "thermal"):
                    # Usar modo térmico para melhor modelar comportamentos
                    # caóticos de mercado
                    self.quantum_universe.thermal = True

            # Calcular métricas de risco avançadas
            risk_metrics = self.quantum_risk_analyzer.calculate_risk_metrics()

            # Adicionar métricas de mercado atuais
            risk_metrics["market_state"] = self._get_current_market_metrics()

            # Adicionar métricas QUALIA específicas
            qualia_metrics = self._get_qualia_specific_risk_metrics()
            if qualia_metrics:
                # Integrar métricas QUALIA ao resultado
                if "quantum_metrics" not in risk_metrics:
                    risk_metrics["quantum_metrics"] = {}

                # Adicionar métricas de risco específicas do QUALIA
                for key, value in qualia_metrics.items():
                    risk_metrics["quantum_metrics"][f"qualia_{key}"] = value

            # Calcular indicadores de risco baseados nas métricas quânticas
            # Usando pesos específicos para cada componente
            risk_level = 0
            risk_components = []
            risk_weights = []

            # 1. Componente de VaR clássico
            var_value = risk_metrics.get("var_1d", {}).get("var", {}).get("0.99", 0)
            capital = (
                self.risk_manager.get_current_capital() if self.risk_manager else 100000
            )
            var_pct = var_value / capital * 100 if capital > 0 else 0

            # Normalizar VaR para pontuação de 0-100
            # VaR de 25% = score 100 (risco máximo)
            var_score = min(100, var_pct * 4)
            risk_components.append(var_score)
            risk_weights.append(0.3)  # Peso de 30% para o componente clássico

            # 2. Componente de entropia quântica
            entropy = risk_metrics.get("quantum_entropy", 0)
            entropy_score = entropy * 100  # Entropia de 1.0 = score 100
            risk_components.append(entropy_score)
            risk_weights.append(0.15)  # Peso de 15%

            # 3. Componente de instabilidade quântica (se disponível)
            if (
                "quantum_metrics" in risk_metrics
                and "quantum_instability" in risk_metrics["quantum_metrics"]
            ):
                instability = risk_metrics["quantum_metrics"]["quantum_instability"]
                instability_score = instability * 100
                risk_components.append(instability_score)
                risk_weights.append(0.2)  # Peso de 20%

            # 4. Componente de risco de eventos extremos
            extreme_prob = 0
            if (
                "quantum_metrics" in risk_metrics
                and "extreme_event_probability" in risk_metrics["quantum_metrics"]
            ):
                extreme_prob = risk_metrics["quantum_metrics"][
                    "extreme_event_probability"
                ]
                # Probabilidade de 15% ou mais = score 100
                extreme_score = min(100, extreme_prob * 100 * (100 / 15))
                risk_components.append(extreme_score)
                risk_weights.append(0.25)  # Peso de 25%

            # 5. Componente de risco de correlação de mercado
            market_chaos = 0
            if (
                "quantum_metrics" in risk_metrics
                and "market_chaos" in risk_metrics["quantum_metrics"]
            ):
                market_chaos = risk_metrics["quantum_metrics"]["market_chaos"]
                chaos_score = min(100, market_chaos * 100)
                risk_components.append(chaos_score)
                risk_weights.append(0.1)  # Peso de 10%

            # Calcular pontuação de risco ponderada (0-100)
            if risk_components and sum(risk_weights) > 0:
                weighted_scores = [
                    score * weight
                    for score, weight in zip(risk_components, risk_weights)
                ]
                risk_level = sum(weighted_scores) / sum(risk_weights)
            else:
                # Se não temos componentes suficientes, usar cálculo
                # simplificado
                risk_level = max(30, min(80, var_pct * 4 + entropy * 40))

            # Determinar categoria de risco e recomendação
            # Thresholds e categorias baseados em análises empíricas do
            # framework QUALIA
            if risk_level < 25:
                risk_category = "BAIXO"
                risk_action = "Manter estratégia normal"
                alert_level = "info"
            elif risk_level < 50:
                risk_category = "MODERADO"
                risk_action = "Monitorar de perto, sem ajustes imediatos"
                alert_level = "info"
            elif risk_level < 75:
                risk_category = "ELEVADO"
                risk_action = "Considerar redução de tamanho de posições em 25-50%"
                alert_level = "warning"
            elif risk_level < 90:
                risk_category = "ALTO"
                risk_action = "Reduzir exposição em 50-75% e aumentar stops"
                alert_level = "danger"
            else:
                risk_category = "EXTREMO"
                risk_action = "Considerar fechamento de todas as posições imediatamente"
                alert_level = "danger"

            # Armazenar categorização de risco
            risk_metrics["risk_level"] = risk_level
            risk_metrics["risk_category"] = risk_category
            risk_metrics["recommended_action"] = risk_action
            risk_metrics["alert_level"] = alert_level

            # Determinar se temos um alerta de risco extremo
            extreme_risk = risk_level >= 75  # Nível de risco alto ou extremo
            risk_metrics["extreme_risk_alert"] = extreme_risk

            # Registrar alertas específicos
            risk_alerts = []

            # Alertas baseados em métricas quânticas
            if entropy > 0.8:
                risk_alerts.append(
                    {
                        "type": "quantum_entropy",
                        "message": f"Alta entropia quântica ({entropy:.4f}) indica mercado caótico e imprevisível",
                        "severity": "high",
                    }
                )

            if extreme_prob > 0.10:
                risk_alerts.append(
                    {
                        "type": "extreme_events",
                        "message": f"Probabilidade elevada de eventos extremos ({extreme_prob:.1%}) detectada",
                        "severity": "high" if extreme_prob > 0.15 else "medium",
                    }
                )

            if market_chaos > 0.6:
                risk_alerts.append(
                    {
                        "type": "market_chaos",
                        "message": f"Alto nível de caos quântico de mercado ({market_chaos:.2f}) - correlações instáveis",
                        "severity": "high" if market_chaos > 0.8 else "medium",
                    }
                )

            # Alerta baseado em VaR
            if var_pct > 15:
                risk_alerts.append(
                    {
                        "type": "var_level",
                        "message": f"VaR elevado: {var_pct:.1f}% do capital em risco (limiar: 15%)",
                        "severity": "high" if var_pct > 20 else "medium",
                    }
                )

            # Verificar impacto de testes de estresse
            stress_impact = (
                risk_metrics.get("stress_test", {}).get("impact_pct", {}).get("0.99", 0)
            )
            if stress_impact > 50:
                risk_alerts.append(
                    {
                        "type": "stress_test",
                        "message": f"Impacto crítico em teste de estresse: perdas de até {stress_impact:.1f}% possíveis",
                        "severity": "high" if stress_impact > 70 else "medium",
                    }
                )

            # Adicionar alertas ao resultado
            risk_metrics["risk_alerts"] = risk_alerts

            # Armazenar métricas na memória do sistema
            self.quantum_metrics["risk"] = risk_metrics

            # Registrar principais métricas no log
            risk_logger.info(f"VaR (99%): ${var_value:.2f} ({var_pct:.2f}% do capital)")
            risk_logger.info(
                f"Entropia Quântica: {entropy:.4f}, Prob. Eventos Extremos: {extreme_prob:.2%}"
            )
            risk_logger.info(
                f"Nível de Risco: {risk_level:.1f}/100 - Categoria: {risk_category}"
            )

            # Registrar alertas no log se houver
            if risk_alerts:
                for alert in risk_alerts:
                    if alert["severity"] == "high":
                        risk_logger.warning(f"ALERTA CRÍTICO: {alert['message']}")
                    else:
                        risk_logger.info(f"Alerta: {alert['message']}")

            # Registrar recomendação
            risk_logger.info(f"Recomendação: {risk_action}")

            # Integrar análise do controlador de posições baseado em dinâmica
            # quântica
            if (
                hasattr(self, "quantum_risk_position_controller")
                and self.quantum_risk_position_controller
            ):
                try:
                    # Preparar avaliação de risco do portfólio completo
                    portfolio_assessment = (
                        self.quantum_risk_position_controller.portfolio_risk_assessment(
                            positions=self.positions, risk_metrics=risk_metrics
                        )
                    )

                    if portfolio_assessment:
                        # Adicionar avaliação do portfólio às métricas de risco
                        risk_metrics["portfolio_quantum_assessment"] = (
                            portfolio_assessment
                        )

                        # Registrar recomendações do controlador
                        if "recommendations" in portfolio_assessment:
                            recommendations = portfolio_assessment["recommendations"]

                            risk_logger.info(
                                "Análise de posições com dinâmica quântica:"
                            )
                            risk_logger.info(
                                f"Exposição ideal: {recommendations.get('ideal_exposure_pct', 0):.1f}%"
                            )
                            risk_logger.info(
                                f"Nível de hedge recomendado: {recommendations.get('hedge_level', 0):.2f}"
                            )

                            # Adicionar alertas específicos de posição (se
                            # houver)
                            if "position_alerts" in portfolio_assessment:
                                for alert in portfolio_assessment["position_alerts"]:
                                    risk_logger.warning(
                                        f"Alerta de posição: {alert.get('message', '')}"
                                    )

                            # Se houver alerta de extremo, aumentar nível de
                            # risco
                            if portfolio_assessment.get(
                                "extreme_condition_detected", False
                            ):
                                risk_metrics["extreme_risk_alert"] = True
                                risk_metrics["risk_level"] = max(
                                    85, risk_metrics["risk_level"]
                                )
                                risk_logger.warning(
                                    "Controlador de dinâmica quântica detectou condição extrema!"
                                )

                        # Verificar dinâmica de emergência de risco
                        if "risk_emergence_dynamics" in portfolio_assessment:
                            dynamics = portfolio_assessment["risk_emergence_dynamics"]

                            if dynamics.get("acceleration", 0) > 0.7:
                                risk_logger.warning(
                                    f"Aceleração rápida de risco detectada: {dynamics.get('acceleration', 0):.2f}"
                                )

                            if dynamics.get("non_linearity", 0) > 0.5:
                                risk_logger.warning(
                                    f"Comportamento não-linear de risco detectado: {dynamics.get('non_linearity', 0):.2f}"
                                )

                except Exception as e:
                    risk_logger.error(
                        f"Erro ao utilizar controlador de posições baseado em dinâmica quântica: {e}"
                    )
                    logger.debug(traceback.format_exc())

            # Registrar tempo de execução
            execution_time = time.time() - start_time
            risk_metrics["execution_time"] = execution_time
            risk_logger.info(
                f"Análise de risco quântico concluída em {execution_time:.2f}s"
            )

            return risk_metrics

        except Exception as e:
            logger.error(f"Erro ao calcular métricas de risco quântico: {e}")
            risk_logger.error(f"Falha na análise de risco quântico: {e}")
            logger.debug(traceback.format_exc())
            return {}

    def _get_current_market_metrics(self) -> Dict[str, Any]:
        """
        Obtém métricas atuais de mercado para complementar a análise de risco.

        Returns:
            Dicionário com métricas de mercado atuais
        """
        metrics = {}

        try:
            # Coletar volatilidades recentes por símbolo
            volatilities = {}
            for symbol, timeframes in self.market_data.items():
                if "5m" in timeframes and "close" in timeframes["5m"]:
                    # Últimos 30 pontos
                    prices = timeframes["5m"]["close"][-30:]
                    if len(prices) >= 10:
                        price_array = np.asarray(prices, dtype=float)
                        returns = np.diff(price_array) / price_array[:-1]
                        vol = np.std(returns) * np.sqrt(252 * 24 * 12)
                        volatilities[symbol] = vol

            metrics["volatilities"] = volatilities

            # Calcular volatilidade média do mercado
            if volatilities:
                metrics["avg_volatility"] = float(np.mean(list(volatilities.values())))

            # Detectar tendência recente do mercado (média de todos os
            # símbolos)
            momentum = {}
            for symbol, timeframes in self.market_data.items():
                if "15m" in timeframes and "close" in timeframes["15m"]:
                    # Últimos 20 pontos
                    prices = timeframes["15m"]["close"][-20:]
                    if len(prices) >= 10:
                        # Usar média móvel simples para tendência
                        sma5 = np.mean(prices[-5:])
                        sma20 = np.mean(prices)
                        momentum[symbol] = sma5 / sma20 - 1  # Normalizado

            if momentum:
                metrics["avg_momentum"] = float(np.mean(list(momentum.values())))
                metrics["momentum_by_symbol"] = momentum

            # Registrar timestamp
            metrics["timestamp"] = datetime.now(timezone.utc).isoformat()

        except Exception as e:
            logger.error(f"Erro ao calcular métricas de mercado: {e}")

        return metrics

    def _get_qualia_specific_risk_metrics(self) -> Dict[str, Any]:
        """
        Obtém métricas de risco específicas do framework QUALIA.

        Returns:
            Dicionário com métricas de risco específicas do QUALIA
        """
        metrics = {}

        try:
            # Verificar se temos universo quântico inicializado
            if not self.quantum_universe:
                return metrics

            # Obter métricas específicas do QUALIA, como entropia em Hawking-Page, etc.
            # Estas são métricas únicas do framework QUALIA

            # Entropia de Hawking-Page (métrica específica do QUALIA para
            # transições de fase quânticas)
            if hasattr(self.quantum_universe, "calculate_hawking_page_entropy"):
                try:
                    hp_entropy = self.quantum_universe.calculate_hawking_page_entropy()
                    metrics["hawking_page_entropy"] = float(hp_entropy)
                except BaseException as exc:
                    logger.exception(
                        "Erro ao calcular hawking_page_entropy: %s",
                        exc,
                    )

            # Indicador de anomalias quânticas (detecção de padrões fora do
            # comum)
            if hasattr(self.quantum_universe, "get_anomaly_score"):
                try:
                    anomaly_score = self.quantum_universe.get_anomaly_score()
                    metrics["anomaly_score"] = float(anomaly_score)
                except BaseException as exc:
                    logger.exception(
                        "Erro ao obter anomaly_score: %s",
                        exc,
                    )

            # Coeficiente de inovação quântica (mede novidade/surpresa no
            # sistema)
            if hasattr(self.quantum_universe, "get_innovation_coefficient"):
                try:
                    innovation = self.quantum_universe.get_innovation_coefficient()
                    metrics["innovation_coefficient"] = float(innovation)
                except BaseException as exc:
                    logger.exception(
                        "Erro ao calcular innovation_coefficient: %s",
                        exc,
                    )

            # Indicador de transições de fase quânticas (mudanças abruptas no
            # mercado)
            if hasattr(self.quantum_universe, "detect_phase_transition"):
                try:
                    phase_transition = self.quantum_universe.detect_phase_transition()
                    if isinstance(phase_transition, dict):
                        metrics["phase_transition"] = phase_transition
                    else:
                        metrics["phase_transition_score"] = float(phase_transition)
                except BaseException as exc:
                    logger.exception(
                        "Erro ao detectar phase_transition: %s",
                        exc,
                    )

        except Exception as e:
            logger.error(f"Erro ao calcular métricas específicas QUALIA: {e}")

        return metrics

    def _calculate_position_size(self, symbol: str, signal: Dict[str, Any]) -> float:
        """
        Calcula o tamanho da posição com base no gerenciador de risco.

        Args:
            symbol: Símbolo para o qual calcular a posição
            signal: Sinal de trading gerado

        Returns:
            Tamanho da posição em USD
        """
        try:
            # Obter preço atual e stop loss do sinal
            current_price = signal.get("entry_price") or self._get_current_price(symbol)
            if not current_price:
                logger.warning(
                    f"Sem preço disponível para {symbol}, não foi possível calcular tamanho da posição"
                )
                return 0

            stop_loss_price = signal.get("stop_loss")
            if not stop_loss_price:
                # Se sinal não incluir stop loss, usar o padrão de X% (baseado
                # no perfil de risco)
                stop_pct = self.strategy.stop_loss_pct
                action_normalized = signal.get("action", "").lower()
                if action_normalized == "buy":
                    stop_loss_price = current_price * (1 - stop_pct / 100)
                else:  # sell
                    stop_loss_price = current_price * (1 + stop_pct / 100)

            # Obter volatilidade do sinal ou calcular
            volatility = signal.get("indicators", {}).get("volatility")

            # Calcular tamanho da posição via gerenciador de risco
            position_result = self.risk_manager.calculate_position_size(
                symbol=symbol,
                current_price=current_price,
                stop_loss_price=stop_loss_price,
                confidence=signal.get("confidence", 0.5),
                volatility=volatility,
                informational_mass=getattr(
                    self.quantum_universe, "informational_mass", None
                ),
                initial_informational_mass=getattr(
                    self.quantum_universe, "initial_informational_mass", None
                ),
                lambda_factor=getattr(
                    self.quantum_universe, "lambda_factor_multiplier", 1.0
                ),
            )

            # Verificar se posição é permitida
            if not position_result.get("position_allowed", False):
                logger.warning(
                    f"Posição em {symbol} não permitida: {position_result.get('reason')}"
                )
                return 0

            # Obter tamanho da posição inicial
            position_size = position_result.get("position_size", 0)

            # Otimizar o dimensionamento de posição com o controlador quântico,
            # se disponível
            if (
                hasattr(self, "quantum_risk_position_controller")
                and self.quantum_risk_position_controller
            ):
                try:
                    # Prepara os dados para o controlador
                    # 1. Obter métricas de risco quântico
                    risk_metrics = {}
                    if (
                        hasattr(self, "quantum_metrics")
                        and self.quantum_metrics
                        and "risk" in self.quantum_metrics
                    ):
                        risk_metrics = self.quantum_metrics["risk"]

                    # 2. Criar uma estrutura de posição para análise
                    action_normalized = signal.get("action", "").lower()
                    if action_normalized not in ["buy", "sell"]:
                        logger.warning(
                            f"Ação não reconhecida '{signal.get('action')}' para {symbol}"
                        )
                    # 3. Calcular posição otimizada com dinâmica quântica
                    optimal_position = self.quantum_risk_position_controller.calculate_optimal_position(
                        symbol=symbol, signal=signal, risk_metrics=risk_metrics
                    )

                    if optimal_position and isinstance(optimal_position, dict):
                        # Obter tamanho da posição otimizado
                        quantum_position_size = optimal_position.get("position_size")

                        if (
                            quantum_position_size is not None
                            and quantum_position_size > 0
                        ):
                            # Registrar ajuste no log
                            adjustment_ratio = (
                                quantum_position_size / position_size
                                if position_size > 0
                                else 0
                            )
                            adjusted_text = (
                                "aumentado" if adjustment_ratio > 1 else "reduzido"
                            )

                            logger.info(
                                f"Posição em {symbol} {adjusted_text} via controlador quântico: "
                                f"${position_size:.2f} → ${quantum_position_size:.2f} "
                                f"({abs(adjustment_ratio - 1) * 100:.1f}% {adjusted_text})"
                            )

                            # Adicionar razão para o ajuste, se disponível
                            if "adjustment_reason" in optimal_position:
                                logger.info(
                                    f"Razão: {optimal_position['adjustment_reason']}"
                                )

                            # Atualizar o tamanho da posição para o valor
                            # otimizado
                            position_size = quantum_position_size

                except Exception as e:
                    logger.warning(
                        f"Erro ao otimizar posição com controlador quântico: {e}"
                    )
                    logger.debug(traceback.format_exc())

            # Converter para quantidade
            quantity = position_size / current_price if current_price > 0 else 0

            logger.info(
                f"Tamanho calculado para {symbol}: ${position_size:.2f} ({quantity:.8f} unidades)"
            )

            return position_size

        except Exception as e:
            logger.error(f"Erro ao calcular tamanho da posição para {symbol}: {e}")
            return 0

    def _add_to_position(
        self, position: Position, additional_size: float, signal: Dict[str, Any]
    ) -> bool:
        """Aumenta uma posição existente consolidando micro-lotes."""

        symbol = position.symbol
        current_price = signal.get("entry_price") or self._get_current_price(symbol)
        if not current_price or additional_size <= 0:
            return False

        quantity = additional_size / current_price if current_price > 0 else 0
        if quantity <= 0:
            return False

        order_result = None
        if self.live_mode:
            order_result = self.exchange.create_order(
                pair=symbol.replace("/", ""),
                side=position.side,
                order_type="market",
                volume=quantity,
            )
            if hasattr(self, "metacognition_system") and self.metacognition_system:
                last_signal = self.metacognition_system.get_last_trade_signal()
                if (
                    last_signal
                    and getattr(last_signal, "signal_type", "") == "REDUCE_EXPOSURE"
                ):
                    try:
                        self.exchange.cancel_order(
                            order_result.get("txid", ["unknown"])[0]
                        )
                    except Exception as exc:
                        logger.exception(
                            "Erro ao cancelar ordem %s: %s",
                            order_result.get("txid", ["unknown"])[0],
                            exc,
                        )
                    return False
        else:
            order_result = {
                "txid": [f"sim-add-{int(time.time())}"],
                "simulation": True,
            }

        total_qty = position.quantity + quantity
        position.entry_price = (
            position.entry_price * position.quantity + current_price * quantity
        ) / total_qty
        position.quantity = total_qty
        position.position_size += additional_size
        position.order_id = order_result.get("txid", [position.order_id or "unknown"])[
            0
        ]
        logger.info(
            f"Posição em {symbol} ajustada em +{quantity:.8f} unidades ({additional_size:.2f})"
        )
        return True

    def _open_position(
        self, symbol: str, side: str, position_size: float, signal: Dict[str, Any]
    ) -> bool:
        """
        Abre uma nova posição.

        Args:
            symbol: Símbolo para abrir posição
            side: 'buy' ou 'sell'
            position_size: Tamanho da posição em USD
            signal: Sinal de trading que originou a posição

        Returns:
            True se posição aberta com sucesso, False caso contrário
        """
        try:
            # Obter preço atual
            current_price = signal.get("entry_price") or self._get_current_price(symbol)
            if not current_price:
                logger.warning(
                    f"Sem preço disponível para {symbol}, não foi possível abrir posição"
                )
                return False

            # Calcular quantidade
            quantity = position_size / current_price if current_price > 0 else 0
            if quantity <= 0:
                logger.warning(f"Quantidade calculada para {symbol} é zero ou negativa")
                return False

            entry_fee = current_price * quantity * self.trading_fee_pct

            if self.risk_manager:
                self.risk_manager.update_capital(
                    self.risk_manager.current_capital - entry_fee
                )

            # Executar ordem no exchange (se em modo live)
            order_result = None
            order_start_time = datetime.now(timezone.utc)
            if self.live_mode:
                try:
                    # Abrir ordem de mercado
                    order_result = self.exchange.create_order(
                        pair=symbol.replace("/", ""),
                        side=side,
                        order_type="market",
                        volume=quantity,
                    )

                    logger.info(
                        f"Ordem executada: {side} {quantity:.8f} {symbol} @ mercado"
                    )
                    if (
                        hasattr(self, "metacognition_system")
                        and self.metacognition_system
                    ):
                        last_signal = self.metacognition_system.get_last_trade_signal()
                        if (
                            last_signal
                            and getattr(last_signal, "signal_type", "")
                            == "REDUCE_EXPOSURE"
                            and getattr(last_signal, "timestamp", datetime.min)
                            >= order_start_time
                        ):
                            logger.warning(
                                "Gate de Execução: REDUCE_EXPOSURE detectado após envio da ordem. Cancelando."
                            )
                            try:
                                self.exchange.cancel_order(
                                    order_result.get("txid", ["unknown"])[0]
                                )
                            except Exception as exc:
                                logger.exception(
                                    "Erro ao cancelar ordem %s após REDUCE_EXPOSURE: %s",
                                    order_result.get("txid", ["unknown"])[0],
                                    exc,
                                )
                            return False
                except Exception as e:
                    logger.error(f"Erro ao executar ordem para {symbol}: {e}")
                    return False
            else:
                # Em modo simulação, simular resultado da ordem
                order_result = {
                    "txid": [f"sim-{int(time.time())}"],
                    "descr": {
                        "order": f"{side} {quantity:.8f} {symbol} @ market",
                        "close": None,
                    },
                    "simulation": True,
                }
                logger.info(
                    f"[SIMULAÇÃO] Ordem executada: {side} {quantity:.8f} {symbol} @ mercado"
                )
                if hasattr(self, "metacognition_system") and self.metacognition_system:
                    last_signal = self.metacognition_system.get_last_trade_signal()
                    if (
                        last_signal
                        and getattr(last_signal, "signal_type", "") == "REDUCE_EXPOSURE"
                        and getattr(last_signal, "timestamp", datetime.min)
                        >= order_start_time
                    ):
                        logger.warning(
                            "Gate de Execução: REDUCE_EXPOSURE detectado após envio da ordem (simulação). Abortando"
                        )
                        return False

            # Criar registro da posição
            position = Position(
                symbol=symbol,
                side=side,
                quantity=quantity,
                entry_price=current_price,
                fees_paid=entry_fee,
                current_price=current_price,
                entry_time=datetime.now(timezone.utc).isoformat(),
                order_id=(
                    order_result.get("txid", ["unknown"])[0]
                    if order_result
                    else "unknown"
                ),
                target_price=signal.get("target_price"),
                stop_loss=signal.get("stop_loss"),
                unrealized_pnl=0.0,
                unrealized_pnl_pct=0.0,
                duration=0,
                position_size=position_size,
                signal_confidence=signal.get("confidence", 0),
                reasons=signal.get("reasons", []),
            )

            # Adicionar à lista de posições
            with self._pos_lock:
                self.positions.append(position)

            logger.info(
                f"Posição aberta: {side} {symbol}, tamanho: ${position_size:.2f}, preço: {current_price:.2f}"
            )

            # Registrar a decisão no sistema de metacognição quântica, se
            # disponível
            if hasattr(self, "metacognition_system") and self.metacognition_system:
                try:
                    # Preparar dados da decisão
                    decision_data = {
                        "action": side,
                        "symbol": symbol,
                        "price": current_price,
                        "position_size": position_size,
                        "confidence": signal.get("confidence", 0.5),
                        "reasons": signal.get("reasons", []),
                        "stop_loss": signal.get("stop_loss"),
                        "target_price": signal.get("target_price"),
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                    }

                    # Preparar dados de contexto de mercado
                    market_context = {
                        "volatility": signal.get("indicators", {}).get("volatility", 0),
                        "trend_strength": signal.get("indicators", {}).get(
                            "trend_strength", 0
                        ),
                        "relative_volume": signal.get("indicators", {}).get(
                            "volume_ratio", 1.0
                        ),
                        "market_regime": signal.get("market_regime", "unknown"),
                    }

                    # Obter estado quântico atual se disponível
                    quantum_state = None
                    if self.quantum_universe and hasattr(
                        self.quantum_universe, "get_current_state"
                    ):
                        quantum_state = self.quantum_universe.get_current_state()
                    elif "quantum_metrics" in signal:
                        quantum_state = signal.get("quantum_metrics")

                    # Registrar a decisão no sistema de metacognição
                    decision_id = self.metacognition_system.register_trading_decision(
                        decision=decision_data,
                        context=market_context,
                        quantum_state=quantum_state,
                    )

                    # Armazenar o ID da decisão metacognitiva para referência
                    # futura
                    position.metacognition_decision_id = decision_id
                    logger.debug(
                        f"Decisão registrada no sistema de metacognição: ID={decision_id}"
                    )

                except Exception as e:
                    logger.warning(
                        f"Erro ao registrar decisão no sistema de metacognição: {e}"
                    )

            return True

        except Exception as e:
            logger.error(f"Erro ao abrir posição em {symbol}: {e}")
            return False

    def _close_position(
        self,
        position: Position,
        reason: str = "Fechamento manual",
        quantity: Optional[float] = None,
    ) -> bool:
        """
        Fecha uma posição existente.

        Args:
            position: Instância de :class:`Position`
            reason: Razão para o fechamento

        Returns:
            True se posição fechada com sucesso, False caso contrário
        """
        try:
            symbol = position.symbol
            # Lado oposto para fechar
            side = "sell" if position.side == "buy" else "buy"
            orig_qty = position.quantity
            qty_to_close = quantity if quantity is not None else orig_qty
            qty_to_close = min(qty_to_close, orig_qty)

            # Obter preço atual
            current_price = self._get_current_price(symbol)
            if not current_price:
                logger.warning(
                    f"Sem preço disponível para {symbol}, usando último preço conhecido"
                )
                current_price = position.current_price or position.entry_price

            # Executar ordem no exchange (se em modo live)
            order_result = None
            if self.live_mode:
                try:
                    # Fechar ordem de mercado
                    order_result = self.exchange.create_order(
                        pair=symbol.replace("/", ""),
                        side=side,
                        order_type="market",
                        volume=qty_to_close,
                    )

                    logger.info(
                        f"Posição fechada: {side} {qty_to_close:.8f} {symbol} @ mercado"
                    )
                except Exception as e:
                    logger.error(f"Erro ao fechar posição para {symbol}: {e}")
                    return False
            else:
                # Em modo simulação, simular resultado da ordem
                order_result = {
                    "txid": [f"sim-close-{int(time.time())}"],
                    "descr": {
                        "order": f"{side} {qty_to_close:.8f} {symbol} @ market",
                        "close": None,
                    },
                    "simulation": True,
                }
                logger.info(
                    f"[SIMULAÇÃO] Posição fechada: {side} {qty_to_close:.8f} {symbol} @ mercado"
                )

            # Calcular P&L realizado
            if position.side == "buy":
                realized_pnl = (current_price - position.entry_price) * qty_to_close
                realized_pnl_pct = ((current_price / position.entry_price) - 1) * 100
            else:  # sell
                realized_pnl = (position.entry_price - current_price) * qty_to_close
                realized_pnl_pct = ((position.entry_price / current_price) - 1) * 100

            entry_fee = position.fees_paid * (qty_to_close / orig_qty)
            exit_fee = current_price * qty_to_close * self.trading_fee_pct
            realized_pnl_net = realized_pnl - exit_fee

            # Criar registro de trade
            trade = {
                "symbol": symbol,
                "side": position.side,
                "quantity": qty_to_close,
                "entry_price": position.entry_price,
                "exit_price": current_price,
                "entry_time": position.entry_time,
                "exit_time": datetime.now(timezone.utc).isoformat(),
                "duration": (
                    datetime.now(timezone.utc)
                    - datetime.fromisoformat(position.entry_time)
                ).total_seconds(),
                "realized_pnl": realized_pnl,
                "realized_pnl_pct": realized_pnl_pct,
                "entry_fee": entry_fee,
                "exit_fee": exit_fee,
                "fees": entry_fee + exit_fee,
                "realized_pnl_net": realized_pnl_net,
                "position_size": position.position_size,
                "exit_reason": reason,
                "order_id": (
                    order_result.get("txid", ["unknown"])[0]
                    if order_result
                    else "unknown"
                ),
                "close_order_id": (
                    order_result.get("txid", ["unknown"])[0]
                    if order_result
                    else "unknown"
                ),
            }

            with self._pos_lock:
                self.trade_history.append(trade)

                if qty_to_close >= orig_qty:
                    self.positions = [p for p in self.positions if p.symbol != symbol]
                    log_msg = "removido"
                else:
                    position.quantity -= qty_to_close
                    position.position_size -= position.entry_price * qty_to_close
                    position.fees_paid = max(0.0, position.fees_paid - entry_fee)
                    self.positions = [
                        p if p.symbol != symbol else position for p in self.positions
                    ]
                    log_msg = "parcialmente reduzido"
            logger.info(
                f"Registro interno da posição {symbol} {log_msg}. order_id={position.order_id} close_id={trade['close_order_id']}"
            )

            # Registrar PnL e atualizar métricas de performance
            if realized_pnl_net != 0:
                # Atualizar capital no gerenciador de risco
                trade_info = {
                    "realized_pnl": realized_pnl_net,
                    "entry_price": position.entry_price,
                    "exit_price": current_price,
                    "quantity": qty_to_close,
                    "symbol": symbol,
                    "side": position.side,
                }
                metrics = self.risk_manager.process_trade_result(trade_info)

                # Log de resultado
                pnl_str = f"{realized_pnl_net:.2f} ({realized_pnl_pct:+.2f}%)"
                logger.info(
                    f"Trade finalizado: {symbol} {position.side}, P&L: ${pnl_str}, Razão: {reason}"
                )

                # Log específico para gerenciamento de risco
                risk_logger.info(
                    f"Trade: {symbol} {position.side}, P&L: ${pnl_str}, Capital: ${metrics['capital']:.2f}"
                )

                kp, ki = load_coefficients()
                record_pid_performance(kp, ki, realized_pnl_net)

                # Atualizar resultado no sistema de metacognição quântica, se
                # disponível
                if (
                    hasattr(self, "metacognition_system")
                    and self.metacognition_system
                    and position.metacognition_decision_id
                ):
                    try:
                        # Determinar outcome com base no P&L
                        outcome = "profitable" if realized_pnl_net > 0 else "loss"
                        if (
                            abs(realized_pnl_pct) < 0.1
                        ):  # Menos de 0.1% é considerado breakeven
                            outcome = "breakeven"

                        # Preparar dados de performance
                        performance_data = {
                            "pnl": realized_pnl_net,
                            "pnl_pct": realized_pnl_pct,
                            "duration": (
                                datetime.now(timezone.utc)
                                - datetime.fromisoformat(position.entry_time)
                            ).total_seconds(),
                            "exit_price": current_price,
                            "exit_reason": reason,
                            "hold_bars": trade.get("hold_bars", 0),
                            "market_movement": trade.get("market_movement", 0),
                        }

                        # Atualizar a decisão no sistema de metacognição
                        updated = self.metacognition_system.update_decision_outcome(
                            decision_id=position.metacognition_decision_id,
                            outcome=outcome,
                            performance=performance_data,
                        )

                        if updated:
                            logger.debug(
                                f"Resultado da decisão atualizado no sistema de metacognição: ID={position.metacognition_decision_id}, Outcome={outcome}"
                            )
                        else:
                            logger.warning(
                                f"Não foi possível atualizar resultado da decisão no sistema de metacognição: ID={position.metacognition_decision_id}"
                            )

                    except Exception as e:
                        logger.warning(
                            f"Erro ao atualizar resultado no sistema de metacognição: {e}"
                        )

            return True

        except Exception as e:
            logger.error(f"Erro ao fechar posição em {position.symbol}: {e}")
            return False

    def _parse_reduction_pct(self, signal: Any, default: float = 50.0) -> float:
        """Wrapper para :func:`utils.parse_reduction_pct`."""

        from ..utils import parse_reduction_pct

        return parse_reduction_pct(signal, default)

    def _should_reduce_exposure(self, signal: Any) -> bool:
        """Wrapper around :func:`should_reduce_exposure` using current settings."""

        from ..utils.trading_calculations import should_reduce_exposure

        return should_reduce_exposure(
            signal,
            self.positions,
            self.REDUCE_EXPOSURE_MIN_CONFIDENCE,
            self.REDUCE_EXPOSURE_MIN_POSITION_AGE_MINUTES,
            allow_single_position=self.REDUCE_EXPOSURE_CLOSE_SINGLE_POSITION,
        )

    def _reduce_portfolio_exposure(self, reduction_pct: float) -> None:
        """Fecha posições até reduzir a exposição total em ``reduction_pct``."""

        if reduction_pct <= 0:
            return

        with self._pos_lock:
            if not self.positions:
                return
            positions_snapshot = list(self.positions)

        from ..utils.exposure_reduction import reduce_positions

        def value_fn(pos: Position) -> float:
            price = self._get_current_price(pos.symbol)
            if price is None:
                price = pos.current_price or pos.entry_price
            return pos.quantity * (price or 0)

        def qty_fn(pos: Position) -> float:
            return pos.quantity

        plan, total_value = reduce_positions(
            positions_snapshot, reduction_pct, value_fn, qty_fn
        )
        if total_value <= 0:
            return

        reduced_value = 0.0
        closed: List[str] = []
        for entry in plan:
            pos = entry.position
            qty = entry.quantity_to_close
            price = entry.price
            try:
                self._close_position(
                    pos,
                    reason="Redução de exposição (metacognição)",
                    quantity=None if qty >= pos.quantity else qty,
                )
                reduced_value += price * qty
                closed.append(pos.symbol)
                logger.debug("Posição reduzida automaticamente: %s", pos.symbol)
            except Exception as e:
                logger.error(f"Erro ao reduzir posição {pos.symbol}: {e}")

        if closed:
            actual_pct = (reduced_value / total_value) * 100.0
            logger.info(
                "Redução de exposição concluída em %.1f%%, %d posições ajustadas",
                actual_pct,
                len(closed),
            )

    def _close_positions_on_metacognition(self) -> None:
        """Fecha posições conforme últimas diretivas de metacognição."""
        if not getattr(self, "metacognition_system", None):
            return

        signal = self.metacognition_system.get_last_trade_signal()
        self._last_metacognitive_ctx = (
            self.metacognition_system.get_last_metacognitive_context()
        )

        if not signal:
            return

        sig_type = getattr(signal, "signal_type", "").upper()

        if sig_type in {"BUY", "SELL"}:
            with self._pos_lock:
                snapshot = list(self.positions)
            for pos in snapshot:
                if (
                    pos.symbol == getattr(signal, "symbol", None)
                    and pos.side != sig_type.lower()
                ):
                    self._close_position(pos, reason="Sinal contrário (metacognição)")

        if sig_type == "REDUCE_EXPOSURE":
            reduction_pct = self._parse_reduction_pct(
                signal, default=self.REDUCE_EXPOSURE_DEFAULT_PCT
            )
            confidence = getattr(signal, "confidence", 0.0)
            if self._should_reduce_exposure(signal):
                self._reduce_portfolio_exposure(reduction_pct)
            else:
                from ..utils.trading_calculations import (
                    get_max_position_age_minutes,
                )

                with self._pos_lock:
                    max_age = get_max_position_age_minutes(self.positions)
                logger.info(
                    "Confiança %.2f abaixo do limite %.2f e posições recentes (idade máxima %.1f m < %.1f m); ignorando redução",
                    confidence,
                    self.REDUCE_EXPOSURE_MIN_CONFIDENCE,
                    max_age,
                    self.REDUCE_EXPOSURE_MIN_POSITION_AGE_MINUTES,
                )

        if getattr(self, "risk_manager", None) and hasattr(
            self.risk_manager, "max_open_positions"
        ):
            with self._pos_lock:
                excess = len(self.positions) - self.risk_manager.max_open_positions
                snapshot_excess = list(self.positions)[:excess] if excess > 0 else []
            for pos in snapshot_excess:
                self._close_position(pos, reason="Limite de posições excedido")

        with self._pos_lock:
            empty = not self.positions
        if empty:
            ctx = None
            if self.metacognition_system:
                ctx = self.metacognition_system.get_last_metacognitive_context()
            if ctx and getattr(ctx, "trade_directive", None):
                logger.info(
                    "Todas as posições fechadas; limpando diretiva %s",
                    ctx.trade_directive,
                )
                ctx.trade_directive = None
            self._last_metacognitive_ctx = ctx

    def _check_metacognition_before_entry(self) -> bool:
        """Verifica diretivas de metacognição antes de abrir posição."""
        if not hasattr(self, "metacognition_system") or not self.metacognition_system:
            return True

        signal = self.metacognition_system.get_last_trade_signal()

        if not signal:
            return True

        sig_type = getattr(signal, "signal_type", "").upper()

        if sig_type == "REDUCE_EXPOSURE":
            reduction_pct = self._parse_reduction_pct(
                signal, default=self.REDUCE_EXPOSURE_DEFAULT_PCT
            )
            confidence = getattr(signal, "confidence", 0.0)
            logger.warning(
                f"Metacognição recomenda reduzir exposição em {reduction_pct:.0f}%"
            )
            if self._should_reduce_exposure(signal):
                self._reduce_portfolio_exposure(reduction_pct)
            else:
                from ..utils.trading_calculations import (
                    get_max_position_age_minutes,
                )

                with self._pos_lock:
                    max_age = get_max_position_age_minutes(self.positions)
                logger.info(
                    "Confiança %.2f abaixo do limite %.2f e posições recentes (idade máxima %.1f m < %.1f m); ignorando redução",
                    confidence,
                    self.REDUCE_EXPOSURE_MIN_CONFIDENCE,
                    max_age,
                    self.REDUCE_EXPOSURE_MIN_POSITION_AGE_MINUTES,
                )
            return False

        if sig_type not in {"BUY", "SELL"}:
            if sig_type in {"NO_SIGNAL", "HOLD"}:
                logger.info(
                    "Entrada descartada: último sinal de metacognição foi %s",
                    sig_type,
                )
            else:
                logger.debug(
                    "Sinal de metacognição %s não permite abertura de posição",
                    sig_type,
                )
            return False

        return True

    def _update_performance_metrics(self) -> None:
        """
        Atualiza métricas de performance do sistema.
        """
        try:
            # Obter métricas do gerenciador de risco
            risk_metrics = self.risk_manager.get_performance_metrics()

            # Adicionar métricas específicas do trading
            metrics = {
                **risk_metrics,
                "open_positions": len(self.positions),
                "total_trades": len(self.trade_history),
                "last_trade_time": (
                    self.trade_history[-1]["exit_time"] if self.trade_history else None
                ),
                "quantum_metrics": self.quantum_metrics,
            }

            # Adicionar P&L não realizado das posições abertas
            unrealized_pnl = sum(p.unrealized_pnl for p in self.positions)
            metrics["unrealized_pnl"] = unrealized_pnl

            # Adicionar métricas por par
            metrics["symbols"] = {}
            for symbol in self.symbols:
                symbol_trades = [t for t in self.trade_history if t["symbol"] == symbol]

                if symbol_trades:
                    symbol_pnl = sum(
                        t.get("realized_pnl_net", t["realized_pnl"])
                        for t in symbol_trades
                    )
                    win_trades = sum(
                        1
                        for t in symbol_trades
                        if (t.get("realized_pnl_net", t["realized_pnl"]) > 0)
                    )

                    metrics["symbols"][symbol] = {
                        "total_trades": len(symbol_trades),
                        "win_trades": win_trades,
                        "win_rate": (
                            win_trades / len(symbol_trades) if symbol_trades else 0
                        ),
                        "pnl": symbol_pnl,
                        "last_trade_time": (
                            symbol_trades[-1]["exit_time"] if symbol_trades else None
                        ),
                    }

            # Atualizar métricas globais
            self.performance_metrics = metrics

        except Exception as e:
            logger.error(f"Erro ao atualizar métricas de performance: {e}")

    def _count_trades_last_hour(self) -> int:
        """Conta quantos trades foram fechados na última hora."""

        threshold = datetime.now(timezone.utc) - timedelta(hours=1)
        count = 0
        for trade in self.trade_history:
            exit_time = trade.get("exit_time")
            if not exit_time:
                continue
            try:
                exit_dt = datetime.fromisoformat(exit_time)
            except ValueError:
                continue
            if exit_dt >= threshold:
                count += 1
        return count

    def manual_order(
        self,
        symbol: str,
        side: str,
        order_type: str,
        quantity: float,
        price: Optional[float] = None,
    ) -> Dict[str, Any]:
        """
        Executa uma ordem manual.

        Args:
            symbol: Símbolo para a ordem
            side: 'buy' ou 'sell'
            order_type: 'market' ou 'limit'
            quantity: Quantidade a comprar/vender
            price: Preço para orders limit (opcional)

        Returns:
            Dicionário com o resultado da ordem
        """
        try:
            if not self.exchange:
                return {"success": False, "message": "Exchange não inicializado"}

            # Verificar se símbolo é válido
            if symbol not in self.symbols:
                return {"success": False, "message": f"Símbolo inválido: {symbol}"}

            # Verificar tipo de ordem
            if order_type not in ["market", "limit"]:
                return {
                    "success": False,
                    "message": f"Tipo de ordem inválido: {order_type}",
                }

            # Verificar quantidade
            if quantity <= 0:
                return {"success": False, "message": f"Quantidade inválida: {quantity}"}

            # Verificar preço para ordens limit
            if order_type == "limit" and (price is None or price <= 0):
                return {
                    "success": False,
                    "message": "Preço é obrigatório para ordens limit",
                }

            # Executar ordem
            if self.live_mode:
                try:
                    # Criar ordem no exchange
                    order_result = self.exchange.create_order(
                        pair=symbol.replace("/", ""),
                        side=side,
                        order_type=order_type,
                        volume=quantity,
                        price=price if order_type == "limit" else None,
                    )

                    # Log
                    price_str = f"@ {price}" if order_type == "limit" else "@ market"
                    logger.info(
                        f"Ordem manual executada: {side} {quantity:.8f} {symbol} {price_str}"
                    )

                    return {
                        "success": True,
                        "message": f"Ordem {order_type} executada com sucesso",
                        "order": order_result,
                    }
                except Exception as e:
                    logger.error(f"Erro ao executar ordem manual: {e}")
                    return {
                        "success": False,
                        "message": f"Erro ao executar ordem: {str(e)}",
                    }
            else:
                # Modo simulação
                current_price = (
                    self._get_current_price(symbol) or 100
                )  # Preço default para simulação

                # Criar resultado simulado
                order_result = {
                    "txid": [f"sim-manual-{int(time.time())}"],
                    "descr": {
                        "order": f"{side} {quantity:.8f} {symbol} @ {price if order_type == 'limit' else 'market'}",
                        "close": None,
                    },
                    "simulation": True,
                }

                # Se for ordem de mercado, criar posição imediatamente
                if order_type == "market":
                    position_size = quantity * current_price

                    # Verificar se já existe posição para este símbolo
                    with self._pos_lock:
                        existing_position = next(
                            (p for p in self.positions if p.symbol == symbol), None
                        )

                    # Se já existe, fechar primeiro (simulando)
                    if existing_position:
                        self._close_position(existing_position, reason="Ordem manual")

                    # Criar posição
                    position = Position(
                        symbol=symbol,
                        side=side,
                        quantity=quantity,
                        entry_price=current_price,
                        current_price=current_price,
                        entry_time=datetime.now(timezone.utc).isoformat(),
                        order_id=order_result["txid"][0],
                        target_price=None,
                        stop_loss=None,
                        unrealized_pnl=0.0,
                        unrealized_pnl_pct=0.0,
                        duration=0,
                        position_size=position_size,
                        signal_confidence=1.0,
                        reasons=["Ordem manual"],
                        metadata={"manual": True},
                    )

                    # Adicionar à lista de posições
                    with self._pos_lock:
                        self.positions.append(position)

                # Log
                price_str = f"@ {price}" if order_type == "limit" else "@ market"
                logger.info(
                    f"[SIMULAÇÃO] Ordem manual: {side} {quantity:.8f} {symbol} {price_str}"
                )

                return {
                    "success": True,
                    "message": f"[SIMULAÇÃO] Ordem {order_type} executada com sucesso",
                    "order": order_result,
                }

        except Exception as e:
            logger.error(f"Erro ao processar ordem manual: {e}")
            return {"success": False, "message": f"Erro ao processar ordem: {str(e)}"}

    def close_position_manual(
        self, symbol: str, reason: str = "Fechamento manual"
    ) -> Dict[str, Any]:
        """
        Fecha uma posição manualmente.

        Args:
            symbol: Símbolo da posição a fechar
            reason: Razão para o fechamento

        Returns:
            Dicionário com o resultado da operação
        """
        try:
            # Buscar posição de forma thread-safe
            with self._pos_lock:
                position = next((p for p in self.positions if p.symbol == symbol), None)

            if not position:
                return {
                    "success": False,
                    "message": f"Nenhuma posição aberta para {symbol}",
                }

            # Fechar posição
            if self._close_position(position, reason=reason):
                return {
                    "success": True,
                    "message": f"Posição em {symbol} fechada com sucesso",
                }
            else:
                return {
                    "success": False,
                    "message": f"Erro ao fechar posição em {symbol}",
                }

        except Exception as e:
            logger.error(f"Erro ao fechar posição manualmente: {e}")
            return {"success": False, "message": f"Erro ao fechar posição: {str(e)}"}

    def close_all_positions(
        self, reason: str = "Fechamento manual de todas posições"
    ) -> Dict[str, Any]:
        """
        Fecha todas as posições abertas.

        Args:
            reason: Razão para o fechamento

        Returns:
            Dicionário com o resultado da operação
        """
        try:
            with self._pos_lock:
                if not self.positions:
                    return {
                        "success": True,
                        "message": "Nenhuma posição aberta para fechar",
                    }
                positions_snapshot = list(self.positions)

            results = []
            for (
                position
            ) in (
                positions_snapshot
            ):  # Copiar lista para permitir modificação durante loop
                success = self._close_position(position, reason=reason)
                results.append({"symbol": position.symbol, "success": success})

            # Verificar se todas foram fechadas
            with self._pos_lock:
                remaining = len(self.positions)
            if remaining == 0:
                return {
                    "success": True,
                    "message": "Todas as posições fechadas com sucesso",
                    "results": results,
                }
            else:
                return {
                    "success": False,
                    "message": "Algumas posições não puderam ser fechadas",
                    "results": results,
                    "remaining": remaining,
                }

        except Exception as e:
            logger.error(f"Erro ao fechar todas as posições: {e}")
            return {"success": False, "message": f"Erro ao fechar posições: {str(e)}"}

    def _monitor_system_health(self) -> None:
        """
        Monitora a saúde do sistema, incluindo uso de recursos, conexão com a exchange e desempenho.

        Este método é executado periodicamente para garantir que o sistema esteja operando dentro
        de parâmetros aceitáveis e tomar ações corretivas quando necessário.
        """
        try:
            # Coletar métricas do sistema
            cpu_percent = psutil.cpu_percent(interval=1)
            memory_usage = psutil.virtual_memory().percent
            disk_usage = psutil.disk_usage("/").percent

            # Verificar uso de memória
            if memory_usage > 90:
                logger.warning(
                    f"Uso alto de memória: {memory_usage}%",
                    extra={
                        "context": {
                            "cpu_percent": cpu_percent,
                            "disk_usage": disk_usage,
                            "process_memory": psutil.Process().memory_info().rss
                            / 1024
                            / 1024,  # MB
                        }
                    },
                )

                # Se o uso de memória estiver muito alto, tentar reduzir cache
                if (
                    memory_usage > 95
                    and hasattr(self, "strategy")
                    and hasattr(self.strategy, "clear_cache")
                ):
                    logger.info(
                        "Limpando cache da estratégia para reduzir uso de memória"
                    )
                    self.strategy.clear_cache()

            # Verificar conexão com a exchange
            if hasattr(self, "exchange") and self.exchange:
                try:
                    # Verificar conexão com a exchange (método hipotético)
                    if hasattr(self.exchange, "check_connection"):
                        connection_ok = self.exchange.check_connection()
                        if not connection_ok:
                            logger.error("Problema detectado na conexão com a exchange")
                except Exception as e:
                    logger.error(f"Erro ao verificar conexão com a exchange: {e}")

            # Verificar latência do sistema
            current_time = time.time()
            if hasattr(self, "_last_health_check"):
                time_since_last_check = current_time - self._last_health_check
                if (
                    time_since_last_check > self.health_check_interval * 1.5
                ):  # 50% de tolerância
                    logger.warning(
                        f"Atraso na verificação de saúde do sistema: {time_since_last_check:.2f}s",
                        extra={
                            "context": {"expected_interval": self.health_check_interval}
                        },
                    )

            # Atualizar último check
            self._last_health_check = current_time

            # Log periódico de saúde do sistema
            if int(time.time()) % 300 == 0:  # A cada 5 minutos
                logger.info(
                    "Status de saúde do sistema",
                    extra={
                        "context": {
                            "cpu_percent": cpu_percent,
                            "memory_usage": memory_usage,
                            "disk_usage": disk_usage,
                            "threads_alive": threading.active_count(),
                            "data_queue_size": self.data_queue.qsize(),
                            "signal_queue_size": self.signal_queue.qsize(),
                            "positions_count": (
                                len(self.positions) if hasattr(self, "positions") else 0
                            ),
                            "status": getattr(self, "status", "unknown"),
                        }
                    },
                )

        except Exception as e:
            logger.error(f"Erro no monitoramento de saúde do sistema: {e}")
            logger.debug(traceback.format_exc())

            # Se ocorrerem muitos erros consecutivos, pode indicar um problema
            # sério
            self._consecutive_errors = getattr(self, "_consecutive_errors", 0) + 1
            if self._consecutive_errors >= 3:  # Após 3 erros consecutivos
                logger.critical(
                    f"Múltiplos erros consecutivos no monitoramento de saúde ({self._consecutive_errors})",
                    extra={"context": {"last_error": str(e)}},
                )

                # Se estiver em modo live e com muitos erros, considerar parar
                # o sistema
                if self.live_mode and self._consecutive_errors >= 5:
                    logger.critical(
                        "Muitos erros consecutivos - Parando o sistema por segurança"
                    )
                    self.stop()

    async def _preload_data_for_strategy(self, strategy_config: Dict[str, Any]):
        """Pré-carrega dados necessários para uma estratégia."""
        symbol = strategy_config["symbol"]
        tf = strategy_config["timeframe"]
        limit = strategy_config.get("required_history", 100)

        logger.debug(f"Pré-carregando {limit} candles para {symbol}@{tf}...")
        try:
            await self.exchange.market_data.fetch_ohlcv(
                spec=MarketSpec(symbol=symbol, timeframe=tf), limit=limit
            )
            logger.info(f"Dados para {symbol}@{tf} pré-carregados.")
        except Exception:
            logger.exception(f"Falha ao pré-carregar dados para {symbol}@{tf}")

    async def _handle_new_candle(self, candle_data: Dict[str, Any]):
        """Processa uma nova vela e dispara a análise da estratégia."""
        # Implemente a lógica para processar uma nova vela aqui
        pass
