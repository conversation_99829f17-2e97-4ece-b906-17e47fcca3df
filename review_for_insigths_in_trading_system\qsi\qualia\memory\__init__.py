"""QUALIA memory module with lazy imports to avoid circular dependencies."""

from __future__ import annotations

import importlib
from typing import Any

__all__ = [
    "BaseMemory",
    "ExperienceReplay",
    "ShortTermMemory",
    "QuantumPatternMemory",
    "HolographicMemory",
    "MemorySystem",
    "MemoryService",
    "with_lock",
    "SimilarityMixin",
    "WarmStartManager",
    "PatternPersistence",
    "IntentMemory",
    "flatten_vector",
    "load_qpm_vectors",
    "compute_similarity_distribution",
    "STORE_EVENT",
    "StorePatternEvent",
    "publish_store_pattern",
    "register_store_handler",
    "get_qpm_instance",
]

_MODULE_MAP = {
    "BaseMemory": "qualia.memory.base_memory",
    "ExperienceReplay": "qualia.memory.experience_replay",
    "ShortTermMemory": "qualia.memory.short_term_memory",
    "QuantumPatternMemory": "qualia.memory.quantum_pattern_memory",
    "HolographicMemory": "qualia.memory.holographic_memory",
    "MemorySystem": "qualia.memory.system",
    "MemoryService": "qualia.memory.service",
    "with_lock": "qualia.memory.locking",
    "SimilarityMixin": "qualia.memory.similarity",
    "WarmStartManager": "qualia.memory.warmstart",
    "PatternPersistence": "qualia.memory.persistence",
    "IntentMemory": "qualia.memory.intent_memory",
    "flatten_vector": "qualia.memory.qpm_utils",
    "load_qpm_vectors": "qualia.memory.qpm_utils",
    "compute_similarity_distribution": "qualia.memory.qpm_utils",
    "STORE_EVENT": "qualia.memory.qpm_interface",
    "StorePatternEvent": "qualia.memory.qpm_interface",
    "publish_store_pattern": "qualia.memory.qpm_interface",
    "register_store_handler": "qualia.memory.qpm_interface",
    "get_qpm_instance": "qualia.memory.qpm_loader",
}


def __getattr__(name: str) -> Any:
    """Lazily import submodules on first access."""
    module_path = _MODULE_MAP.get(name)
    if module_path is None:
        raise AttributeError(f"module 'qualia.memory' has no attribute {name}")
    module = importlib.import_module(module_path)
    return getattr(module, name)
