"""IntentMemory - armazenamento sequencial de tokens.

Este módulo implementa um armazenamento leve inspirado em Transformers para
sequências de tokens produzidos pelo ``IntentioWaveletEngine``. A memória
mantém contagens de ocorrência de subsequências para prever o próximo token.
"""

from __future__ import annotations

from typing import Dict, List, Tuple


class IntentMemory:
    """Armazena sequências de tokens e prevê o próximo elemento."""

    def __init__(self, max_context: int = 3) -> None:
        """Inicializa a memória.

        Parameters
        ----------
        max_context
            Tamanho máximo do contexto considerado na previsão.
        """
        self.max_context = int(max_context)
        self._model: Dict[Tuple[str, ...], Dict[str, int]] = {}

    def store_sequence(self, tokens: List[str]) -> None:
        """Armazena ``tokens`` atualizando contagens internas."""
        normalized = [t.strip().lower() for t in tokens if t]
        for idx, token in enumerate(normalized):
            for ctx_len in range(1, min(self.max_context, idx) + 1):
                context = tuple(normalized[idx - ctx_len : idx])
                next_counts = self._model.setdefault(context, {})
                next_counts[token] = next_counts.get(token, 0) + 1

    def predict_next(self, tokens: List[str]) -> str:
        """Prevê o próximo token mais provável para ``tokens``."""
        if not tokens:
            return ""
        normalized = [t.strip().lower() for t in tokens if t]
        for ctx_len in range(min(self.max_context, len(normalized)), 0, -1):
            context = tuple(normalized[-ctx_len:])
            next_counts = self._model.get(context)
            if next_counts:
                return max(next_counts.items(), key=lambda item: item[1])[0]
        return ""


__all__ = ["IntentMemory"]
