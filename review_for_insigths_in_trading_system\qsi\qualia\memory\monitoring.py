"""Utilities for monitoring memory operations."""

from __future__ import annotations

import logging
from collections import deque
from dataclasses import dataclass, field
from typing import Deque

import numpy as np
from datadog import DogStatsd

from .event_bus import SimpleEventBus
from ..events import (
    MetricRecordedEvent,
    MemoryLatencyAlert,
    DiskUsageAlert,
)
import psutil

logger = logging.getLogger(__name__)


@dataclass
class MemoryMonitor:
    """Track memory write performance and system usage.

    Parameters
    ----------
    statsd : DogStatsd, optional
        Client used to emit metrics.
    max_samples : int, default 100
        Number of recent latency samples kept for percentile calculation.
    disk_path : str, default '/'
        Filesystem path monitored for available disk space.
    """

    statsd: DogStatsd | None = None
    event_bus: SimpleEventBus | None = None
    max_samples: int = 100
    disk_path: str = "/"
    latency_samples: Deque[float] = field(init=False)

    def __post_init__(self) -> None:
        self.latency_samples = deque(maxlen=self.max_samples)

    def record_write_latency(self, latency_ms: float) -> None:
        """Store latency sample and emit metrics."""
        self.latency_samples.append(latency_ms)
        if self.statsd:
            self.statsd.histogram("memory.write_latency_ms", latency_ms)
        if self.event_bus:
            self.event_bus.publish(
                "monitor.metric_recorded",
                MetricRecordedEvent(name="memory.write_latency_ms", value=latency_ms),
            )
        self._check_alerts()

    def report_items_count(self, count: int) -> None:
        """Emit gauge with current item count."""
        if self.statsd:
            self.statsd.gauge("memory.items_count", count)
        if self.event_bus:
            self.event_bus.publish(
                "monitor.metric_recorded",
                MetricRecordedEvent(name="memory.items_count", value=float(count)),
            )

    def increment_evictions(self, cause: str) -> None:
        """Increment eviction counter.

        Parameters
        ----------
        cause : str
            Reason for the eviction. Emitted as a metric tag when ``statsd`` is
            available.
        """
        if self.statsd:
            self.statsd.increment("memory.evictions_total", tags=[f"cause:{cause}"])
        if self.event_bus:
            self.event_bus.publish(
                "monitor.metric_recorded",
                MetricRecordedEvent(
                    name="memory.evictions_total", value=1.0, tags={"cause": cause}
                ),
            )

    def _check_alerts(self) -> None:
        disk_pct = psutil.disk_usage(self.disk_path).percent
        if disk_pct > 90:
            logger.warning("Disk usage above 90%%: %.1f%%", disk_pct)
            if self.event_bus:
                self.event_bus.publish(
                    "monitor.disk_usage_alert",
                    DiskUsageAlert(usage_pct=float(disk_pct)),
                )
        if self.latency_samples:
            p95 = float(np.percentile(list(self.latency_samples), 95))
            if p95 > 30:
                logger.warning(
                    "Memory write latency p95 %.2f ms exceeds threshold", p95
                )
                if self.event_bus:
                    self.event_bus.publish(
                        "monitor.memory_latency_alert",
                        MemoryLatencyAlert(latency_ms=p95),
                    )
