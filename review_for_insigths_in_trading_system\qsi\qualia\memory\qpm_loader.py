from __future__ import annotations

from typing import Any, Dict, Optional, TYPE_CHECKING

from ..utils.logger import get_logger
from ..config.settings import settings
from ..risk.manager import create_risk_manager

if TYPE_CHECKING:  # pragma: no cover - used for type checking only
    from .quantum_pattern_memory import QuantumPatternMemory

logger = get_logger(__name__)

_QPM_INSTANCE: Optional["QuantumPatternMemory"] = None


def get_qpm_instance(config: Optional[Dict[str, Any]] = None) -> "QuantumPatternMemory":
    """Return a shared :class:`QuantumPatternMemory` instance.

    Parameters
    ----------
    config : dict, optional
        Configuration overrides for ``QuantumPatternMemory``.
    """
    global _QPM_INSTANCE
    if _QPM_INSTANCE is not None:
        return _QPM_INSTANCE

    cfg = dict(config or {})
    cfg.setdefault("persistence_path", settings.qpm_memory_file)
    if "risk_manager" not in cfg:
        cfg["risk_manager"] = create_risk_manager(
            initial_capital=10000.0,
            risk_profile="moderate",
        )
    from .quantum_pattern_memory import QuantumPatternMemory

    qpm = QuantumPatternMemory(**cfg)
    _QPM_INSTANCE = qpm
    logger.info("QuantumPatternMemory criado/carregado em %s", cfg["persistence_path"])
    return qpm
