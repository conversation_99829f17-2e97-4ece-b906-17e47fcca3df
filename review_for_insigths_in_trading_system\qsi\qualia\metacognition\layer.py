"""Camada de metacognição que avalia padrões quânticos.

Esta unidade computa métricas quânticas para cada ``QuantumSignaturePacket`` e
atribui rótulos simbólicos explicando o resultado.

Classes
-------
MetacognitionConfig
    Define pesos e limiares usados nas avaliações.
QuantumMetacognitionLayer
    Integra-se ao ``QUALIAQuantumUniverse`` e à ``QuantumPatternMemory`` para
    produzir scores e explicações detalhadas.
"""

from __future__ import annotations

import time
import uuid  # Packet ID fallback
from typing import Any, Dict, List, Optional, Tuple, TYPE_CHECKING, Union

import copy

import numpy as np
from numpy.linalg import LinAlgError
from qiskit.exceptions import QiskitError
from pydantic import BaseModel, Field, model_validator, field_validator
from scipy.spatial.distance import cosine
from ..utils.logger import get_logger

from ..common_types import QuantumSignaturePacket

try:
    from opentelemetry import trace
except Exception:  # pragma: no cover - optional dependency
    trace = None

if TYPE_CHECKING:
    from ..core.universe import QUALIAQuantumUniverse, QuantumPatternMemory

# Default thresholds used as class-level constants
_DEFAULT_THRESHOLDS: Dict[str, float] = {"high": 0.75, "medium": 0.5, "low": 0.0}

# Default value when a metric cannot be computed or is missing
DEFAULT_METRIC_VALUE = 0.5

# Default value when coherence is not provided
DEFAULT_COHERENCE_VALUE = 0.0

# Default time step for OTOC calculation when not specified in metadata
DEFAULT_OTOC_TIME_STEP = 1.0

# Helper constants used in _von_neumann_entropy
VN_EIGENVALUE_EPSILON = 1e-10
INTERLEAVED_CHECK_ENTRIES = 6
IMBALANCE_THRESHOLD_FACTOR = 100

logger = get_logger(__name__)  # module logger


def detect_interleaved_vector(vec: np.ndarray) -> bool:
    """Check whether an array is in ``[real1, imag1, real2, imag2, ...]`` form.

    Parameters
    ----------
    vec : numpy.ndarray
        Vector to inspect.

    Returns
    -------
    bool
        ``True`` if the vector appears interleaved, ``False`` otherwise.
    """

    if (
        vec is None
        or not hasattr(vec, "shape")
        or vec.shape != (len(vec),)
        or len(vec) % 2 != 0
    ):
        return False

    try:
        first_entries = vec[:INTERLEAVED_CHECK_ENTRIES]
        for i in range(0, len(first_entries), 2):
            if i + 1 < len(first_entries):
                r = abs(first_entries[i])
                im = abs(first_entries[i + 1])
                if (
                    r > IMBALANCE_THRESHOLD_FACTOR * im
                    or im > IMBALANCE_THRESHOLD_FACTOR * r
                ):
                    return False
        return True
    except (IndexError, ValueError, TypeError) as exc:  # pragma: no cover - defensive
        logger.exception(
            "Error while checking if vector is interleaved in detect_interleaved_vector: %s",
            exc,
        )
        return False


class MetacognitionConfig(BaseModel):
    weight_similarity: float = Field(0.30, ge=0, le=1)
    weight_entropy: float = Field(0.25, ge=0, le=1)
    weight_otoc: float = Field(0.20, ge=0, le=1)
    weight_coherence: float = Field(0.15, ge=0, le=1)
    weight_entanglement: float = Field(0.10, ge=0, le=1)

    thresholds: Dict[str, float] = Field(
        default_factory=lambda: _DEFAULT_THRESHOLDS.copy()
    )  # Usar .copy() da factory
    default_metric_value: float = Field(DEFAULT_METRIC_VALUE, ge=0, le=1)
    default_otoc_time_step: float = Field(DEFAULT_OTOC_TIME_STEP, ge=0)
    anomaly_threshold: float = Field(2.0, ge=0)
    min_history_for_anomaly: int = Field(5, ge=1)
    version: str = "1.1"

    @model_validator(mode="after")
    def _process_config_after_init(self) -> MetacognitionConfig:
        # 1. Normalizar pesos
        total_weights = (
            self.weight_similarity
            + self.weight_entropy
            + self.weight_otoc
            + self.weight_coherence
            + self.weight_entanglement
        )
        if not np.isclose(total_weights, 1.0):
            self.weight_similarity /= total_weights
            self.weight_entropy /= total_weights
            self.weight_otoc /= total_weights
            self.weight_coherence /= total_weights
            self.weight_entanglement /= total_weights
            logger.info(
                "[MetacogCfg] Pesos normalizados (soma original=%.3f).",
                total_weights,
            )

        # A lógica de preenchimento de defaults para thresholds foi movida
        # para o field_validator 'check_thresholds_consistency'.

        return self

    @field_validator("thresholds", mode="after")
    def check_thresholds_consistency(cls, v: Dict[str, float]) -> Dict[str, float]:
        # Preencher com defaults ANTES de validar a consistência.
        # 'v' aqui é o valor passado pelo usuário (ou o default_factory se nada foi passado,
        # que já retorna uma cópia de _DEFAULT_THRESHOLDS).
        # Se um dict parcial é passado na instanciação, ele substitui o default_factory.
        # Portanto, precisamos mesclar 'v' com os defaults para garantir todas as chaves.

        processed_thresholds = _DEFAULT_THRESHOLDS.copy()
        # 'v' deve ser um dict aqui devido à anotação de tipo e ao mode="after".
        # Pydantic tentaria converter para dict ou falharia antes de chegar aqui se não fosse compatível.
        processed_thresholds.update(v)

        # Agora, processed_thresholds tem todas as chaves, seja do default ou atualizadas por 'v'.
        low = processed_thresholds["low"]
        medium = processed_thresholds["medium"]
        high = processed_thresholds["high"]

        # A validação de tipo (se são floats) é feita pelo Pydantic com base na anotação Dict[str, float].
        # Se um valor não numérico for passado, o Pydantic levantará um erro antes deste validador.
        # Portanto, a checagem isinstance foi removida por ser redundante.

        if not (0 <= low <= medium < high <= 1):
            raise ValueError(
                f"Limiares inconsistentes: low({low}) <= medium({medium}) < high({high}) e todos entre 0 e 1"
            )
        return processed_thresholds  # Retornar o dicionário processado e validado


class QuantumMetacognitionLayer:
    """Evaluate quantum patterns, assign scores and explain the rationale.

    The layer can operate synchronously (default) or asynchronously via a queue.

    This component implements:
    1. Similarity evaluation against stored patterns
    2. Calculation of quantum metrics (entropy, OTOC, coherence)
    3. Weighted aggregation for the final score
    4. Generation of structured explanations
    5. Feedback-based learning

    Parameters
    ----------
    universe : QUALIAQuantumUniverse
        Universe instance used for quantum measurements.
    pattern_memory : QuantumPatternMemory
        Memory of patterns used for similarity comparison.
    config : Optional[MetacognitionConfig]
        Optional configuration with weights and thresholds.
    """

    def __init__(
        self,
        universe: "QUALIAQuantumUniverse",  # Forward reference
        pattern_memory: "QuantumPatternMemory",  # Forward reference
        config: Optional[MetacognitionConfig] = None,
        max_log_entries: int = 1000,
    ):
        self.universe = universe
        self.memory = pattern_memory
        self.cfg = config or MetacognitionConfig()
        self.log: List[Dict[str, Any]] = []  # histórico de avaliações
        self.max_log_entries = max_log_entries
        self.layer_id = str(uuid.uuid4())  # identificador da camada
        logger.info(
            "QuantumMetacognitionLayer %s inicializada.",
            self.layer_id,
        )  # loga o layer_id

    def _reconstruct_complex_vector(
        self, interleaved_float_vec: np.ndarray
    ) -> Optional[np.ndarray]:
        """Reconstruct a complex vector from an interleaved real/imaginary array.

        Parameters
        ----------
        interleaved_float_vec : numpy.ndarray
            Sequence of values ``[real1, imag1, real2, imag2, ...]``.

        Returns
        -------
        Optional[numpy.ndarray]
            Complex vector or ``None`` if reconstruction fails.
        """
        try:
            if len(interleaved_float_vec) % 2 != 0:
                return None  # Odd number of elements, cannot reconstruct

            real_parts = interleaved_float_vec[::2]
            imag_parts = interleaved_float_vec[1::2]
            return real_parts + 1j * imag_parts
        except (ValueError, TypeError, IndexError) as e:
            logger.error("Error reconstructing complex vector: %s", e)
            return None

    def _extract_input_data(
        self,
        packet_or_tuple: Union[
            "QuantumSignaturePacket", Tuple[List[float], Dict[str, Any]]
        ],
    ) -> Tuple[Optional[np.ndarray], Dict[str, Any], Optional[str]]:
        """Extracts vector and metadata from an input packet.

        Parameters
        ----------
        packet_or_tuple : Union[QuantumSignaturePacket, Tuple[List[float], Dict[str, Any]]]
            The packet instance or ``(vector, metadata)`` tuple.

        Returns
        -------
        Tuple[Optional[numpy.ndarray], Dict[str, Any], Optional[str]]
            Signature vector, associated metadata and packet identifier.
        """
        try:
            # Case 1: QuantumSignaturePacket instance
            if hasattr(packet_or_tuple, "vector") and hasattr(
                packet_or_tuple, "source_details"
            ):
                packet = packet_or_tuple
                vector = np.array(packet.vector, dtype=float)
                metadata = packet.metrics.copy() if hasattr(packet, "metrics") else {}

                # Add source_details to metadata
                if hasattr(packet, "source_details"):
                    metadata.update(packet.source_details)

                packet_id = (
                    packet.id if hasattr(packet, "id") else f"unknown_{time.time()}"
                )
                return vector, metadata, packet_id

            # Case 2: tuple (vector, metadata)
            elif isinstance(packet_or_tuple, tuple) and len(packet_or_tuple) >= 2:
                vector, metadata = packet_or_tuple[0], packet_or_tuple[1]
                vector_np = np.array(vector, dtype=float)
                packet_id = metadata.get("id", f"tuple_{time.time()}")
                return vector_np, metadata, packet_id

            # Error case
            return None, {}, f"error_{time.time()}"

        except (TypeError, ValueError, AttributeError) as e:
            logger.error("Error extracting input data: %s", e)
            return None, {}, f"error_{time.time()}"

    def evaluate_pattern(
        self,
        packet_or_tuple: Union[
            "QuantumSignaturePacket", Tuple[List[float], Dict[str, Any]]
        ],
    ) -> Dict[str, Any]:
        """Evaluate a quantum pattern with adaptive sensitivity.

        NOTE(YAA REFINEMENT): Enhanced pattern evaluation with context-aware
        sensitivity adjustments and emergent pattern detection.

        Parameters
        ----------
        packet_or_tuple : Union[QuantumSignaturePacket, Tuple[List[float], Dict[str, Any]]]
            Input pattern or tuple ``(vector, metadata)``.

        Returns
        -------
        Dict[str, Any]
            Detailed evaluation including score, category, and adaptive insights.
        """
        # Unpack input data
        if isinstance(packet_or_tuple, tuple):
            vector, metadata = packet_or_tuple
            packet = QuantumSignaturePacket(vector=vector, metrics=metadata)
        else:
            packet = packet_or_tuple
            vector = packet.vector
            metadata = packet.metrics

        # NOTE(YAA REFINEMENT): Context-aware evaluation
        market_context = self._analyze_evaluation_context(metadata)
        adaptive_weights = self._calculate_adaptive_weights(market_context)

        logger.debug(
            f"Evaluating pattern with adaptive weights: {adaptive_weights}, "
            f"context: {market_context['regime']}"
        )

        # Core metric calculations with adaptive sensitivity
        similarity = self._refined_similarity_score(vector, adaptive_weights)
        entropy = self._adaptive_entropy_calculation(vector, market_context)
        otoc = self._adaptive_otoc_estimation(metadata, market_context)
        coherence = self._adaptive_coherence_calculation(vector, market_context)
        entanglement = self._adaptive_entanglement_measurement(vector, market_context)

        # NOTE(YAA REFINEMENT): Weighted score calculation with adaptive thresholds
        weighted_score = self._calculate_adaptive_weighted_score(
            similarity, entropy, otoc, coherence, entanglement, adaptive_weights
        )

        # NOTE(YAA REFINEMENT): Enhanced classification with pattern emergence detection
        classification = self._enhanced_pattern_classification(
            weighted_score,
            similarity,
            entropy,
            otoc,
            coherence,
            entanglement,
            market_context,
            adaptive_weights,
        )

        # NOTE(YAA REFINEMENT): Emergent pattern detection
        emergent_patterns = self._detect_emergent_patterns(
            vector, metadata, market_context
        )

        # Compile comprehensive evaluation result
        evaluation_result = {
            "packet_id": getattr(packet, "id", str(uuid.uuid4())),
            "quantum_score": float(weighted_score),
            "similarity": float(similarity),
            "entropy": float(entropy),
            "otoc": float(otoc),
            "coherence": float(coherence),
            "entanglement": float(entanglement),
            "label": classification["label"],
            "explanation": classification["explanation"],
            "confidence": classification["confidence"],
            "market_context": market_context,
            "adaptive_weights": adaptive_weights,
            "emergent_patterns": emergent_patterns,
            "sensitivity_factors": {
                "entropy_sensitivity": adaptive_weights["entropy"]
                * market_context.get("entropy_multiplier", 1.0),
                "otoc_sensitivity": adaptive_weights["otoc"]
                * market_context.get("otoc_multiplier", 1.0),
                "coherence_sensitivity": adaptive_weights["coherence"]
                * market_context.get("coherence_multiplier", 1.0),
            },
            "timestamp": time.time(),
            "evaluation_version": "2.0_adaptive",
        }

        # NOTE(YAA REFINEMENT): Dynamic threshold adjustment
        self._update_dynamic_thresholds(evaluation_result)

        return evaluation_result

    def _analyze_evaluation_context(self, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze context for adaptive evaluation."""
        context = {
            "regime": "normal",
            "volatility_level": "medium",
            "pattern_complexity": "standard",
            "temporal_urgency": "normal",
            "entropy_multiplier": 1.0,
            "otoc_multiplier": 1.0,
            "coherence_multiplier": 1.0,
        }

        try:
            # Analyze volatility from metadata
            if "volatility" in metadata or "vol" in str(metadata):
                vol_indicators = [k for k in metadata.keys() if "vol" in k.lower()]
                if vol_indicators:
                    vol_value = metadata.get(vol_indicators[0], 0.5)
                    if vol_value > 0.7:
                        context["regime"] = "high_volatility"
                        context["volatility_level"] = "high"
                        context["entropy_multiplier"] = 1.2
                        context["otoc_multiplier"] = 1.4
                        context["coherence_multiplier"] = 0.9
                    elif vol_value < 0.3:
                        context["regime"] = "low_volatility"
                        context["volatility_level"] = "low"
                        context["entropy_multiplier"] = 1.5
                        context["otoc_multiplier"] = 0.8
                        context["coherence_multiplier"] = 1.3

            # Analyze pattern complexity
            if "pattern_complexity" in metadata:
                complexity = metadata["pattern_complexity"]
                if complexity > 0.8:
                    context["pattern_complexity"] = "high"
                elif complexity < 0.3:
                    context["pattern_complexity"] = "low"

            # Analyze temporal urgency (how quickly patterns are changing)
            if "temporal_change_rate" in metadata:
                change_rate = metadata["temporal_change_rate"]
                if change_rate > 0.75:
                    context["temporal_urgency"] = "high"
                elif change_rate < 0.25:
                    context["temporal_urgency"] = "low"

        except Exception as e:
            logger.debug(f"Context analysis error: {e}")

        return context

    def _calculate_adaptive_weights(
        self, market_context: Dict[str, Any]
    ) -> Dict[str, float]:
        """Calculate adaptive weights based on market context."""
        base_weights = {
            "similarity": self.cfg.weight_similarity,
            "entropy": self.cfg.weight_entropy,
            "otoc": self.cfg.weight_otoc,
            "coherence": self.cfg.weight_coherence,
            "entanglement": self.cfg.weight_entanglement,
        }

        regime = market_context.get("regime", "normal")

        # Adjust weights based on regime
        if regime == "high_volatility":
            # High volatility: focus more on OTOC and entropy
            adjustments = {
                "similarity": 0.9,
                "entropy": 1.2,
                "otoc": 1.4,
                "coherence": 0.9,
                "entanglement": 1.0,
            }
        elif regime == "low_volatility":
            # Low volatility: focus more on subtle coherence and entanglement
            adjustments = {
                "similarity": 1.1,
                "entropy": 1.5,
                "otoc": 0.8,
                "coherence": 1.3,
                "entanglement": 1.2,
            }
        else:
            # Normal regime: balanced weights
            adjustments = {k: 1.0 for k in base_weights.keys()}

        # Apply adjustments
        adaptive_weights = {}
        total_weight = 0
        for key, base_weight in base_weights.items():
            adjusted_weight = base_weight * adjustments[key]
            adaptive_weights[key] = adjusted_weight
            total_weight += adjusted_weight

        # Normalize to maintain total weight consistency
        for key in adaptive_weights:
            adaptive_weights[key] /= total_weight

        return adaptive_weights

    def _refined_similarity_score(
        self, vector: List[float], adaptive_weights: Dict[str, float]
    ) -> float:
        """Calculate similarity with enhanced pattern matching."""
        try:
            if (
                not self.memory
                or not hasattr(self.memory, "memory_slots")
                or not self.memory.memory_slots
            ):
                return 0.0

            # Convert vector to numpy array
            query_vector = np.asarray(vector, dtype=float)

            # NOTE(YAA REFINEMENT): Multi-modal similarity calculation
            similarities = []

            # Standard cosine similarity
            for pattern in self.memory.memory_slots:
                if not hasattr(pattern, "vector") or not pattern.vector:
                    continue

                pattern_vec = np.array(pattern.vector, dtype=float)
                cosine_sim = 1 - cosine(query_vector, pattern_vec)
                similarities.append(cosine_sim)

            if not similarities:
                return 0.0

            # NOTE(YAA REFINEMENT): Adaptive similarity aggregation
            max_similarity = max(similarities)
            mean_similarity = np.mean(similarities)

            # Weight based on context - in high volatility, prefer max similarity (stability)
            # In low volatility, prefer mean similarity (sensitivity to subtle changes)
            sensitivity_factor = adaptive_weights.get("similarity", 0.3)

            if sensitivity_factor > 0.35:  # High sensitivity context
                final_similarity = 0.7 * max_similarity + 0.3 * mean_similarity
            else:  # Normal or low sensitivity context
                final_similarity = 0.5 * max_similarity + 0.5 * mean_similarity

            return float(np.clip(final_similarity, 0.0, 1.0))

        except Exception as e:
            logger.error(f"Similarity calculation error: {e}")
            return 0.0

    def _adaptive_entropy_calculation(
        self, vector: List[float], market_context: Dict[str, Any]
    ) -> float:
        """Calculate entropy with adaptive sensitivity."""
        try:
            vec_array = np.asarray(vector, dtype=float)

            # NOTE(YAA REFINEMENT): Multi-scale entropy calculation
            # Standard entropy
            if len(vec_array) > 1:
                # Normalize vector for probability calculation
                vec_normalized = np.abs(vec_array) / (np.sum(np.abs(vec_array)) + 1e-10)
                standard_entropy = -np.sum(
                    vec_normalized * np.log2(vec_normalized + 1e-10)
                )
            else:
                standard_entropy = 0.0

            # NOTE(YAA REFINEMENT): Adaptive entropy scaling
            entropy_multiplier = market_context.get("entropy_multiplier", 1.0)
            adaptive_entropy = standard_entropy * entropy_multiplier

            # Apply regime-specific normalization
            if market_context.get("regime") == "low_volatility":
                # In low volatility, amplify small entropy changes
                adaptive_entropy = np.power(adaptive_entropy, 0.8)
            elif market_context.get("regime") == "high_volatility":
                # In high volatility, dampen large entropy swings
                adaptive_entropy = np.power(adaptive_entropy, 1.2)

            return float(
                np.clip(adaptive_entropy / 10.0, 0.0, 1.0)
            )  # Normalize to [0,1]

        except Exception as e:
            logger.error(f"Adaptive entropy calculation error: {e}")
            return self.cfg.default_metric_value

    def _adaptive_otoc_estimation(
        self, metadata: Dict[str, Any], market_context: Dict[str, Any]
    ) -> float:
        """Estimate OTOC with adaptive sensitivity."""
        try:
            # NOTE(YAA REFINEMENT): Context-aware OTOC estimation
            if self.universe:
                time_step = metadata.get("time_step", self.cfg.default_otoc_time_step)
                n_qubits = metadata.get(
                    "n_qubits", getattr(self.universe, "n_qubits", 2)
                )

                # Adaptive time step based on market context
                if market_context.get("temporal_urgency") == "high":
                    time_step *= 1.5  # Faster scrambling for urgent patterns
                elif market_context.get("temporal_urgency") == "low":
                    time_step *= 0.7  # Slower scrambling for stable patterns

                statevector = getattr(self.universe, "current_sv", None)
                if statevector:
                    otoc_value = self.universe.calculate_otoc(
                        statevector=statevector,
                        time_step=time_step,
                        n_qubits=n_qubits,
                        target_qubits=[0],
                    )

                    if otoc_value is not None:
                        # Apply adaptive scaling
                        otoc_multiplier = market_context.get("otoc_multiplier", 1.0)
                        return float(np.clip(otoc_value * otoc_multiplier, 0.0, 1.0))

            # Fallback estimation based on metadata
            if "otoc" in metadata:
                otoc_value = float(metadata["otoc"])
                otoc_multiplier = market_context.get("otoc_multiplier", 1.0)
                return float(np.clip(otoc_value * otoc_multiplier, 0.0, 1.0))

            return self.cfg.default_metric_value

        except Exception as e:
            logger.error(f"Adaptive OTOC estimation error: {e}")
            return self.cfg.default_metric_value

    def _adaptive_coherence_calculation(
        self, vector: List[float], market_context: Dict[str, Any]
    ) -> float:
        """Calculate coherence with adaptive sensitivity."""
        try:
            coherence_value = self._coherence_l1(vector)

            # NOTE(YAA REFINEMENT): Adaptive coherence scaling
            coherence_multiplier = market_context.get("coherence_multiplier", 1.0)
            adaptive_coherence = coherence_value * coherence_multiplier

            # Apply sensitivity adjustments based on pattern complexity
            complexity = market_context.get("pattern_complexity", "standard")
            if complexity == "high":
                # High complexity patterns need more coherence sensitivity
                adaptive_coherence = np.power(adaptive_coherence, 0.9)
            elif complexity == "low":
                # Low complexity patterns - standard coherence
                adaptive_coherence = np.power(adaptive_coherence, 1.1)

            return float(np.clip(adaptive_coherence, 0.0, 1.0))

        except Exception as e:
            logger.error(f"Adaptive coherence calculation error: {e}")
            return DEFAULT_COHERENCE_VALUE

    def _adaptive_entanglement_measurement(
        self, vector: List[float], market_context: Dict[str, Any]
    ) -> float:
        """Measure entanglement with adaptive sensitivity."""
        try:
            entanglement_value = self._multipartite_entanglement(
                np.asarray(vector, dtype=float)
            )

            # NOTE(YAA REFINEMENT): Context-aware entanglement assessment
            regime = market_context.get("regime", "normal")

            if regime == "high_volatility":
                # High volatility - entanglement indicates systemic correlations
                entanglement_value *= 1.2
            elif regime == "low_volatility":
                # Low volatility - subtle entanglement more significant
                entanglement_value *= 1.4

            return float(np.clip(entanglement_value, 0.0, 1.0))

        except Exception as e:
            logger.error(f"Adaptive entanglement measurement error: {e}")
            return self.cfg.default_metric_value

    def _calculate_adaptive_weighted_score(
        self,
        similarity: float,
        entropy: float,
        otoc: float,
        coherence: float,
        entanglement: float,
        adaptive_weights: Dict[str, float],
    ) -> float:
        """Calculate weighted score with adaptive weights."""
        try:
            weighted_score = (
                similarity * adaptive_weights["similarity"]
                + entropy * adaptive_weights["entropy"]
                + otoc * adaptive_weights["otoc"]
                + coherence * adaptive_weights["coherence"]
                + entanglement * adaptive_weights["entanglement"]
            )

            return float(np.clip(weighted_score, 0.0, 1.0))

        except Exception as e:
            logger.error(f"Adaptive weighted score calculation error: {e}")
            return 0.5

    def _enhanced_pattern_classification(
        self,
        weighted_score: float,
        similarity: float,
        entropy: float,
        otoc: float,
        coherence: float,
        entanglement: float,
        market_context: Dict[str, Any],
        adaptive_weights: Dict[str, float],
    ) -> Dict[str, Any]:
        """Enhanced pattern classification with adaptive thresholds."""
        try:
            regime = market_context.get("regime", "normal")

            # NOTE(YAA REFINEMENT): Adaptive thresholds based on context
            if regime == "high_volatility":
                high_threshold = (
                    self.cfg.thresholds["high"] * 0.85
                )  # Lower thresholds in volatile markets
                medium_threshold = self.cfg.thresholds["medium"] * 0.85
            elif regime == "low_volatility":
                high_threshold = (
                    self.cfg.thresholds["high"] * 1.15
                )  # Higher thresholds in calm markets
                medium_threshold = self.cfg.thresholds["medium"] * 1.15
            else:
                high_threshold = self.cfg.thresholds["high"]
                medium_threshold = self.cfg.thresholds["medium"]

            # Determine primary classification
            if weighted_score >= high_threshold:
                primary_label = "high_relevance"
                confidence = 0.8 + (weighted_score - high_threshold) * 0.4
            elif weighted_score >= medium_threshold:
                primary_label = "medium_relevance"
                confidence = 0.5 + (weighted_score - medium_threshold) * 0.6
            else:
                primary_label = "low_relevance"
                confidence = weighted_score * 0.5

            # NOTE(YAA REFINEMENT): Enhanced pattern categorization
            pattern_characteristics = []

            if similarity > 0.7:
                pattern_characteristics.append("familiar")
            elif similarity < 0.3:
                pattern_characteristics.append("novel")

            if entropy > 0.6:
                pattern_characteristics.append("complex")
            elif entropy < 0.2:
                pattern_characteristics.append("simple")

            if otoc > 0.8:
                pattern_characteristics.append("chaotic")
            elif otoc < 0.3:
                pattern_characteristics.append("ordered")

            if coherence > 0.7:
                pattern_characteristics.append("coherent")
            elif coherence < 0.3:
                pattern_characteristics.append("decoherent")

            if entanglement > 0.6:
                pattern_characteristics.append("entangled")

            # Construct enhanced explanation
            base_explanation = f"Pattern classified as {primary_label} with {confidence:.1%} confidence"

            if pattern_characteristics:
                characteristics_str = ", ".join(pattern_characteristics)
                enhanced_explanation = f"{base_explanation}. Characteristics: {characteristics_str}. Market regime: {regime}."
            else:
                enhanced_explanation = f"{base_explanation}. Market regime: {regime}."

            return {
                "label": primary_label,
                "confidence": float(np.clip(confidence, 0.0, 1.0)),
                "explanation": enhanced_explanation,
                "characteristics": pattern_characteristics,
                "adaptive_thresholds": {
                    "high": high_threshold,
                    "medium": medium_threshold,
                },
            }

        except Exception as e:
            logger.error(f"Enhanced pattern classification error: {e}")
            return {
                "label": "unknown",
                "confidence": 0.5,
                "explanation": f"Classification error: {e}",
                "characteristics": [],
                "adaptive_thresholds": self.cfg.thresholds,
            }

    def _detect_emergent_patterns(
        self,
        vector: List[float],
        metadata: Dict[str, Any],
        market_context: Dict[str, Any],
    ) -> List[Dict[str, Any]]:
        """Detect emergent patterns using quantum-inspired analysis."""
        emergent_patterns = []

        try:
            vec_array = np.asarray(vector, dtype=float)

            # NOTE(YAA REFINEMENT): Fractal pattern detection
            if len(vec_array) >= 8:
                fractal_dimension = self._calculate_fractal_dimension(vec_array)
                if fractal_dimension > 1.5:
                    emergent_patterns.append(
                        {
                            "type": "fractal_emergence",
                            "strength": fractal_dimension - 1.0,
                            "description": f"Fractal pattern detected with dimension {fractal_dimension:.2f}",
                        }
                    )

            # NOTE(YAA REFINEMENT): Phase transition detection
            phase_transitions = self._detect_phase_transitions(vec_array)
            if phase_transitions:
                emergent_patterns.extend(phase_transitions)

            # NOTE(YAA REFINEMENT): Resonance pattern detection
            resonance_patterns = self._detect_resonance_patterns(vec_array, metadata)
            if resonance_patterns:
                emergent_patterns.extend(resonance_patterns)

        except Exception as e:
            logger.debug(f"Emergent pattern detection error: {e}")

        return emergent_patterns

    def _calculate_fractal_dimension(self, vector: np.ndarray) -> float:
        """Calculate fractal dimension of the pattern."""
        try:
            # Box-counting method approximation
            n = len(vector)
            if n < 4:
                return 1.0

            # Normalize vector
            norm_vector = (vector - np.min(vector)) / (
                np.max(vector) - np.min(vector) + 1e-10
            )

            # Calculate approximate fractal dimension
            scales = np.logspace(0.1, np.log10(n / 4), 10)
            counts = []

            for scale in scales:
                box_size = int(max(1, scale))
                boxes = int(np.ceil(n / box_size))
                count = 0

                for i in range(boxes):
                    start_idx = i * box_size
                    end_idx = min((i + 1) * box_size, n)
                    box_data = norm_vector[start_idx:end_idx]

                    if (
                        len(box_data) > 0
                        and (np.max(box_data) - np.min(box_data)) > 1e-6
                    ):
                        count += 1

                counts.append(count)

            # Linear regression to find fractal dimension
            if len(counts) > 2:
                log_scales = np.log(scales)
                log_counts = np.log(np.array(counts) + 1e-10)
                slope = np.polyfit(log_scales, log_counts, 1)[0]
                return float(1.0 + abs(slope))

            return 1.0

        except Exception as e:
            logger.debug(f"Fractal dimension calculation error: {e}")
            return 1.0

    def _detect_phase_transitions(self, vector: np.ndarray) -> List[Dict[str, Any]]:
        """Detect phase transitions in the pattern."""
        transitions = []

        try:
            if len(vector) < 6:
                return transitions

            # Calculate local variance to detect phase changes
            window_size = max(3, len(vector) // 4)
            variances = []

            for i in range(len(vector) - window_size + 1):
                window = vector[i : i + window_size]
                variances.append(np.var(window))

            # Detect significant variance changes
            if len(variances) > 2:
                variance_changes = np.diff(variances)
                threshold = np.std(variance_changes) * 2

                transition_points = np.where(np.abs(variance_changes) > threshold)[0]

                for point in transition_points:
                    transitions.append(
                        {
                            "type": "phase_transition",
                            "position": int(point),
                            "strength": float(
                                abs(variance_changes[point])
                                / (np.std(variance_changes) + 1e-10)
                            ),
                            "description": f"Phase transition detected at position {point}",
                        }
                    )

        except Exception as e:
            logger.debug(f"Phase transition detection error: {e}")

        return transitions

    def _detect_resonance_patterns(
        self, vector: np.ndarray, metadata: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Detect resonance patterns in the quantum signature."""
        resonance_patterns = []

        try:
            if len(vector) < 4:
                return resonance_patterns

            # NOTE(YAA REFINEMENT): Fourier analysis for resonance detection
            fft_vector = np.fft.fft(vector)
            frequencies = np.fft.fftfreq(len(vector))
            magnitudes = np.abs(fft_vector)

            # Find dominant frequencies
            peak_indices = np.where(
                magnitudes > np.mean(magnitudes) + 2 * np.std(magnitudes)
            )[0]

            for peak_idx in peak_indices[:3]:  # Top 3 peaks
                if peak_idx > 0:  # Skip DC component
                    frequency = frequencies[peak_idx]
                    magnitude = magnitudes[peak_idx]

                    resonance_patterns.append(
                        {
                            "type": "frequency_resonance",
                            "frequency": float(frequency),
                            "strength": float(magnitude / np.max(magnitudes)),
                            "description": f"Resonance at frequency {frequency:.3f} with strength {magnitude:.2f}",
                        }
                    )

        except Exception as e:
            logger.debug(f"Resonance pattern detection error: {e}")

        return resonance_patterns

    def _update_dynamic_thresholds(self, evaluation_result: Dict[str, Any]):
        """Atualiza ``self.cfg.thresholds`` de forma adaptativa.

        A cada nova avaliação o ``quantum_score`` é armazenado em uma janela
        deslizante de no máximo 100 entradas. O limiar ``"high"`` passa a ser o
        percentil 90 dos ``quantum_score`` recentes, enquanto ``"medium"`` segue
        a média móvel simples dessas pontuações. O limiar ``"low"`` permanece
        inalterado.

        Parameters
        ----------
        evaluation_result : Dict[str, Any]
            Dicionário retornado por :meth:`evaluate_pattern` contendo a chave
            ``"quantum_score"``.
        """
        try:
            score = float(evaluation_result["quantum_score"])
        except (KeyError, TypeError, ValueError) as exc:  # noqa: B902
            logger.debug("Dynamic threshold update error: %s", exc)
            return

        try:
            if not hasattr(self, "_recent_evaluations"):
                self._recent_evaluations = []

            self._recent_evaluations.append({"score": score, "timestamp": time.time()})
            self._recent_evaluations = self._recent_evaluations[-100:]

            scores = [entry["score"] for entry in self._recent_evaluations]
            if len(scores) < 5:
                return  # Aguarda mais dados para ajustes estáveis

            high = float(np.percentile(scores, 90))
            medium = float(np.mean(scores))

            low = self.cfg.thresholds.get("low", 0.0)
            high = min(max(high, medium + 1e-6), 1.0)
            medium = min(max(medium, low), high - 1e-6)

            self.cfg.thresholds["high"] = high
            self.cfg.thresholds["medium"] = medium

            logger.debug(
                "Thresholds atualizados - high: %.4f, medium: %.4f", high, medium
            )
        except Exception as e:
            logger.debug("Dynamic threshold update error: %s", e)

    def _similarity_to_memory(self, query_vec: np.ndarray) -> float:
        """Compute similarity between a vector and stored patterns.

        Parameters
        ----------
        query_vec : numpy.ndarray
            Vector to be compared against memory.

        Returns
        -------
        float
            Similarity value in the ``[0, 1]`` range.
        """
        if not hasattr(self.memory, "memory_slots") or not self.memory.memory_slots:
            return 0.0

        try:
            # Normalizar vetor de consulta
            query_norm = np.linalg.norm(query_vec)
            if query_norm < 1e-9:  # Vetor nulo
                return 0.0
            normalized_query = query_vec / query_norm

            # Calcular similaridades com todos os padrões
            similarities = []

            for pattern in self.memory.memory_slots:
                if not hasattr(pattern, "vector") or not pattern.vector:
                    continue

                pattern_vec = np.array(pattern.vector, dtype=float)
                pattern_norm = np.linalg.norm(pattern_vec)

                if pattern_norm < 1e-9:
                    similarities.append(0.0)
                    continue

                normalized_pattern = pattern_vec / pattern_norm
                # Similaridade do cosseno é 1 - distância do cosseno
                sim = 1.0 - cosine(normalized_query, normalized_pattern)
                similarities.append(sim)

            # Retornar a maior similaridade encontrada
            return float(max(similarities)) if similarities else 0.0

        except (ValueError, TypeError) as e:
            logger.error("Erro ao calcular similaridade: %s", e)
            return 0.0

    def _aggregate(
        self, sim: float, ent: float, otoc: float, coh: float, entgl: float
    ) -> Tuple[float, float, float, float, float, float]:
        """Combine individual metrics into a single score.

        Parameters
        ----------
        sim : float
            Similarity measure.
        ent : float
            Quantum entropy.
        otoc : float
            Out-of-time-order correlator.
        coh : float
            L1 coherence.
        entgl : float
            Multipartite entanglement.

        Returns
        -------
        Tuple[float, float, float, float, float, float]
            Aggregated score followed by the processed metrics.
        """

        # Validar entradas e tratar valores nulos ou listas
        def _process_metric(value: Any, default: float) -> float:
            if isinstance(value, list):
                candidate = value[0] if value else None
            else:
                candidate = value

            if isinstance(candidate, (int, float)) and not (
                isinstance(candidate, float) and np.isnan(candidate)
            ):
                return float(candidate)
            return float(default)

        metric_map = {
            "sim": (sim, 0.0),
            "ent": (ent, self.cfg.default_metric_value),
            "otoc": (otoc, self.cfg.default_metric_value),
            "coh": (coh, DEFAULT_COHERENCE_VALUE),
            "entgl": (entgl, self.cfg.default_metric_value),
        }

        processed = {
            name: np.clip(_process_metric(val, default), 0.0, 1.0)
            for name, (val, default) in metric_map.items()
        }

        sim_final = processed["sim"]
        ent_final = processed["ent"]
        otoc_final = processed["otoc"]
        coh_final = processed["coh"]
        entgl_final = processed["entgl"]

        # Para entropia e OTOC, valores menores indicam maior ordem e coerência
        # Então usamos (1-valor) na agregação
        score = (
            self.cfg.weight_similarity * sim_final
            + self.cfg.weight_entropy * (1.0 - ent_final)  # menos entropia = mais ordem
            + self.cfg.weight_otoc * (1.0 - otoc_final)  # menos scrambling = melhor
            + self.cfg.weight_coherence * coh_final  # mais coerência = melhor
            + self.cfg.weight_entanglement * entgl_final
        )

        # Garantir que o score esteja em [0,1]
        return (
            float(np.clip(score, 0.0, 1.0)),
            sim_final,
            ent_final,
            otoc_final,
            coh_final,
            entgl_final,
        )

    def _von_neumann_entropy(self, vec: np.ndarray) -> float:
        """Compute the von Neumann entropy of a quantum state.

        Parameters
        ----------
        vec : numpy.ndarray
            State vector to analyse.

        Returns
        -------
        float
            Entropy value normalized to the ``[0, 1]`` interval.
        """
        try:
            if vec is not None:
                if detect_interleaved_vector(vec):
                    complex_vec = self._reconstruct_complex_vector(vec)
                    if complex_vec is not None:
                        # Criar matriz densidade
                        rho = np.outer(complex_vec, np.conj(complex_vec))
                        # Calcular autovalores
                        evals = np.linalg.eigvalsh(rho)
                        # Remover autovalores próximos de zero
                        evals = evals[evals > VN_EIGENVALUE_EPSILON]
                        # Calcular entropia de von Neumann: S = -Tr(ρ ln ρ)
                        entropy_val = -np.sum(evals * np.log2(evals))
                        # Normalizar para [0,1] baseado no número máximo de qubits n: max_entropy = n
                        # Estimamos n logaritmicamente a partir do tamanho do vetor: log2(len)
                        n_qubits_est = (
                            np.log2(len(complex_vec)) if len(complex_vec) > 0 else 1.0
                        )
                        return (
                            float(min(entropy_val / n_qubits_est, 1.0))
                            if n_qubits_est > 0
                            else 0.0
                        )
                else:
                    # Tentar usar diretamente como vetor de estado
                    try:
                        from qiskit.quantum_info import Statevector, entropy

                        sv = Statevector(vec)
                        ent_qiskit = entropy(sv, base=2)
                        # Normalizar para [0,1]
                        n_qubits_est = np.log2(len(vec)) if len(vec) > 0 else 1.0
                        return (
                            float(min(ent_qiskit / n_qubits_est, 1.0))
                            if n_qubits_est > 0
                            else 0.0
                        )
                    except (QiskitError, ValueError, ImportError, TypeError) as e:
                        logger.exception(
                            "Error computing entropy via Qiskit in _von_neumann_entropy: %s",
                            e,
                        )
        except (ValueError, LinAlgError, QiskitError, TypeError, ImportError) as e:
            logger.exception("Unexpected error during _von_neumann_entropy: %s", e)

        # Fallback para caso de erro
        return self.cfg.default_metric_value

    def _estimate_otoc(self, meta: Dict[str, Any]) -> Optional[float]:
        """Estimate the OTOC metric from metadata or the universe.

        Parameters
        ----------
        meta : Dict[str, Any]
            Metadata associated with the pattern being evaluated.

        Returns
        -------
        Optional[float]
            Estimated OTOC value in the ``[0, 1]`` range or ``None`` if not
            available.
        """
        # Método 1: Usar valor pré-calculado nos metadados se disponível
        if "otoc" in meta and isinstance(meta.get("otoc"), (int, float)):
            # garantir que o valor de OTOC esteja em [0,1]
            otoc_val = float(meta["otoc"])
            return float(np.clip(otoc_val, 0.0, 1.0))

        # Method 2: compute using the current universe state
        if getattr(self, "universe", None) is None:
            logger.info(
                "MetaLayer (%s): Universe not available in _estimate_otoc. Using fallback %.3f",
                self.layer_id,
                self.cfg.default_metric_value,
            )
            return self.cfg.default_metric_value

        required_methods = [
            "get_current_statevector",
            "calculate_otoc",
            "n_qubits",
        ]
        if not all(hasattr(self.universe, m) for m in required_methods):
            logger.info(
                "MetaLayer (%s): Universe missing required methods (get_current_statevector, calculate_otoc, n_qubits) for _estimate_otoc. Using fallback %.3f",
                self.layer_id,
                self.cfg.default_metric_value,
            )
            return self.cfg.default_metric_value

        try:
            # use get_current_statevector()
            current_statevector = self.universe.get_current_statevector()
            n_qubits_universe = self.universe.n_qubits
            qsp_id = meta.get("packet_id", meta.get("id", "N/A"))  # attempt to fetch ID
            timestamp = meta.get("timestamp", time.time())  # fallback timestamp

            if current_statevector is None:
                logger.info(
                    "MetaLayer (%s): current_statevector is None in _estimate_otoc for qsp_id %s at timestamp %s. OTOC cannot be computed.",
                    self.layer_id,
                    qsp_id,
                    timestamp,
                )
                return self.cfg.default_metric_value

            if n_qubits_universe is None or n_qubits_universe <= 0:
                logger.info(
                    "MetaLayer (%s): invalid n_qubits_universe (%s). Unable to compute OTOC.",
                    self.layer_id,
                    n_qubits_universe,
                )
                return self.cfg.default_metric_value

            # time_step para calculate_otoc. Usar o timestamp do pacote pode ser
            # muito grande ou não representar um "step".
            # Usaremos um valor fixo pequeno ou um valor de 'meta' se disponível
            # e apropriado.
            # O valor original `int(time_step % 1000)` onde time_step era o
            # timestamp do pacote não é ideal.
            # Vamos usar um time_step fixo por enquanto ou permitir que seja
            # passado por 'meta'.
            otoc_time_step = float(
                meta.get("otoc_time_step", self.cfg.default_otoc_time_step)
            )  # usa time_step configurável ou padrão

            # Verificar se o universo possui qubits válidos
            if n_qubits_universe <= 0:
                logger.debug(
                    "MetaLayer (%s): OTOC cannot be calculated for 0 qubits. Using fallback %.3f",
                    self.layer_id,
                    self.cfg.default_metric_value,
                )
                return self.cfg.default_metric_value

            # Selecionar subconjunto de qubits para o cálculo
            num_target_qubits = min(
                max(1, n_qubits_universe // 2), 2, n_qubits_universe
            )
            target_qubits_list = list(range(num_target_qubits))

            logger.debug(
                "MetaLayer (%s): Estimando OTOC com n_qubits=%s, time_step=%s, target_qubits=%s",
                self.layer_id,
                n_qubits_universe,
                otoc_time_step,
                target_qubits_list,
            )

            otoc_val = self.universe.calculate_otoc(
                statevector=current_statevector,  # passa o statevector obtido
                time_step=otoc_time_step,
                n_qubits=n_qubits_universe,
                target_qubits=target_qubits_list,
            )

            if otoc_val is None:
                logger.info(
                    "MetaLayer (%s): self.universe.calculate_otoc retornou None. Usando somente métricas clássicas.",
                    self.layer_id,
                )
                return None

            logger.info(
                "MetaLayer (%s): OTOC calculado via universo: %.4f",
                self.layer_id,
                otoc_val,
            )
            return float(np.clip(otoc_val, 0.0, 1.0))

        except (RuntimeError, ValueError, TypeError) as e:
            logger.error(
                f"MetaLayer ({self.layer_id}): Error computing OTOC in the universe: {e}. "
                f"Using fallback {self.cfg.default_metric_value}."
            )
            return self.cfg.default_metric_value

    def _coherence_l1(self, vec: np.ndarray) -> float:
        """Calculate the L1 coherence of a quantum state vector.

        Parameters
        ----------
        vec : numpy.ndarray
            Input state vector.

        Returns
        -------
        float
            Coherence value normalized to ``[0, 1]``.
        """
        try:
            # Normalizar vetor de entrada
            norm = np.linalg.norm(vec)
            if norm < VN_EIGENVALUE_EPSILON:
                return 0.0

            vector_normalized = vec / norm

            # Identificar vetor intercalado e reconstruir se aplicável
            if detect_interleaved_vector(vector_normalized):
                complex_vec = self._reconstruct_complex_vector(vector_normalized)
                if complex_vec is not None:
                    # Usar vetor complexo reconstruído
                    vector_normalized = complex_vec

            # Criar matriz densidade
            try:
                rho = np.outer(vector_normalized, np.conj(vector_normalized))
            except TypeError:
                # Fallback: tratar como vetor real se a reconstrução falhou ou não foi aplicável
                rho = np.outer(vector_normalized, vector_normalized)

            # Calcular norma L1 da coerência usando vetorização
            # A soma dos valores absolutos fora da diagonal é equivalente a
            # ``np.sum(np.abs(rho - np.diag(np.diag(rho))))``
            coherence = np.sum(np.abs(rho - np.diag(np.diag(rho))))

            # Normalizar: valor máximo teórico é (d-1) para estado máximo coerente, mas aqui é d-1,
            # ou para C_l1, o valor máximo é d-1.
            # A normalização C_l1 / (d-1) é comum.
            d = rho.shape[0]
            coherence_normalized = coherence / (d - 1) if d > 1 else 0.0

            return float(np.clip(coherence_normalized, 0.0, 1.0))

        except (ValueError, TypeError) as e:
            logger.error("Error computing L1 coherence: %s", e)
            return 0.0  # Safe fallback

    def _multipartite_entanglement(self, vec: np.ndarray) -> float:
        """Compute Meyer-Wallach multipartite entanglement.

        Parameters
        ----------
        vec : numpy.ndarray
            State vector whose entanglement is to be evaluated.

        Returns
        -------
        float
            Entanglement value in the ``[0, 1]`` range.
        """
        try:
            norm = np.linalg.norm(vec)
            if norm < VN_EIGENVALUE_EPSILON:
                return 0.0

            vector_normalized = vec / norm
            if len(vector_normalized) % 2 == 0:
                complex_vec = self._reconstruct_complex_vector(vector_normalized)
                if complex_vec is not None:
                    vector_normalized = complex_vec

            n_qubits = int(np.log2(len(vector_normalized)))
            if 2**n_qubits != len(vector_normalized):
                return self.cfg.default_metric_value

            from qiskit.quantum_info import Statevector, partial_trace

            sv = Statevector(vector_normalized)
            purities = []
            for k in range(n_qubits):
                rho = partial_trace(sv, [q for q in range(n_qubits) if q != k])
                pur = np.trace(rho.data @ rho.data).real
                purities.append(pur)

            qval = 2 * (1.0 - sum(purities) / n_qubits)
            return float(np.clip(qval, 0.0, 1.0))
        except Exception as e:  # noqa: BLE001
            logger.error("Error computing multipartite entanglement: %s", e)
            return self.cfg.default_metric_value

    def _assign_symbolic_label(self, score: float) -> str:
        """Map a numerical score to a symbolic label.

        Parameters
        ----------
        score : float
            Numerical score in ``[0, 1]``.

        Returns
        -------
        str
            One of ``"low"``, ``"medium"`` or ``"high"``.
        """
        thresholds = self.cfg.thresholds
        return (
            "high"
            if score >= thresholds.get("high", 0.75)
            else "medium" if score >= thresholds.get("medium", 0.5) else "low"
        )

    def _build_explanation(
        self,
        packet_id: str,
        sim: float,
        ent: float,
        otoc: float,
        coh: float,
        entgl: float,
        score: float,
        label: str,
        meta: Dict[str, Any],
        *,
        use_classical_only: bool = False,
    ) -> Dict[str, Any]:
        """Construct a structured explanation for a pattern evaluation.

        Parameters
        ----------
        packet_id : str
            Identifier of the evaluated packet.
        sim, ent, otoc, coh, entgl : float
            Processed metric values.
        score : float
            Aggregated score.
        label : str
            Symbolic label assigned to the packet.
        meta : Dict[str, Any]
            Additional metadata to include.

        Returns
        -------
        Dict[str, Any]
            Dictionary describing the evaluation.
        """
        # Formato padrão para explicações
        explanation_dict = {
            "packet_id": packet_id,
            "timestamp": time.time(),
            "layer_version": self.cfg.version,
            # Métricas individuais
            "similarity": float(sim),
            "entropy": float(ent),
            "otoc": float(otoc),
            "coherence": float(coh),
            "entanglement": float(entgl),
            # Resultado final
            "quantum_score": float(score),
            "symbolic_label": label,
            # Configuração usada
            "weights": {
                "similarity": float(self.cfg.weight_similarity),
                "entropy": float(self.cfg.weight_entropy),
                "otoc": float(self.cfg.weight_otoc),
                "coherence": float(self.cfg.weight_coherence),
                "entanglement": float(self.cfg.weight_entanglement),
            },
            # Contexto do padrão
            "pattern_context": {
                "n_qubits": meta.get("n_qubits", "N/A"),
                "vector_type": meta.get("vector_type", "unknown"),
                "method": meta.get("method", "unknown"),
            },
            "raw_metadata_preview": {
                k: str(v)[:100]
                for k, v in meta.items()
                if k not in ["vector", "id", "timestamp"]
            },
            "use_classical_metrics_only": use_classical_only,
        }

        return explanation_dict

    def learn_from_feedback(self, packet_id: str, correct_label: str) -> bool:
        """Update metric weights based on external feedback.

        Parameters
        ----------
        packet_id : str
            Identifier of the evaluated packet.
        correct_label : str
            Correct label (``"low"``, ``"medium"`` or ``"high"``).

        Returns
        -------
        bool
            ``True`` if learning was successfully applied.
        """
        # Validate label
        if correct_label not in ["low", "medium", "high"]:
            logger.info(
                "Invalid label: %s. Must be low, medium or high.",
                correct_label,
            )
            return False

        # Find the entry in the log
        entry = next(
            (
                e
                for e in self.log
                if e.get("packet_id") == packet_id
                and e.get("type", "evaluation") == "evaluation"
            ),
            None,
        )
        if not entry:
            logger.info(
                "Evaluation packet with ID %s not found in history.",
                packet_id,
            )
            return False

        # Convert label to target numerical value
        target_values = {"low": 0.25, "medium": 0.625, "high": 0.875}
        target_score = target_values[correct_label]

        # Get the current score
        current_score = entry.get("quantum_score", 0.0)

        # Compute the error
        error = target_score - current_score

        # If the error is small, no adjustment is needed
        if abs(error) < 0.05:
            logger.info(
                "Small error (%.3f), skipping weight adjustment for %s.",
                error,
                packet_id,
            )
            return True

        # Determine adjustment direction
        adjust_direction = np.sign(error)

        # Learning rate (increased to produce more noticeable adjustments)
        learning_rate = 0.2

        # Extrair métricas e pesos atuais
        metrics_values = {
            "similarity": entry.get("similarity", 0.0),
            "entropy": entry.get("entropy", 0.5),
            "otoc": entry.get("otoc", 0.5),
            "coherence": entry.get("coherence", 0.0),
        }

        current_weights = {
            "similarity": self.cfg.weight_similarity,
            "entropy": self.cfg.weight_entropy,
            "otoc": self.cfg.weight_otoc,
            "coherence": self.cfg.weight_coherence,
            "entanglement": self.cfg.weight_entanglement,
        }

        new_weights_map = {}

        # Ajuste simplificado: Aumenta/diminui pesos das métricas que "puxariam" o score na direção correta.
        if metrics_values["similarity"] > 0.5:
            new_weights_map["similarity"] = current_weights["similarity"] * (
                1 + adjust_direction * learning_rate
            )
        else:
            new_weights_map["similarity"] = current_weights["similarity"] * (
                1 - adjust_direction * learning_rate
            )

        if (1.0 - metrics_values["entropy"]) > 0.5:
            new_weights_map["entropy"] = current_weights["entropy"] * (
                1 + adjust_direction * learning_rate
            )
        else:
            new_weights_map["entropy"] = current_weights["entropy"] * (
                1 - adjust_direction * learning_rate
            )

        if (1.0 - metrics_values["otoc"]) > 0.5:
            new_weights_map["otoc"] = current_weights["otoc"] * (
                1 + adjust_direction * learning_rate
            )
        else:
            new_weights_map["otoc"] = current_weights["otoc"] * (
                1 - adjust_direction * learning_rate
            )

        if metrics_values["coherence"] > 0.5:
            new_weights_map["coherence"] = current_weights["coherence"] * (
                1 + adjust_direction * learning_rate
            )
        else:
            new_weights_map["coherence"] = current_weights["coherence"] * (
                1 - adjust_direction * learning_rate
            )

        if metrics_values.get("entanglement", 0.5) > 0.5:
            new_weights_map["entanglement"] = current_weights["entanglement"] * (
                1 + adjust_direction * learning_rate
            )
        else:
            new_weights_map["entanglement"] = current_weights["entanglement"] * (
                1 - adjust_direction * learning_rate
            )

        total_new_weight = 0
        for metric_key in new_weights_map:
            new_weights_map[metric_key] = np.clip(
                new_weights_map[metric_key], 0.01, 1.0
            )
            total_new_weight += new_weights_map[metric_key]

        if total_new_weight > 1e-9:
            for metric_key in new_weights_map:
                new_weights_map[metric_key] /= total_new_weight
        else:
            logger.warning(
                "Aviso: Soma dos novos pesos muito baixa (%.6f). Resetando para pesos default.",
                total_new_weight,
            )
            default_cfg = MetacognitionConfig()
            new_weights_map["similarity"] = default_cfg.weight_similarity
            new_weights_map["entropy"] = default_cfg.weight_entropy
            new_weights_map["otoc"] = default_cfg.weight_otoc
            new_weights_map["coherence"] = default_cfg.weight_coherence
            new_weights_map["entanglement"] = default_cfg.weight_entanglement
            temp_total = sum(new_weights_map.values())
            if not np.isclose(temp_total, 1.0) and temp_total > 1e-9:
                for mk in new_weights_map:
                    new_weights_map[mk] /= temp_total

        # Nova etapa: garantir que após a normalização nenhum peso fique abaixo do mínimo
        min_weight = 0.01
        # Verificar se algum peso ficou abaixo do mínimo após a normalização
        below_min = [k for k, v in new_weights_map.items() if v < min_weight]

        # Se algum peso estiver abaixo do mínimo, ajustar iterativamente
        while below_min:
            # Total de déficit que precisamos recuperar
            deficit = sum([min_weight - new_weights_map[k] for k in below_min])

            # Ajustar pesos abaixo do mínimo para o valor mínimo
            for k in below_min:
                new_weights_map[k] = min_weight

            # Calcular quais pesos estão acima do mínimo para redistribuir o déficit
            above_min = [
                k
                for k, v in new_weights_map.items()
                if v > min_weight and k not in below_min
            ]

            if not above_min:
                # Caso extremo: todos os pesos estão no mínimo
                # Distribuir igualmente
                for k in new_weights_map:
                    new_weights_map[k] = 1.0 / len(new_weights_map)
                break

            # Calcular soma dos pesos acima do mínimo
            sum_above = sum([new_weights_map[k] for k in above_min])

            # Reduzir proporcionalmente os pesos acima do mínimo
            for k in above_min:
                reduction_factor = 1.0 - (deficit / sum_above)
                new_weights_map[k] *= reduction_factor

            # Verificar novamente se algum peso ficou abaixo do mínimo
            below_min = [k for k, v in new_weights_map.items() if v < min_weight]

        # Normalizar uma última vez para garantir soma = 1.0
        total_final = sum(new_weights_map.values())
        if not np.isclose(total_final, 1.0) and total_final > 1e-9:
            for k in new_weights_map:
                new_weights_map[k] /= total_final

        # Atualizar os pesos na configuração
        self.cfg.weight_similarity = new_weights_map["similarity"]
        self.cfg.weight_entropy = new_weights_map["entropy"]
        self.cfg.weight_otoc = new_weights_map["otoc"]
        self.cfg.weight_coherence = new_weights_map["coherence"]
        self.cfg.weight_entanglement = new_weights_map["entanglement"]

        logger.info(
            "Pesos ajustados para %s. Novos pesos: S=%.3f, E=%.3f, O=%.3f, C=%.3f, Ent=%.3f",
            packet_id,
            self.cfg.weight_similarity,
            self.cfg.weight_entropy,
            self.cfg.weight_otoc,
            self.cfg.weight_coherence,
            self.cfg.weight_entanglement,
        )

        # Registrar o aprendizado no log
        learning_entry = {
            "timestamp": time.time(),
            "type": "learning_feedback",
            "packet_id": packet_id,
            "correct_label": correct_label,
            "current_score_before_learn": current_score,
            "target_score_for_learn": target_score,
            "error_value": error,
            "old_weights": current_weights,
            "new_weights": {
                "similarity": self.cfg.weight_similarity,
                "entropy": self.cfg.weight_entropy,
                "otoc": self.cfg.weight_otoc,
                "coherence": self.cfg.weight_coherence,
                "entanglement": self.cfg.weight_entanglement,
            },
        }

        self.log.append(learning_entry)
        if len(self.log) > self.max_log_entries:
            excess = len(self.log) - self.max_log_entries
            del self.log[:excess]
        return True

    def get_evaluation_log(self) -> List[Dict[str, Any]]:
        """Return a copy of the evaluation and learning log.

        Returns
        -------
        List[Dict[str, Any]]
            List of recorded evaluation entries.
        """

        return copy.deepcopy(self.log)

    def clear_log(self) -> None:
        """Clear the evaluation and learning history."""
        self.log.clear()

    def train_neural_model(self) -> None:
        """Train a neural model using the evaluation history."""
        from .neural_learning import MetacognitionNeuralLearner

        learner = MetacognitionNeuralLearner()
        learner.fit(self.log)
        self.neural_learner = learner

    def predict_label_with_model(self, metrics: Dict[str, float]) -> Optional[str]:
        """Predict a symbolic label using the trained neural model.

        Parameters
        ----------
        metrics : Dict[str, float]
            Metric values used as model input.

        Returns
        -------
        Optional[str]
            Predicted label or ``None`` if the model is unavailable.
        """
        if not hasattr(self, "neural_learner") or self.neural_learner is None:
            return None
        return self.neural_learner.predict_label(metrics)

    def receive_decision_feedback(
        self, packet_id: str, pnl_outcome: float, neutral_threshold: float = 0.0
    ) -> bool:
        """Integra feedback de módulos de decisão e ajusta os pesos.

        Parameters
        ----------
        packet_id
            Identificador do ``QuantumSignaturePacket`` avaliado previamente.
        pnl_outcome
            Resultado financeiro associado à decisão. Valores positivos
            indicam ganho e negativos indicam perda.
        neutral_threshold
            Faixa de tolerância ao redor de zero considerada neutra. Quando o
            ``pnl_outcome`` estiver dentro desse intervalo, o rótulo
            ``"medium"`` será utilizado.

        Returns
        -------
        bool
            ``True`` se o aprendizado foi aplicado com sucesso.
        """

        if pnl_outcome > neutral_threshold:
            label = "high"
        elif pnl_outcome < -neutral_threshold:
            label = "low"
        else:
            label = "medium"

        return self.learn_from_feedback(packet_id, label)

    def _compute_anomaly_score(self, metrics: Dict[str, float]) -> float:
        """Calculate an anomaly score from metric z-scores.

        Parameters
        ----------
        metrics : Dict[str, float]
            Metric values from the current pattern.

        Returns
        -------
        float
            Mean anomaly score where ``0.0`` represents a regular pattern.
        """

        if len(self.log) < self.cfg.min_history_for_anomaly:
            return 0.0

        history = self.log[:-1]  # Ignora a avaliação corrente se já registrada
        z_scores = []

        for key, value in metrics.items():
            hist_vals = [
                entry.get(key)
                for entry in history
                if isinstance(entry.get(key), (int, float))
            ]
            if len(hist_vals) < 2:
                continue
            mean_val = float(np.mean(hist_vals))
            std_val = float(np.std(hist_vals))
            if std_val < 1e-6:
                continue
            z_scores.append(abs((value - mean_val) / std_val))

        if not z_scores:
            return 0.0

        return float(np.mean(z_scores))

    def detect_anomaly(
        self,
        packet_or_tuple: Union[
            "QuantumSignaturePacket", Tuple[List[float], Dict[str, Any]]
        ],
    ) -> Dict[str, Any]:
        """Evaluate a pattern and determine whether it is an anomaly.

        Parameters
        ----------
        packet_or_tuple : Union[QuantumSignaturePacket, Tuple[List[float], Dict[str, Any]]]
            Packet instance or ``(vector, metadata)`` tuple.

        Returns
        -------
        Dict[str, Any]
            Evaluation result including ``anomaly_score`` and ``is_anomaly``.
        """

        evaluation = self.evaluate_pattern(packet_or_tuple)

        metrics = {
            "similarity": evaluation.get("similarity", 0.0),
            "entropy": evaluation.get("entropy", self.cfg.default_metric_value),
            "otoc": evaluation.get("otoc", self.cfg.default_metric_value),
            "coherence": evaluation.get("coherence", DEFAULT_COHERENCE_VALUE),
            "entanglement": evaluation.get(
                "entanglement", self.cfg.default_metric_value
            ),
        }

        anomaly_score = self._compute_anomaly_score(metrics)
        is_anomaly = anomaly_score >= self.cfg.anomaly_threshold

        evaluation["anomaly_score"] = anomaly_score
        evaluation["is_anomaly"] = is_anomaly
        evaluation["type"] = "anomaly_detection"

        return evaluation
