# coding: utf-8
"""
QUALIAMetacognitionTrading - Sistema de Metacognição Quântica para Trading

Este módulo implementa o sistema de metacognição que permite ao QUALIA Trading
observar, analisar e melhorar seu próprio processo decisório, criando um ciclo
de aprendizado contínuo. Quando o mercado se mantém estável por vários ciclos,
o mecanismo de cooldown aumenta a confiança gradualmente e reinicia parâmetros
da ACE, prevenindo ajustes excessivos. O cooldown é disparado apenas quando as
diretivas de trading consecutivas são "HOLD" ou "NO_SIGNAL".
"""

from ..config.feature_flags import feature_toggle
from ..config.metacognition_defaults import load_metacognition_defaults
from ..memory.event_bus import SimpleEventBus
from ..events import CoherenceGuardFreeze, CoherenceGuardUnfreeze
from ..utils.logger import get_logger

try:
    from opentelemetry import trace
except Exception:  # pragma: no cover - optional dependency
    trace = None
import json
import uuid
from collections import deque
from dataclasses import asdict, dataclass, field
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict, List, Literal, Optional, Tuple, Union

import numpy as np
try:
    import yaml
except ModuleNotFoundError:
    class YAMLError(Exception):
        pass

    class DummyYAML:
        def safe_load(self, stream):
            return {}
        @property
        def YAMLError(self):
            return YAMLError

    yaml = DummyYAML()

from ..common_types import QuantumSignaturePacket  # usado pela QPM

# Módulo de análise principal
from ..core.consciousness import QUALIAConsciousness
from ..core.universe import QUALIAQuantumUniverse  # ➡️ ADICIONADO (para type hint)
from ..memory.quantum_pattern_memory import QuantumPatternMemory
from ..metacognition.layer import MetacognitionConfig, QuantumMetacognitionLayer
from ..utils.persistence import (
    convert_to_serializable,
    load_json_safe,
    save_json_safe,
)
from ..config.settings import DEFAULT_CACHE_DIR, get_env

logger = get_logger(__name__)

# Minimum number of scores required to adapt thresholds using history
MIN_SCORE_WINDOW = 5
# Minimum PnL history length required for analysis
MIN_HISTORY_LENGTH = 5
# Configurações padrão
DEFAULT_LOOKBACK_HOURS = 24


@dataclass
class QASTTuningDirectives:
    """Diretivas para modular o QAST.
    exploration_bias_factor: Multiplica a força de mutação base (ex: 1.0 = sem mudança, >1 aumenta, <1 diminui).
    selective_pressure_adjustment: Adiciona à taxa de elite base (ex: 0.0 = sem mudança, >0 aumenta, <0 diminui).
    """

    exploration_bias_factor: float = 1.0
    selective_pressure_adjustment: float = 0.0  # Ajuste aditivo na taxa de elite
    # Outras diretivas podem ser adicionadas, ex: reset_population_trigger,
    # change_fitness_function_weights


@dataclass
class ACEModulationParameters:
    """Parâmetros para modular o AdaptiveConsciousnessEvolution.
    adaptation_rate_factor: Multiplica a taxa de adaptação base do ACE.
    universe_target_complexity: Sugestão para o nível de complexidade do universo ('increase', 'decrease', 'maintain').
    """

    adaptation_rate_factor: float = 1.0
    # 'increase', 'decrease', 'maintain', ou None para sem sugestão
    universe_target_complexity: Optional[str] = None
    override_on_metacognition: bool = False
    qubit_increase: int = 0
    # Quando verdadeiro, o ACE não deve realizar mutações de circuito
    # neste ciclo de adaptação.
    skip_circuit_mutation: bool = False

    def to_dict(self) -> Dict[str, Any]:
        """Converte parâmetros para dicionário."""
        return asdict(self)


@dataclass
class DecisionContext:
    """
    Estrutura de dados para encapsular o contexto de uma decisão de trading
    a ser avaliada pelo sistema de metacognição.
    """

    quantum_signature_packet: QuantumSignaturePacket  # pacote quântico obrigatório
    market_regime: str = "undefined"
    quantum_metrics_at_decision: Dict[str, float] = field(
        default_factory=dict
    )  # métricas como entropia, OTOC, coerência
    trade_details: Dict[str, Any] = field(default_factory=dict)  # símbolo, preço etc.
    current_universe_statevector: Optional[np.ndarray] = None
    latest_universe_metrics: Optional[Dict[str, Any]] = field(default_factory=dict)
    timestamp: datetime = field(
        default_factory=lambda: datetime.now(timezone.utc)
    )  # para rastreabilidade

    def __post_init__(self):
        # Validação mínima de exemplo (pode ser expandida)
        if not self.market_regime:
            logger.warning("DecisionContext: market_regime não definido.")
        if not isinstance(self.quantum_signature_packet, QuantumSignaturePacket):
            # Isso levantaria um TypeError na instanciação do dataclass se o tipo não bater,
            # mas uma checagem explícita pode ser útil para logs ou comportamento customizado.
            # No entanto, para "validação mínima" de dataclass, a checagem de tipo já é feita.
            # Vamos manter simples por agora, confiando na tipagem.
            pass


@dataclass
class TradeSignal:
    """
    Representa um sinal de trading gerado pelo sistema de metacognição.
    """

    signal_type: Literal["BUY", "SELL", "HOLD", "REDUCE_EXPOSURE", "NO_SIGNAL"]
    confidence: float = 0.0  # Confiança no sinal, de 0.0 a 1.0
    symbol: Optional[str] = None  # Símbolo do ativo
    # Preço sugerido para a ordem (pode ser None para ordens a mercado)
    suggested_price: Optional[float] = None
    # Quantidade sugerida ou percentual da posição/capital
    suggested_quantity_specifier: Optional[Union[float, str]] = None
    # ID do MetacognitiveContext que originou este sinal
    origin_metacognitive_context_id: Optional[str] = None
    # Detalhes adicionais ou justificativa para o sinal
    reasoning: Optional[str] = None
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))


@dataclass
class MetacognitiveContext:
    """
    Estrutura de dados para encapsular os insights e diretivas gerados
    pelo sistema de metacognição a cada ciclo.
    """

    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    # Confiança geral no estado/decisões atuais do sistema (0.0 a 1.0)
    overall_system_confidence_level: float = 0.5
    # Ex: 'trending_up', 'ranging', 'volatile_spike', 'calm'
    identified_market_regime: str = "undefined"
    # Quão relevante/forte é o insight da QPM (0.0 a 1.0)
    qpm_insight_relevance_score: float = 0.0
    # Ex: 'alta_confianca_positiva', 'baixa_confianca_mista', 'sem_historico'
    qpm_feedback_type: str = "no_feedback"
    qast_tuning_directives: QASTTuningDirectives = field(
        default_factory=QASTTuningDirectives
    )
    ace_modulation_parameters: ACEModulationParameters = field(
        default_factory=ACEModulationParameters
    )
    # Fator para modular risco (ex: 0.8 a 1.2)
    dynamic_risk_adjustment_factor: float = 1.0
    # Diretiva de trading ativa (ex: 'REDUCE_EXPOSURE')
    trade_directive: Optional[str] = None
    raw_qpm_feedback_details: Dict[str, Any] = field(
        default_factory=dict
    )  # detalhes brutos do feedback da QPM
    # Insights do QualiaAnalysisCore
    internal_system_health: Optional[Dict[str, Any]] = None
    error_messages: List[str] = field(default_factory=list)
    alert_state: Optional[str] = None


@dataclass
class MetacognitionResult:
    """Container for the output of ``run_with_qast_feedback``."""

    trade_signal: TradeSignal
    context: MetacognitiveContext


class QUALIAMetacognitionTrading:
    """
    Sistema de Metacognição Quântica para Trading.
    """

    def __init__(
        self,
        adaptive_consciousness_evolution: Any,  # Mantido Any por enquanto para ACE
        qpm_memory: QuantumPatternMemory,
        initial_pnl_data: Optional[List[float]] = None,
        config: Optional[Union[Dict[str, Any], str]] = None,
        qualia_analysis_module: Optional[QUALIAConsciousness] = None,
        universe: Optional[QUALIAQuantumUniverse] = None,  # ➡️ ADICIONADO
        event_bus: Optional[SimpleEventBus] = None,
    ):
        self.pnl_history = list(initial_pnl_data) if initial_pnl_data else []
        # Log de decisões e seus contextos
        self.decision_log: List[Dict[str, Any]] = []

        loaded_config: Dict[str, Any] = {}
        if config is None:
            loaded_config = load_metacognition_defaults()
        elif isinstance(config, str):
            try:
                with open(config, "r", encoding="utf-8") as cfg_file:
                    if config.endswith((".yaml", ".yml")):
                        loaded_config = yaml.safe_load(cfg_file) or {}
                    else:
                        loaded_config = json.load(cfg_file)
            except (OSError, yaml.YAMLError, json.JSONDecodeError) as exc:
                logger.error(
                    f"Erro ao carregar arquivo de configuração {config}: {exc}",
                    exc_info=True,
                )
                loaded_config = {}
        elif isinstance(config, dict):
            loaded_config = config

        require_full_stack = loaded_config.get("require_full_stack", True)
        self.config = loaded_config
        self.universe = universe
        if self.universe is None:
            raise ValueError("QUALIAMetacognitionTrading requires a universe object")

        if adaptive_consciousness_evolution is None and qualia_analysis_module is None:
            if require_full_stack:
                raise ValueError(
                    "AdaptiveConsciousnessEvolution and QualiaAnalysisCore are required when require_full_stack=True"
                )
            from ..adaptive_evolution import AdaptiveConsciousnessEvolution
            from ..config.ace_config import load_ace_config
            from ..config.config_loader import load_env_and_json
            from ..config.settings import get_config_file_path
            from ..core.unified_qualia_consciousness import UnifiedQUALIAConsciousness

            ace_cfg = load_ace_config(
                str(get_config_file_path("adaptive_evolution.yaml"))
            )
            adaptive_consciousness_evolution = AdaptiveConsciousnessEvolution(
                qualia_universe=self.universe,
                config=ace_cfg,
            )
            uq_cfg = load_env_and_json(
                json_path=str(
                    get_config_file_path("unified_qualia_consciousness_config.yaml")
                )
            )
            qualia_analysis_module = UnifiedQUALIAConsciousness(
                config=uq_cfg,
                symbols=uq_cfg.get("system", {}).get("symbols", []),
                timeframes=uq_cfg.get("system", {}).get("timeframes", []),
                capital=uq_cfg.get("system", {}).get("capital", 10000.0),
                mode=uq_cfg.get("system", {}).get("mode", "paper_trading"),
            )

        self.adaptive_consciousness_evolution = adaptive_consciousness_evolution
        self.qualia_analysis_module = qualia_analysis_module
        self.max_history_size = loaded_config.get("max_pnl_history_size", 200)
        self.min_history_length = int(
            self.config.get("min_pnl_history_length", MIN_HISTORY_LENGTH)
        )
        self.qpm_memory = qpm_memory
        self.event_bus = event_bus
        self.publish_events = feature_toggle("metacog_events")
        self.frozen: bool = False
        if self.event_bus is not None:
            self.event_bus.subscribe("nexus.coherence_guard.freeze", self._on_freeze)
            self.event_bus.subscribe(
                "nexus.coherence_guard.unfreeze", self._on_unfreeze
            )

        # Estado para detecção de estabilidade e cooldown
        self._stable_cycle_count: int = 0
        self.cooldown_threshold: int = int(self.config.get("cooldown_threshold", 3))
        self.confidence_cooldown_step: float = float(
            self.config.get("cooldown_confidence_step", 0.05)
        )

        # Estado para monitorar ciclos consecutivos sem mutações no ACE por falta de padrões na QPM
        self._consecutive_skip_cycles: int = 0
        self.skip_circuit_mutation_threshold: int = int(
            self.config.get("skip_circuit_mutation_threshold", 3)
        )
        self.skip_circuit_mutation_propagation_cycles: int = int(
            self.config.get("skip_circuit_mutation_propagation_cycles", 2)
        )
        self._throttled_cycles_remaining: int = 0
        self.skip_mutation_reenable_cycles: int = int(
            self.config.get("skip_mutation_reenable_cycles", 10)
        )
        self._cycles_without_improvement: int = 0

        # Estruturas para insights e histórico de ajustes
        self.metacognitive_insights: List[Dict[str, Any]] = []
        self.adjustment_history: List[Dict[str, Any]] = []
        self.min_confidence: float = float(
            self.config.get("metacognition_min_confidence", 0.65)
        )

        self.quantum_score_window_size: int = int(
            self.config.get("quantum_score_window_size", 50)
        )
        self.quantum_score_sigma: float = float(
            self.config.get("quantum_score_sigma", 0.02)
        )
        """Fallback thresholds used when insufficient history exists."""
        self.buy_score_threshold: float = float(
            self.config.get("buy_score_threshold", 0.42)
        )
        self.sell_score_threshold: float = float(
            self.config.get("sell_score_threshold", 0.35)
        )
        self.blocked_signal_log_path: Optional[str] = self.config.get(
            "blocked_signal_log_path"
        )
        self.blocked_signals: List[Dict[str, Any]] = []
        self.low_confidence_alert: bool = False
        self._quantum_score_window: deque = deque(maxlen=self.quantum_score_window_size)

        cache_dir = Path(
            self.config.get(
                "cache_dir",
                get_env("QUALIA_CACHE_DIR", DEFAULT_CACHE_DIR, warn=False),
            )
        )
        self.quantum_score_history_file: Path = Path(
            self.config.get(
                "quantum_score_history_file",
                cache_dir / "quantum_score_history.json",
            )
        )
        self.quantum_score_persist_interval: int = int(
            self.config.get("quantum_score_persist_interval", 10)
        )
        self._quantum_score_update_counter: int = 0
        self._load_quantum_score_history()

        # Caminho para o histórico persistido em JSON
        self.history_path: Optional[str] = self.config.get("history_path")
        if self.history_path:
            self.load_history(self.history_path)

        # Estado para último sinal e contexto gerados
        self.last_trade_signal: Optional[TradeSignal] = None
        self.last_metacognitive_context: Optional[MetacognitiveContext] = None

        # ➡️ Adapter: injeta a low-layer
        meta_layer_config_dict = self.config.get("metacognition_layer_config")
        if meta_layer_config_dict and not isinstance(meta_layer_config_dict, dict):
            logger.warning(
                "metacognition_layer_config em config deveria ser um dict, "
                f"mas é {type(meta_layer_config_dict)}. Usando config default "
                "para QuantumMetacognitionLayer."
            )
            meta_layer_config_dict = None

        final_meta_config = None
        if meta_layer_config_dict:
            try:
                final_meta_config = MetacognitionConfig(**meta_layer_config_dict)
            except (TypeError, ValueError) as e:
                logger.error(
                    "Erro ao criar MetacognitionConfig com meta_layer_config_dict: "
                    f"{e}. Usando config default. Detalhes: {meta_layer_config_dict}",
                    exc_info=True,
                )
                final_meta_config = None  # Garantir fallback para default da Layer

        # QuantumMetacognitionLayer requer um objeto Universe. Se não for fornecido
        # ao inicializar QUALIAMetacognitionTrading, a criação da camada não deve
        # prosseguir para evitar falhas posteriores.
        if self.universe is None:
            raise ValueError("QUALIAMetacognitionTrading requires a universe object")

        if not hasattr(self, "meta_layer") or self.meta_layer is None:
            self.meta_layer = QuantumMetacognitionLayer(
                universe=self.universe,  # type: ignore
                pattern_memory=self.qpm_memory,
                config=final_meta_config,  # Passa o objeto MetacognitionConfig ou None
            )
            logger.info("QuantumMetacognitionLayer inicializada dinamicamente.")

        logger.info(
            (
                "QUALIAMetacognitionTrading inicializado. "
                f"ACE: {'Presente' if self.adaptive_consciousness_evolution else 'Ausente'}, "
                f"QPM: {'Presente' if self.qpm_memory else 'Ausente'}, "
                f"QACore: {'Presente' if self.qualia_analysis_module else 'Ausente'}, "
                f"Universe: {'Presente' if self.universe else 'Ausente'}, "
                f"MetaLayer: {'Presente' if hasattr(self, 'meta_layer') and self.meta_layer else 'Ausente'}"
            )
        )

    def get_last_metacognitive_context(self) -> Optional[MetacognitiveContext]:
        """Retorna o último contexto gerado pelo sistema."""
        return self.last_metacognitive_context

    def get_last_trade_signal(self) -> Optional[TradeSignal]:
        """Retorna o último ``TradeSignal`` produzido."""
        return self.last_trade_signal

    def log_decision_and_pnl(
        self,
        decision_details: Dict,
        decision_context: Dict,
        quantum_metrics: Dict,
        pnl_feedback: Optional[float],
    ):
        """Registra a decisão, seu contexto, métricas quânticas e o PnL resultante."""
        log_entry = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "decision_details": decision_details,
            "decision_context": decision_context,  # Contexto mais amplo que levou à decisão
            "quantum_metrics_at_decision": quantum_metrics,
            "pnl_feedback": pnl_feedback,
        }
        self.decision_log.append(log_entry)
        if len(self.decision_log) > self.max_history_size:
            self.decision_log.pop(0)

        if pnl_feedback is not None:
            self.pnl_history.append(pnl_feedback)

    def run_with_qast_feedback(
        self,
        decision_context: DecisionContext,  # utiliza dataclass
        pnl_feedback: Optional[float],
    ) -> Optional[MetacognitionResult]:
        """Processa feedback do QAST e retorna ``MetacognitionResult``.

        Esta interface permanece pública e delega as etapas para métodos
        privados que executam a análise da QPM, consulta à
        ``QUALIAConsciousness`` e geração do ``TradeSignal``.
        """
        if not self.config.get("enabled", True):
            logger.info(
                "QUALIAMetacognitionTrading está desabilitada por configuração."
            )
            return None  # retorna None se desabilitado

        if self.frozen:
            logger.info(
                "QUALIAMetacognitionTrading congelada pelo CoherenceGuard; pulando ciclo"
            )
            return None

        if trace:
            tracer = trace.get_tracer(__name__)
            span_cm = tracer.start_as_current_span(
                "metacog.run_with_qast_feedback",
                attributes={"market_regime": decision_context.market_regime},
            )
        else:
            from contextlib import nullcontext

            span_cm = nullcontext()

        with span_cm:
            qmt_logger = logger
            qmt_logger.info(
                f"QMT: Iniciando ciclo. Contexto (market_regime): {decision_context.market_regime}, PnL: {pnl_feedback}"
            )

            # ID único para rastrear a origem do TradeSignal
            current_metacognitive_run_id = f"mctx_{uuid.uuid4().hex[:8]}_{datetime.now(timezone.utc).strftime('%Y%m%d%H%M%S')}"

            ctx = MetacognitiveContext(
                timestamp=datetime.now(timezone.utc),
                # Poderia-se usar o ID gerado aqui também se o MetacognitiveContext tivesse um campo id
            )
            ctx.identified_market_regime = decision_context.market_regime

            # Assumindo que decision_context já contém tudo que log_decision_and_pnl precisa
            self.log_decision_and_pnl(
                decision_details=decision_context.trade_details,
                decision_context=asdict(
                    decision_context
                ),  # Logar o contexto completo como dict
                quantum_metrics=decision_context.quantum_metrics_at_decision,
                pnl_feedback=pnl_feedback,
            )
            # 1. Análise da QPM e feedback para ACE
            qpm_result = self._process_qpm_feedback(decision_context, ctx, qmt_logger)
            qpm_feedback_type = qpm_result.get("type", "qpm_nao_disponivel")
            qpm_confidence = qpm_result.get("confidence", 0.0)

            self._handle_skip_mutation_throttling(ctx, qmt_logger)

            # 2. Consulta ao QUALIAConsciousness
            self._consult_qualia_consciousness(decision_context, ctx, qmt_logger)

            # 3. Geração do TradeSignal
            final_trade_signal = self._generate_trade_signal(
                decision_context,
                ctx,
                qpm_feedback_type,
                qpm_confidence,
                current_metacognitive_run_id,
                qmt_logger,
                pnl_feedback,
            )

            self._apply_cooldown_logic(ctx, final_trade_signal)

            self.last_trade_signal = final_trade_signal
            self.last_metacognitive_context = ctx

            if self.qpm_memory:
                try:
                    if (
                        decision_context.quantum_signature_packet
                        and decision_context.quantum_signature_packet.vector
                    ):
                        self.qpm_memory.store_pattern(
                            quantum_signature_packet=decision_context.quantum_signature_packet,
                            market_snapshot=decision_context.trade_details,
                            outcome=(
                                {"pnl_feedback": pnl_feedback}
                                if pnl_feedback is not None
                                else {}
                            ),
                            decision_context=asdict(decision_context),
                        )
                    else:
                        logger.warning(
                            "QMT: QuantumSignaturePacket ausente ou vetor vazio; pattern nao sera armazenado na QPM."
                        )
                except Exception as exc:
                    logger.error(
                        "QMT: erro ao armazenar padrão na QPM: %s", exc, exc_info=True
                    )

            result = MetacognitionResult(trade_signal=final_trade_signal, context=ctx)

        if self.event_bus and self.publish_events:
            self.event_bus.publish(
                "metacog.step_completed",
                {
                    "context": asdict(ctx),
                    "signal": result.trade_signal.signal_type,
                    "confidence": result.trade_signal.confidence,
                },
            )
            self.event_bus.publish(
                "metacog.trade_signal",
                {
                    "signal": result.trade_signal.signal_type,
                    "confidence": result.trade_signal.confidence,
                    "origin": result.trade_signal.origin_metacognitive_context_id,
                },
            )
        return result

    def _process_qpm_feedback(
        self,
        decision_context: DecisionContext,
        ctx: MetacognitiveContext,
        qmt_logger,
    ) -> Dict[str, Any]:
        """Analisa a QPM e envia feedback ao ACE."""
        qpm_feedback_type = "qpm_nao_disponivel"
        qpm_confidence = 0.0
        qpm_relevance_score = 0.0
        raw_qpm_details: Dict[str, Any] = {}

        if (
            decision_context.quantum_signature_packet
            and decision_context.quantum_signature_packet.vector
            and hasattr(self, "meta_layer")
            and self.meta_layer
        ):
            qmt_logger.debug(
                "QMT: Consultando QPM e MetaLayer com QuantumSignaturePacket do DecisionContext."
            )
            try:
                analysis_result_qpm = self._analyze_qpm_patterns(
                    decision_context.quantum_signature_packet
                )
                qpm_feedback_type = analysis_result_qpm.get("type", "erro_analise_qpm")
                qpm_confidence = analysis_result_qpm.get("confidence", 0.0)
                qpm_relevance_score = analysis_result_qpm.get(
                    "relevance",
                    qpm_confidence,
                )
                raw_qpm_details = analysis_result_qpm.get("details", {})
                similar_patterns = analysis_result_qpm.get("similar_patterns", [])
                if not similar_patterns:
                    ctx.ace_modulation_parameters.skip_circuit_mutation = True
                    qmt_logger.info(
                        "QMT: Nenhum padrao similar encontrado; ACE nao sera atualizado neste ciclo."
                    )
                else:
                    ctx.ace_modulation_parameters.skip_circuit_mutation = False
            except (ValueError, RuntimeError) as e:
                qmt_logger.error(
                    f"QMT: Erro ao consultar ou analisar padrões da QPM/MetaLayer: {e}",
                    exc_info=True,
                )
                qpm_feedback_type = "erro_consulta_qpm"
                ctx.error_messages.append(f"QPM Query: {str(e)}")
                raw_qpm_details = {"error": str(e)}
        else:
            qmt_logger.debug(
                "QMT: QuantumSignaturePacket ausente ou vetor vazio, ou meta_layer ausente. Consulta à QPM ignorada."
            )
            qpm_feedback_type = "sem_assinatura_quantica_valida_ou_layer_ausente"

        ctx.qpm_feedback_type = qpm_feedback_type
        ctx.qpm_insight_relevance_score = qpm_relevance_score
        ctx.raw_qpm_feedback_details = raw_qpm_details

        if self.adaptive_consciousness_evolution and hasattr(
            self.adaptive_consciousness_evolution, "receive_metacognitive_feedback"
        ):
            self.adaptive_consciousness_evolution.receive_metacognitive_feedback(
                feedback_type=qpm_feedback_type,
                confidence_score=qpm_confidence,
                details=raw_qpm_details,
            )
            qmt_logger.info(
                f"QMT: Feedback da QPM (Tipo: {qpm_feedback_type}, Conf: {qpm_confidence:.2f}) enviado para ACE."
            )

        return {
            "type": qpm_feedback_type,
            "confidence": qpm_confidence,
            "relevance": qpm_relevance_score,
            "details": raw_qpm_details,
        }

    def _consult_qualia_consciousness(
        self,
        decision_context: DecisionContext,
        ctx: MetacognitiveContext,
        qmt_logger,
    ) -> None:
        """Consulta o ``QUALIAConsciousness`` para obter insights."""
        if not self.qualia_analysis_module:
            return

        qmt_logger.debug("QMT: Consultando QualiaAnalysisCore...")
        try:
            health_insights = None
            if hasattr(self.qualia_analysis_module, "get_system_health_summary"):
                health_insights = self.qualia_analysis_module.get_system_health_summary(
                    latest_metrics=decision_context.latest_universe_metrics
                )
            elif (
                hasattr(self.qualia_analysis_module, "process_qast_cycle")
                and decision_context.current_universe_statevector is not None
            ):
                health_insights = self.qualia_analysis_module.process_qast_cycle(
                    statevector_from_universe=decision_context.current_universe_statevector
                )
            if health_insights:
                qmt_logger.debug(
                    f"QMT: Insights de saúde do QualiaAnalysisCore: {list(health_insights.keys())}"
                )
                ctx.internal_system_health = health_insights
            else:
                qmt_logger.warning("QMT: QualiaAnalysisCore não retornou insights.")
        except (RuntimeError, ValueError, AttributeError) as e_qac:
            qmt_logger.error(
                f"QMT: Erro com QualiaAnalysisCore: {e_qac}", exc_info=True
            )
            ctx.error_messages.append(f"QualiaAnalysisCore: {str(e_qac)}")
            ctx.internal_system_health = {"error": str(e_qac)}

    def _compute_adaptive_thresholds(self) -> Tuple[float, float]:
        """Deriva limiares de BUY e SELL a partir de ``quantum_score_window``.

        Os thresholds são calculados com base nos percentis 75 e 25 dos scores
        recentes armazenados em ``self._quantum_score_window``. Um pequeno
        ``sigma`` é adicionado/subtraído para criar margem de segurança.

        Returns
        -------
        Tuple[float, float]
            ``(buy_threshold, sell_threshold)`` adaptados aos scores recentes.
        """

        if len(self._quantum_score_window) < MIN_SCORE_WINDOW:
            return float(self.buy_score_threshold), float(self.sell_score_threshold)

        scores = np.array(self._quantum_score_window)
        buy_thr = float(np.percentile(scores, 75) + self.quantum_score_sigma)
        sell_thr = float(np.percentile(scores, 25) - self.quantum_score_sigma)
        buy_thr = float(np.clip(buy_thr, 0.0, 1.0))
        sell_thr = float(np.clip(sell_thr, 0.0, 1.0))

        return float(buy_thr), float(sell_thr)

    def _generate_trade_signal(
        self,
        decision_context: DecisionContext,
        ctx: MetacognitiveContext,
        qpm_feedback_type: str,
        qpm_confidence: float,
        current_metacognitive_run_id: str,
        qmt_logger,
        pnl_feedback: Optional[float] = None,
    ) -> TradeSignal:
        """Gera o ``TradeSignal`` a partir do contexto atualizado.

        Parameters
        ----------
        decision_context : DecisionContext
            Contexto da decisão de trading avaliada.
        ctx : MetacognitiveContext
            Contexto metacognitivo em construção.
        qpm_feedback_type : str
            Tipo de feedback obtido da QPM.
        qpm_confidence : float
            Confiança associada ao feedback da QPM.
        current_metacognitive_run_id : str
            Identificador do ciclo de metacognição atual.
        qmt_logger : Logger
            Instância de logger para depuração.
        pnl_feedback : float, optional
            Último PnL observado para aplicar regras de exposição.

        Returns
        -------
        TradeSignal
            Objeto representando o sinal gerado.
        """
        if (
            qpm_feedback_type == "alta_confianca_positiva"
            or qpm_feedback_type == "feedback_combinado_positivo"
        ):
            ctx.qast_tuning_directives.exploration_bias_factor = np.clip(
                ctx.qast_tuning_directives.exploration_bias_factor * 0.9, 0.5, 1.5
            )
            ctx.overall_system_confidence_level = min(0.9, qpm_confidence * 0.8 + 0.2)
        elif (
            qpm_feedback_type == "alta_confianca_negativa"
            or qpm_feedback_type == "feedback_combinado_negativo"
        ):
            ctx.qast_tuning_directives.exploration_bias_factor = np.clip(
                ctx.qast_tuning_directives.exploration_bias_factor * 1.2, 0.5, 2.0
            )
            ctx.ace_modulation_parameters.universe_target_complexity = "decrease"
            ctx.overall_system_confidence_level = max(0.1, (1.0 - qpm_confidence) * 0.7)
        else:
            ctx.overall_system_confidence_level = np.clip(
                qpm_confidence * 0.5 + 0.25, 0.1, 0.75
            )

        qmt_logger.debug(
            f"QMT: MetacognitiveContext populado. Confiança: {ctx.overall_system_confidence_level:.2f}"
        )

        quantum_score = ctx.raw_qpm_feedback_details.get("q_score_from_layer", 0.0)
        symbolic_label = ctx.raw_qpm_feedback_details.get("q_label_from_layer", "low")
        final_confidence_for_signal = ctx.overall_system_confidence_level

        self._update_quantum_score_window(float(quantum_score))

        BUY_SCORE_THRESHOLD, SELL_SCORE_THRESHOLD = self._compute_adaptive_thresholds()

        trade_signal_type: Literal[
            "BUY",
            "SELL",
            "HOLD",
            "REDUCE_EXPOSURE",
            "NO_SIGNAL",
        ] = "NO_SIGNAL"
        reasoning = (
            f"QS: {quantum_score:.2f}, Label: {symbolic_label}, SysConf: {final_confidence_for_signal:.2f}, "
            f"QPMType: {qpm_feedback_type}"
        )

        current_symbol = decision_context.trade_details.get("symbol")
        epsilon = float(self.config.get("reduce_exposure_epsilon", 0.0))
        if (
            symbolic_label == "high"
            and quantum_score is not None
            and quantum_score >= BUY_SCORE_THRESHOLD
            and final_confidence_for_signal >= 0.55
        ):
            trade_signal_type = "BUY"
            reasoning += "; Action: BUY due to high score/label."
        elif (
            symbolic_label == "low"
            and quantum_score is not None
            and quantum_score <= SELL_SCORE_THRESHOLD
            and (
                final_confidence_for_signal >= 0.6
                or (pnl_feedback is not None and pnl_feedback < -epsilon)
            )
        ):
            trade_signal_type = "REDUCE_EXPOSURE"
            reasoning += "; Action: REDUCE_EXPOSURE due to low score/label."
        elif symbolic_label == "medium":
            trade_signal_type = "HOLD"
            reasoning += "; Action: HOLD due to medium score/label."
        else:
            trade_signal_type = "NO_SIGNAL"
            reasoning += "; Action: NO_SIGNAL as thresholds not met or ambiguous label."
            comparison: str
            if symbolic_label == "high":
                comparison = f"score {quantum_score:.2f} < BUY_THRESHOLD {BUY_SCORE_THRESHOLD:.2f}"
            elif symbolic_label == "low":
                comparison = f"score {quantum_score:.2f} > SELL_THRESHOLD {SELL_SCORE_THRESHOLD:.2f}"
            else:
                comparison = f"label {symbolic_label} not actionable"

            get_logger(__name__).debug(
                "gate_reason=%s | score %.2f | BUY_THRESHOLD %.2f | SELL_THRESHOLD %.2f | label=%s",
                comparison,
                quantum_score,
                BUY_SCORE_THRESHOLD,
                SELL_SCORE_THRESHOLD,
                symbolic_label,
            )

        final_trade_signal = TradeSignal(
            signal_type=trade_signal_type,
            confidence=final_confidence_for_signal,
            symbol=current_symbol,
            suggested_price=decision_context.trade_details.get("price"),
            origin_metacognitive_context_id=current_metacognitive_run_id,
            reasoning=reasoning,
            timestamp=datetime.now(timezone.utc),
        )

        if trade_signal_type == "NO_SIGNAL":
            logger.info("NO_SIGNAL – reason: %s", reasoning)
            self.blocked_signals.append(
                {
                    "score": float(quantum_score),
                    "label": symbolic_label,
                    "reason": comparison,
                }
            )
            self._log_blocked_signal(final_trade_signal, ctx)

        qmt_logger.info(
            (
                f"QMT: TradeSignal gerado: {final_trade_signal.signal_type} para {final_trade_signal.symbol} "
                f"com confiança {final_trade_signal.confidence:.2f}; quantum_score={quantum_score:.2f}, "
                f"BUY_SCORE_THRESHOLD={BUY_SCORE_THRESHOLD}, SELL_SCORE_THRESHOLD={SELL_SCORE_THRESHOLD}, "
                f"symbolic_label={symbolic_label}"
            )
        )

        health_score = None
        if ctx.internal_system_health is not None:
            health_score = ctx.internal_system_health.get("health_score")
            if not isinstance(health_score, (int, float)):
                health_score = None

        if (
            final_trade_signal.signal_type == "REDUCE_EXPOSURE"
            and health_score is not None
            and health_score <= 0.5
        ):
            ctx.ace_modulation_parameters.override_on_metacognition = True
            ctx.ace_modulation_parameters.qubit_increase = max(
                ctx.ace_modulation_parameters.qubit_increase,
                2,
            )
            qmt_logger.info(
                "QMT: override_on_metacognition ativado para mutação de circuito"
            )
        elif (
            final_trade_signal.signal_type == "NO_SIGNAL"
            and health_score is not None
            and health_score <= 0.5
        ):
            final_trade_signal.signal_type = "REDUCE_EXPOSURE"
            final_trade_signal.reasoning += (
                "; Forced: REDUCE_EXPOSURE due to low system health."
            )
            ctx.ace_modulation_parameters.override_on_metacognition = True
            ctx.ace_modulation_parameters.qubit_increase = max(
                ctx.ace_modulation_parameters.qubit_increase,
                2,
            )
            qmt_logger.info("QMT: REDUCE_EXPOSURE forçado por baixa saúde do sistema")

        ctx.trade_directive = final_trade_signal.signal_type
        return final_trade_signal

    def _summarize_pnl(
        self, similar_patterns: List[Dict[str, Any]]
    ) -> Tuple[float, float]:
        """
        Calcula win rate e PnL médio de uma lista de padrões similares.
        Este é um método auxiliar extraído da lógica anterior de _analyze_qpm_patterns.
        """
        if not similar_patterns:
            return 0.0, 0.0

        outcomes_pnl_percentage = []
        num_valid_for_pnl_calc = 0

        for item_dict in similar_patterns:
            outcome = item_dict.get("outcome")
            if outcome and isinstance(outcome, dict):
                pnl_perc = outcome.get("pnl_percentage")
                if pnl_perc is not None:
                    outcomes_pnl_percentage.append(pnl_perc)
                    num_valid_for_pnl_calc += 1

        if num_valid_for_pnl_calc == 0:
            return 0.0, 0.0

        avg_pnl_perc = float(np.mean(outcomes_pnl_percentage))
        win_rate = float(np.mean([1 if p > 0 else 0 for p in outcomes_pnl_percentage]))
        return win_rate, avg_pnl_perc

    def _update_quantum_score_window(self, score: float) -> None:
        """Atualiza a janela deslizante de quantum scores."""

        if np.isnan(score):
            return

        self._quantum_score_window.append(float(score))
        self._quantum_score_update_counter += 1
        if (
            self.quantum_score_persist_interval > 0
            and self._quantum_score_update_counter % self.quantum_score_persist_interval
            == 0
        ):
            self._persist_quantum_score_history()

    @property
    def quantum_score_window(self) -> List[float]:
        """Return the current quantum score history as a list."""

        return list(self._quantum_score_window)

    def _load_quantum_score_history(self) -> None:
        """Load persisted quantum score history from disk if available."""

        data = load_json_safe(self.quantum_score_history_file.as_posix(), [])
        if isinstance(data, list):
            try:
                self._quantum_score_window.extend(float(x) for x in data)
            except Exception as exc:  # pragma: no cover - defensive
                logger.error(
                    "Falha ao carregar histórico de quantum_score: %s",
                    exc,
                    exc_info=True,
                )

    def _persist_quantum_score_history(self) -> None:
        """Persist current quantum score history to disk."""

        save_json_safe(
            list(self._quantum_score_window),
            self.quantum_score_history_file.as_posix(),
        )

    def _handle_skip_mutation_throttling(
        self, ctx: MetacognitiveContext, qmt_logger
    ) -> None:
        """Controla a propagação de ``skip_circuit_mutation`` ao ACE."""

        qpm_skip = ctx.ace_modulation_parameters.skip_circuit_mutation

        if qpm_skip:
            self._consecutive_skip_cycles += 1
        else:
            self._consecutive_skip_cycles = 0

        if self._throttled_cycles_remaining > 0:
            ctx.ace_modulation_parameters.skip_circuit_mutation = True
            self._throttled_cycles_remaining -= 1
            qmt_logger.info(
                "QMT: ACE mutation throttled due to repeated lack of QPM patterns (%d cycles remaining)",
                self._throttled_cycles_remaining,
            )
        elif (
            self._consecutive_skip_cycles >= self.skip_circuit_mutation_threshold
            and self.skip_circuit_mutation_propagation_cycles > 0
        ):
            self._throttled_cycles_remaining = (
                self.skip_circuit_mutation_propagation_cycles
            )
            ctx.ace_modulation_parameters.skip_circuit_mutation = True
            qmt_logger.info(
                "QMT: ACE mutation throttled for %d cycles due to repeated lack of QPM patterns",
                self._throttled_cycles_remaining,
            )

        if (
            ctx.ace_modulation_parameters.skip_circuit_mutation
            and self._consecutive_skip_cycles >= self.skip_mutation_reenable_cycles
        ):
            ctx.ace_modulation_parameters.skip_circuit_mutation = False
            self._consecutive_skip_cycles = 0
            qmt_logger.info(
                "QMT: mutation re-enabled after %d stagnant cycles",
                self.skip_mutation_reenable_cycles,
            )

    def _log_blocked_signal(
        self, trade_signal: TradeSignal, ctx: MetacognitiveContext
    ) -> None:
        """Persist blocked signals for offline replay."""

        if not self.blocked_signal_log_path:
            return

        record = {
            "signal": asdict(trade_signal),
            "context": asdict(ctx),
        }
        try:
            path = Path(self.blocked_signal_log_path)
            path.parent.mkdir(parents=True, exist_ok=True)
            with open(path, "a", encoding="utf-8") as fh:
                fh.write(json.dumps(record, default=str) + "\n")
            self.blocked_signals.append(record)
        except Exception as exc:  # pragma: no cover - unexpected I/O errors
            logger.error(
                "QMT: falha ao registrar blocked signal: %s", exc, exc_info=True
            )

    def _apply_cooldown_logic(
        self, ctx: MetacognitiveContext, current_signal: TradeSignal
    ) -> None:
        """Aplica lógica de cooldown baseada em diretivas recentes.

        Se sinais consecutivos forem considerados "estáveis" (``HOLD`` ou
        ``NO_SIGNAL``), aumenta gradualmente ``overall_system_confidence_level``
        e reseta ``ace_modulation_parameters`` após um limiar de ciclos.

        Parameters
        ----------
        ctx : MetacognitiveContext
            Contexto em atualização.
        current_signal : TradeSignal
            Sinal de trading do ciclo atual.
        """

        stable_set = {"HOLD", "NO_SIGNAL"}
        last_directive = None
        if self.last_trade_signal is not None:
            last_directive = self.last_trade_signal.signal_type

        if last_directive in stable_set and current_signal.signal_type in stable_set:
            self._stable_cycle_count += 1
        else:
            self._stable_cycle_count = 0

        if self._stable_cycle_count >= self.cooldown_threshold:
            ctx.overall_system_confidence_level = min(
                1.0,
                ctx.overall_system_confidence_level + self.confidence_cooldown_step,
            )
            preserved_skip = (
                ctx.ace_modulation_parameters.skip_circuit_mutation
                if self._throttled_cycles_remaining > 0
                or ctx.ace_modulation_parameters.skip_circuit_mutation
                else False
            )
            ctx.ace_modulation_parameters = ACEModulationParameters(
                skip_circuit_mutation=preserved_skip
            )
            self._stable_cycle_count = 0
            logger.info("QMT: cooldown ativado; confiança aumentada e ACE resetado")

    def _analyze_qpm_patterns(
        self, current_packet: QuantumSignaturePacket
    ) -> Dict[str, Any]:
        """
        Analisa o pacote quântico atual usando QuantumMetacognitionLayer e
        consulta a QPM para padrões históricos, combinando os insights.
        """
        analysis_result = {
            "type": "sem_historico_relevante",  # Default type
            "confidence": 0.0,
            "details": {},
            "relevance": 0.0,
        }

        # 1) Avaliação quântica de baixo nível via QuantumMetacognitionLayer
        # Adicionada verificação se meta_layer foi inicializada
        if not hasattr(self, "meta_layer") or not self.meta_layer:
            logger.error(
                "QuantumMetacognitionLayer (self.meta_layer) não inicializada. Não é possível avaliar o padrão."
            )
            analysis_result["type"] = "erro_meta_layer_nao_init"
            analysis_result["details"]["error"] = "MetaLayer not initialized"
            return analysis_result

        eval_out = self.meta_layer.evaluate_pattern(current_packet)
        q_score = eval_out.get("quantum_score", 0.0)  # Use .get com default
        q_label = eval_out.get("symbolic_label", "low")  # Use .get com default

        analysis_result["details"]["q_score_from_layer"] = q_score
        analysis_result["details"]["q_label_from_layer"] = q_label

        # 2) Consulta aos padrões semelhantes na QPM (lógica anterior adaptada)
        top_n = self.config.get("qpm_retrieval_top_n", 10)
        # Assumindo que current_packet é QuantumSignaturePacket e retrieve_similar_patterns aceita isso.
        similar_pkts = self.qpm_memory.retrieve_similar_patterns(
            current_signature_packet=current_packet,
            top_n=top_n,
            adaptive=True,
        )

        # Expor os padrões similares para lógica externa caso necessário
        analysis_result["similar_patterns"] = similar_pkts

        logger.debug(
            f"QMT_QPM_Analysis: Analisando {len(similar_pkts)} padrões da QPM."
        )
        analysis_result["details"]["num_similar_patterns_found_qpm"] = len(similar_pkts)

        # 3) Métricas de PnL agregadas dos padrões históricos da QPM
        win_rate, avg_pnl = self._summarize_pnl(similar_pkts)
        analysis_result["details"]["historical_win_rate"] = win_rate
        analysis_result["details"]["historical_avg_pnl"] = avg_pnl

        num_valid_for_pnl_calc = 0  # Re-calcular para lógica abaixo
        if similar_pkts:
            outcomes_pnl_percentage_temp = []
            for item_dict in similar_pkts:
                outcome = item_dict.get("outcome")
                if outcome and isinstance(outcome, dict):
                    pnl_perc = outcome.get("pnl_percentage")
                    if pnl_perc is not None:
                        outcomes_pnl_percentage_temp.append(pnl_perc)
                        num_valid_for_pnl_calc += 1
            analysis_result["details"][
                "num_patterns_with_pnl_perc_qpm"
            ] = num_valid_for_pnl_calc

        # 4) Combinar tudo para decidir feedback type e confidence
        #    (exemplo simples – ajuste depois com regressão)
        # A lógica original de _analyze_qpm_patterns para determinar 'type' e 'confidence'
        # era baseada apenas nos `similar_patterns`. Vamos tentar integrar o q_score.

        min_relevant_patterns = self.config.get("min_relevant_patterns_for_feedback", 3)

        if num_valid_for_pnl_calc >= min_relevant_patterns:
            # Usar a lógica de tipo e confiança baseada em PnL histórico
            # e depois modular com q_score.
            confidence_threshold_win_rate_high = self.config.get(
                "qpm_feedback_wr_high", 0.70
            )
            confidence_threshold_win_rate_low = self.config.get(
                "qpm_feedback_wr_low", 0.30
            )
            avg_pnl_positive_threshold = self.config.get(
                "qpm_feedback_pnl_pos_thresh", 0.0001
            )
            avg_pnl_negative_threshold = self.config.get(
                "qpm_feedback_pnl_neg_thresh", -0.0001
            )

            # Confiança e tipo baseados no histórico
            historical_type = "baixa_confianca_mista"

            if (
                win_rate >= confidence_threshold_win_rate_high
                and avg_pnl > avg_pnl_positive_threshold
            ):
                historical_type = "alta_confianca_positiva"
            elif (
                win_rate <= confidence_threshold_win_rate_low
                and avg_pnl < avg_pnl_negative_threshold
            ):
                historical_type = "alta_confianca_negativa"
            else:
                historical_type = "baixa_confianca_mista"

            analysis_result["type"] = historical_type
            # Combinar ``q_score`` com ``win_rate`` como uma aproximação de
            # confiança histórica. Essa média ponderada funciona como uma
            # heurística simples quando o histórico ainda é limitado.

            combined_confidence = (
                0.5 * q_score + 0.5 * win_rate
            )  # Usando win_rate diretamente como no snippet
            analysis_result["confidence"] = np.clip(combined_confidence, 0.0, 1.0)

            # Atualizar o 'type' com base na 'combined_confidence' se desejado, ou manter o 'historical_type'.
            # O snippet do usuário recalcula o 'feedback_type' com base na 'confidence' combinada.
            # Vamos seguir essa sugestão:
            if analysis_result["confidence"] >= self.config.get(
                "combined_conf_positive_thresh", 0.7
            ):
                analysis_result["type"] = "feedback_combinado_positivo"
            elif analysis_result["confidence"] <= self.config.get(
                "combined_conf_negative_thresh", 0.3
            ):
                analysis_result["type"] = "feedback_combinado_negativo"
            else:
                analysis_result["type"] = "feedback_combinado_neutro"

            relevance_factor_count = min(
                num_valid_for_pnl_calc,
                self.config.get("qpm_relevance_max_count_cap", 10),
            ) / self.config.get("qpm_relevance_max_count_cap", 10)
            # A relevância pode agora também considerar a força do q_score
            analysis_result["relevance"] = (
                analysis_result["confidence"]
                * relevance_factor_count
                * (0.5 + 0.5 * q_score)  # Ponderando com q_score
            )
            analysis_result["relevance"] = np.clip(
                analysis_result["relevance"], 0.0, 1.0
            )

        elif num_valid_for_pnl_calc > 0:  # Histórico insuficiente mas algum PnL
            analysis_result["type"] = "historico_insuficiente_q_score_dominante"
            analysis_result["confidence"] = (
                q_score  # Confiança baseada principalmente no q_score
            )
            avg_pnl_info = float(
                np.mean(
                    [
                        p.get("outcome", {}).get("pnl_percentage", 0)
                        for p in similar_pkts
                        if p.get("outcome", {}).get("pnl_percentage") is not None
                    ]
                )
            )
            analysis_result["details"]["avg_pnl_perc_informative"] = avg_pnl_info
            analysis_result["relevance"] = q_score * 0.5  # Relevância moderada
        else:  # Sem histórico de PnL relevante
            analysis_result["type"] = "sem_historico_q_score_unico"
            analysis_result["confidence"] = q_score
            analysis_result["relevance"] = (
                q_score * 0.8
            )  # Relevância mais alta baseada apenas no q_score
            logger.debug(
                "QMT_QPM_Analysis: Nenhum padrão similar com PnL % utilizável encontrado. Usando q_score."
            )

        analysis_result["details"][
            "final_q_label_used"
        ] = q_label  # Guardar o label da camada inferior

        logger.debug(
            (
                "QMT_QPM_Analysis Result: "
                f"Type='{analysis_result['type']}', "
                f"Conf={analysis_result['confidence']:.2f}, "
                f"Relevance={analysis_result['relevance']:.2f}, "
                f"QScoreIn={q_score:.2f}"
            )
        )
        return analysis_result

    def analyze_decision_history(
        self, source_type: str = "internal", source_location: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Analisa o histórico de decisões para extrair PnL e outras métricas.
        Implementação inicial para ler PnL do histórico interno.
        """
        logger.info(
            (
                "Metacognição: Iniciando análise do histórico de decisões. "
                f"Fonte: {source_type}, {len(self.decision_log)} registros."
            )
        )

        processed_history = []

        if source_type == "internal":
            for record in self.decision_log:
                analysis_entry = {
                    "trade_id": record.get("decision_details", {}).get(
                        "order_id", f"sim_{record.get('timestamp')}"
                    ),
                    "symbol": record.get("decision_details", {}).get("symbol"),
                    "action": record.get("decision_details", {}).get("action"),
                    "entry_price": record.get("decision_details", {}).get("price"),
                    "pnl": record.get("pnl"),
                    "timestamp": record.get("timestamp"),
                    "quantum_metrics": record.get("quantum_metrics", {}),
                    "market_context": record.get("decision_context", {}),
                }
                processed_history.append(analysis_entry)
            logger.info(
                f"Metacognição: Análise do histórico interno concluída. {len(processed_history)} decisões processadas."
            )

        elif source_type == "broker_api":
            logger.warning(
                "Metacognição: Leitura de PnL da API da corretora ainda não implementada."
            )
            # Placeholder: historical_trades = self._fetch_from_broker(source_location)
            # Processar historical_trades e popular processed_history
        elif source_type == "backtest_log":
            logger.warning(
                "Metacognição: Leitura de PnL de logs de backtest ainda não implementada."
            )
            # Placeholder: results = self._parse_backtest_log(source_location)
            # Processar results e popular processed_history
        else:
            logger.error(
                f"Metacognição: Tipo de fonte desconhecido para análise de histórico: {source_type}"
            )

        return processed_history

    def analyze_context_and_suggest_adjustments(self, historical_outcomes):
        """Analisa resultados históricos e sugere ajustes simples.

        Parameters
        ----------
        historical_outcomes : list
            Lista de valores ou dicionários contendo PnL. Aceita formatos
            variados, por exemplo ``{"pnl": 0.1}`` ou ``{"outcome": {"pnl": 0.1}}``.

        Returns
        -------
        Dict[str, Any]
            Mapeamento com parâmetros sugeridos para o ``QUALIAQuantumUniverse``.
        """

        if not historical_outcomes:
            return {"universe_params": {}}

        pnls = []
        for entry in historical_outcomes:
            if isinstance(entry, (int, float)):
                pnls.append(float(entry))
            elif isinstance(entry, dict):
                if entry.get("pnl") is not None:
                    pnls.append(float(entry["pnl"]))
                elif entry.get("pnl_percentage") is not None:
                    pnls.append(float(entry["pnl_percentage"]))
                elif isinstance(entry.get("outcome"), dict):
                    outcome = entry["outcome"]
                    if outcome.get("pnl") is not None:
                        pnls.append(float(outcome["pnl"]))
                    elif outcome.get("pnl_percentage") is not None:
                        pnls.append(float(outcome["pnl_percentage"]))

        if not pnls:
            return {"universe_params": {}}

        avg_pnl = float(np.mean(pnls))
        suggested = {}

        current_depth = getattr(self.universe, "scr_depth", None)
        if current_depth is not None:
            if avg_pnl > 0.0:
                suggested["scr_depth"] = current_depth + 1
            elif avg_pnl < 0.0:
                suggested["scr_depth"] = max(1, current_depth - 1)

        logger.info(
            "Metacognição: média de PnL %.4f, sugestão de ajustes: %s",
            avg_pnl,
            suggested,
        )

        return {"universe_params": suggested, "avg_pnl": avg_pnl}

    def perform_metacognitive_analysis(self) -> List[Dict[str, Any]]:
        """Gera insights simples a partir do ``decision_log`` atual."""

        pnls = [
            d.get("pnl_feedback")
            for d in self.decision_log
            if d.get("pnl_feedback") is not None
        ]
        if len(pnls) < self.min_history_length:
            # ESTADO CRÍTICO: Sem histórico, o sistema não pode aprender.
            # Em vez de falhar ou retornar um valor ambíguo, definimos um estado
            # explícito de bootstrap com confiança mínima.
            logger.warning(
                "Metacognição: Histórico insuficiente para análise (atual=%d, mínimo=%d). "
                "Entrando em estado de bootstrap com confiança mínima.",
                len(pnls),
                self.min_history_length,
            )
            self.low_confidence_alert = True

            # O insight gerado é claro e acionável: alerta de bootstrap, confiança zero, sem ajustes.
            # Os sistemas consumidores podem usar o 'type' para identificar esta condição.
            bootstrap_insight = {
                "type": "BOOTSTRAP_NO_HISTORY",
                "confidence": 0.0,  # Confiança zero explícita
                "suggested_adjustments": [],  # Nenhum ajuste deve ser feito
                "reason": "Histórico de PnL insuficiente para iniciar a análise metacognitiva.",
            }
            self.metacognitive_insights = [bootstrap_insight]
            return self.metacognitive_insights

        suggestions = self.analyze_context_and_suggest_adjustments(pnls)
        avg_pnl = suggestions.get("avg_pnl", 0.0)
        confidence = float(np.clip(abs(avg_pnl) * 10.0, 0.0, 1.0))

        insight = {
            "type": "pnl_trend",
            "avg_pnl": avg_pnl,
            "confidence": confidence,
            "suggested_adjustments": [
                {"parameter": k, "value": v}
                for k, v in suggestions.get("universe_params", {}).items()
            ],
        }

        self.metacognitive_insights = [insight]
        return self.metacognitive_insights

    def apply_metacognitive_feedback(
        self, confidence_threshold: Optional[float] = None
    ) -> List[Dict[str, Any]]:
        """Aplica ajustes simples no ``universe`` baseados em insights."""

        threshold = (
            confidence_threshold
            if confidence_threshold is not None
            else self.min_confidence
        )

        if not self.metacognitive_insights:
            self.perform_metacognitive_analysis()

        applied: List[Dict[str, Any]] = []
        for insight in self.metacognitive_insights:
            if insight.get("confidence", 0.0) < threshold:
                continue
            for adj in insight.get("suggested_adjustments", []):
                param = adj.get("parameter")
                value = adj.get("value")
                if param and hasattr(self.universe, param):
                    previous = getattr(self.universe, param)
                    setattr(self.universe, param, value)
                    record = {
                        "parameter": param,
                        "previous_value": previous,
                        "value": value,
                        "confidence": insight.get("confidence", 0.0),
                    }
                    applied.append(record)
                    self.adjustment_history.append(record)

        logger.info("Metacognição: %d ajustes aplicados", len(applied))
        return applied

    def save_report(self, filepath: str) -> str:
        """Persist metacognitive logs and blocked signals as JSON.

        Parameters
        ----------
        filepath : str
            Destination file path for the report.

        Returns
        -------
        str
            The path where the report was saved.
        """

        report_data = {
            "decision_log": self.decision_log,
            "adjustment_history": self.adjustment_history,
            "Signals blocked & reason": self.blocked_signals,
            "pnl_history": self.pnl_history,
        }
        path = Path(filepath)
        path.parent.mkdir(parents=True, exist_ok=True)
        with open(path, "w", encoding="utf-8") as fh:
            json.dump(report_data, fh, indent=2, default=str)
        return str(path)

    def load_history(self, filepath: str) -> bool:
        """Load metacognitive history from a JSON file.

        Parameters
        ----------
        filepath : str
            Path to the history file previously generated by
            :meth:`save_report`.

        Returns
        -------
        bool
            ``True`` if the file was loaded successfully.
        """

        path = Path(filepath)
        if not path.exists():
            logger.info("Arquivo de histórico %s não encontrado", path.as_posix())
            return False

        try:
            with open(path, "r", encoding="utf-8") as fh:
                data = json.load(fh)
            self.decision_log = data.get("decision_log", [])
            self.adjustment_history = data.get("adjustment_history", [])
            self.pnl_history = data.get("pnl_history", [])
            logger.info(
                "Histórico de metacognição carregado de %s (%s registros)",
                path.as_posix(),
                len(self.decision_log),
            )
            return True
        except (OSError, json.JSONDecodeError) as exc:  # pragma: no cover
            logger.error(
                "Erro ao carregar histórico de metacognição de %s: %s",
                path.as_posix(),
                exc,
                exc_info=True,
            )
            return False

    # ------------------------------------------------------------------
    def _on_freeze(self, _payload: CoherenceGuardFreeze) -> None:
        self.frozen = True
        logger.info("QUALIAMetacognitionTrading congelada via CoherenceGuard")

    def _on_unfreeze(self, _payload: CoherenceGuardUnfreeze) -> None:
        self.frozen = False
        logger.info("QUALIAMetacognitionTrading liberada via CoherenceGuard")

    # Métodos avançados (perform_metacognitive_analysis, apply_metacognitive_feedback, etc.)
    # podem ser adicionados posteriormente. No momento o foco é manter __init__ e
    # as funções principais operacionais.

    def run_cycle(self) -> List[Dict[str, Any]]:
        """Execute um ciclo completo de metacognição.

        O ciclo consiste em analisar o ``decision_log`` e aplicar os
        ajustes sugeridos no universo holográfico, retornando os insights
        gerados.

        Returns
        -------
        list of dict
            Lista de insights produzidos durante o ciclo.
        """

        if self.frozen:
            logger.debug("Metacognição congelada; pulando ciclo")
            return []

        insights = self.perform_metacognitive_analysis()
        try:
            self.apply_metacognitive_feedback()
        except Exception as exc:  # pragma: no cover - feedback errors
            logger.error(
                "Erro ao aplicar feedback metacognitivo: %s", exc, exc_info=True
            )
        return insights
