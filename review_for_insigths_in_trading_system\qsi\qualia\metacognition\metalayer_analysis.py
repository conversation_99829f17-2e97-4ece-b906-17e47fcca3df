from dataclasses import dataclass
from typing import List, Optional

from ..utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class MetalayerAnalysis:
    """Realiza análises de metalayer no sistema QUALIA."""

    universe: object

    def analyze_otoc(
        self, time_step: float = 1.0, target_qubits: Optional[List[int]] = None
    ) -> Optional[float]:
        """
        Analisa o OTOC (Out-of-Time-Ordered Correlator) do sistema.
        Utiliza a interface correta de self.universe.calculate_otoc.

        Args:
            time_step (float): O passo de tempo para a evolução do operador no cálculo OTOC.
            target_qubits (Optional[List[int]]): Lista de qubits alvo para os operadores OTOC.
                                               Se None, o padrão de calculate_otoc será usado (geralmente [0]).

        Returns:
            Optional[float]: Valor do OTOC calculado ou ``None`` se não for possível calcular.
        """
        if not hasattr(self.universe, "calculate_otoc"):
            logger.warning("O universo não possui o método calculate_otoc")
            return None

        if not hasattr(self.universe, "get_current_statevector"):
            logger.warning(
                "O universo não possui o método get_current_statevector, não é possível calcular OTOC."
            )
            return None

        if not hasattr(self.universe, "n_qubits") or self.universe.n_qubits is None:
            logger.warning(
                "O universo não possui n_qubits definido, não é possível calcular OTOC."
            )
            return None

        current_statevector = self.universe.get_current_statevector()
        if current_statevector is None:
            logger.warning(
                "Não foi possível obter o statevector atual do universo para OTOC."
            )
            return None

        num_qubits = self.universe.n_qubits

        try:
            otoc_value = self.universe.calculate_otoc(
                statevector=current_statevector,
                time_step=time_step,
                n_qubits=num_qubits,
                target_qubits=target_qubits,
            )
            if otoc_value is not None:
                logger.info(
                    f"OTOC calculado: {otoc_value:.4f} (time_step={time_step}, target_qubits={target_qubits})"
                )
            else:
                logger.warning(
                    f"Cálculo de OTOC retornou None (time_step={time_step}, target_qubits={target_qubits})"
                )
            return otoc_value

        except Exception as e:
            logger.error(f"Erro ao chamar calculate_otoc: {e}", exc_info=True)
            return None
