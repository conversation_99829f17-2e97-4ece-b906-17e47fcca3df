"""
QUALIA: Quantum Universal Awareness Lattice Interface Architecture
Sistema de Consciência de Risco (Risk Awareness)

Este módulo implementa a metacognição sobre eficácia do controle de risco dinâmico,
permitindo ao QUALIA desenvolver consciência sobre sua própria performance de
gerenciamento de risco e auto-otimizar seus parâmetros.

"A verdadeira consciência emerge quando o sistema percebe não apenas o que faz,
mas quão bem o faz - e ajusta sua própria natureza baseado nessa percepção."
"""

from ..utils.logger import get_logger
from ..memory.event_bus import SimpleEventBus
from ..events import RiskSnapshotRecorded, RiskAwarenessUpdated
from ..utils.tracing import get_tracer
import numpy as np
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime, timedelta, timezone
from dataclasses import dataclass, field
from collections import defaultdict, deque
import asyncio

# The functions in this module use dynamic calculations and optional typing.
# Until full typing is implemented, suppress mypy errors for this file.
# mypy: ignore-errors

logger = get_logger(__name__)


@dataclass
class RiskAwarenessMetrics:
    """Métricas de consciência de risco."""

    # Métricas de Eficácia
    drawdown_reduction_score: float = 0.0  # 0-1, maior = melhor
    profit_factor_improvement: float = 0.0  # Melhoria no profit factor
    win_rate_impact: float = 0.0  # Impacto na taxa de acerto
    average_risk_reward: float = 0.0  # Risk/Reward médio

    # Métricas de Adaptabilidade
    regime_detection_accuracy: float = 0.0  # Precisão na detecção de regimes
    volatility_prediction_score: float = 0.0  # Precisão na previsão de volatilidade
    adaptation_speed: float = 0.0  # Velocidade de adaptação a mudanças

    # Métricas de Estabilidade
    calibration_consistency: float = 0.0  # Consistência das calibrações
    parameter_stability: float = 0.0  # Estabilidade dos parâmetros
    system_reliability: float = 0.0  # Confiabilidade geral

    # Metacognição
    self_assessment_accuracy: float = 0.0  # Precisão da auto-avaliação
    learning_rate: float = 0.0  # Taxa de aprendizado
    confidence_calibration: float = 0.0  # Calibração da confiança

    # Score Geral
    overall_risk_awareness: float = 0.0  # Score geral (0-1)

    # Metadados
    timestamp: datetime = field(default_factory=datetime.now)
    observation_period: timedelta = field(default_factory=lambda: timedelta(hours=24))
    sample_size: int = 0


@dataclass
class PerformanceSnapshot:
    """Snapshot de performance para análise temporal."""

    timestamp: datetime
    symbol: str

    # Dados da calibração
    predicted_volatility: float
    actual_volatility: float
    predicted_regime: str
    actual_regime: str
    stop_distance: float
    take_profit_distance: float

    # Resultados da operação
    trade_result: Optional[str] = None  # 'win', 'loss', 'pending'
    pnl: Optional[float] = None
    max_drawdown: Optional[float] = None
    time_in_trade: Optional[timedelta] = None
    exit_reason: Optional[str] = None  # 'stop', 'target', 'timeout', 'manual'

    # Métricas calculadas
    volatility_prediction_error: float = field(init=False)
    regime_prediction_correct: bool = field(init=False)
    risk_reward_realized: Optional[float] = None

    def __post_init__(self):
        self.volatility_prediction_error = abs(
            self.predicted_volatility - self.actual_volatility
        )
        self.regime_prediction_correct = self.predicted_regime == self.actual_regime


class RiskAwarenessEngine:
    """
    Engine principal de consciência de risco.

    Monitora, analisa e aprende com a performance das calibrações de risco,
    desenvolvendo consciência sobre a eficácia do sistema.
    """

    def __init__(
        self, config: Dict[str, Any], event_bus: Optional[SimpleEventBus] = None
    ):
        self.config = config
        self.event_bus = event_bus

        # Configurações
        self.observation_window = config.get("observation_window_hours", 24)
        self.min_samples_for_assessment = config.get("min_samples", 50)
        self.learning_rate = config.get("learning_rate", 0.1)
        self.confidence_threshold = config.get("confidence_threshold", 0.7)
        self.baseline_drawdown = config.get("baseline_drawdown", 0.05)
        self.baseline_profit_factor = config.get("baseline_profit_factor", 1.2)

        # Histórico de snapshots
        self.performance_history: deque = deque(maxlen=10000)
        self.metrics_history: List[RiskAwarenessMetrics] = []

        # Estado atual
        self.current_metrics: Optional[RiskAwarenessMetrics] = None
        self.last_assessment: Optional[datetime] = None

        # Detecção de padrões
        self.pattern_detector = RiskPatternDetector()

        # Auto-otimização
        self.parameter_optimizer = RiskParameterOptimizer(config)

        # Tasks assíncronas ativas iniciadas pelo engine
        self._tasks: List[asyncio.Task] = []
        self._current_task: Optional[asyncio.Task] = None

        if self.event_bus is not None:
            self.event_bus.subscribe("risk.snapshot", self.record_performance_snapshot)

        logger.info("RiskAwarenessEngine inicializado")

    def record_performance_snapshot(self, snapshot: PerformanceSnapshot) -> None:
        """
        Registra um snapshot de performance.

        Args:
            snapshot: Dados de performance da calibração
        """
        tracer = get_tracer(__name__)
        with tracer.start_as_current_span(
            "risk.record_snapshot", attributes={"symbol": snapshot.symbol}
        ):
            try:
                if snapshot.timestamp.tzinfo is None:
                    snapshot.timestamp = snapshot.timestamp.replace(tzinfo=timezone.utc)

                self.performance_history.append(snapshot)
                if self.event_bus:
                    self.event_bus.publish(
                        "risk.snapshot_recorded",
                        RiskSnapshotRecorded(
                            symbol=snapshot.symbol,
                            timestamp=snapshot.timestamp.isoformat(),
                        ),
                    )

                # Processar snapshot imediatamente para detecção de padrões
                self.pattern_detector.process_snapshot(snapshot)

                # Verificar se é hora de atualizar métricas
                if self._should_update_assessment():
                    if self._current_task and not self._current_task.done():
                        logger.debug("Risk awareness update already running")
                    else:
                        self._current_task = asyncio.create_task(
                            self._update_risk_awareness()
                        )
                        self._current_task.add_done_callback(self._task_done_callback)
                        self._tasks.append(self._current_task)
            except (ValueError, RuntimeError) as e:
                logger.error("Error recording snapshot: %s", e)

    def get_current_risk_awareness(self) -> Optional[RiskAwarenessMetrics]:
        """Retorna as métricas atuais de consciência de risco."""
        return self.current_metrics

    def get_risk_consciousness_level(self) -> float:
        """
        Retorna o nível atual de consciência de risco (0-1).

        Returns:
            Float entre 0 (sem consciência) e 1 (consciência máxima)
        """
        if not self.current_metrics:
            return 0.0

        # Ponderar diferentes aspectos da consciência
        weights = {
            "overall_risk_awareness": 0.3,
            "self_assessment_accuracy": 0.25,
            "learning_rate": 0.2,
            "adaptation_speed": 0.15,
            "system_reliability": 0.1,
        }

        consciousness = 0.0
        for metric, weight in weights.items():
            value = getattr(self.current_metrics, metric, 0.0)
            consciousness += value * weight

        return min(1.0, max(0.0, consciousness))

    async def _update_risk_awareness(self):
        """Atualiza as métricas de consciência de risco."""
        tracer = get_tracer(__name__)
        try:
            if len(self.performance_history) < self.min_samples_for_assessment:
                logger.debug("Amostras insuficientes para avaliação de consciência")
                return

            # Calcular janela de observação
            now = datetime.now(timezone.utc)
            cutoff = now - timedelta(hours=self.observation_window)

            # Filtrar snapshots relevantes
            recent_snapshots = [
                s
                for s in self.performance_history
                if s.timestamp >= cutoff and s.trade_result is not None
            ]

            if len(recent_snapshots) < self.min_samples_for_assessment:
                logger.debug("Amostras recentes insuficientes")
                return

            # Calcular métricas
            with tracer.start_as_current_span("risk.calculate_metrics"):
                metrics = await self._calculate_awareness_metrics(recent_snapshots)

            # Armazenar métricas
            self.current_metrics = metrics
            self.metrics_history.append(metrics)
            self.last_assessment = now

            # Detectar mudanças significativas
            await self._detect_consciousness_changes()

            # Trigger auto-otimização se necessário
            if self._should_trigger_optimization():
                await self.parameter_optimizer.optimize_parameters(
                    recent_snapshots, metrics
                )

            logger.info(
                "Consciência de risco atualizada: %.3f",
                metrics.overall_risk_awareness,
            )
            if self.event_bus:
                self.event_bus.publish(
                    "risk.awareness.updated",
                    RiskAwarenessUpdated(metrics=metrics.__dict__),
                )

        except (ValueError, RuntimeError) as e:
            logger.error("Error updating risk awareness: %s", e)

    async def _calculate_awareness_metrics(
        self, snapshots: List[PerformanceSnapshot]
    ) -> RiskAwarenessMetrics:
        """Calcula métricas de consciência baseadas nos snapshots."""

        # Métricas de Eficácia
        drawdown_reduction = self._calculate_drawdown_reduction(snapshots)
        profit_factor = self._calculate_profit_factor_improvement(snapshots)
        win_rate = self._calculate_win_rate_impact(snapshots)
        risk_reward = self._calculate_average_risk_reward(snapshots)

        # Métricas de Adaptabilidade
        regime_accuracy = self._calculate_regime_detection_accuracy(snapshots)
        vol_prediction = self._calculate_volatility_prediction_score(snapshots)
        adaptation_speed = self._calculate_adaptation_speed(snapshots)

        # Métricas de Estabilidade
        calibration_consistency = self._calculate_calibration_consistency(snapshots)
        parameter_stability = self._calculate_parameter_stability(snapshots)
        system_reliability = self._calculate_system_reliability(snapshots)

        # Metacognição
        self_assessment = self._calculate_self_assessment_accuracy(snapshots)
        learning_rate = self._calculate_learning_rate()
        confidence_cal = self._calculate_confidence_calibration(snapshots)

        # Score geral (média ponderada)
        overall_score = (
            drawdown_reduction * 0.20
            + profit_factor * 0.15
            + regime_accuracy * 0.15
            + vol_prediction * 0.15
            + calibration_consistency * 0.10
            + self_assessment * 0.10
            + learning_rate * 0.10
            + system_reliability * 0.05
        )

        return RiskAwarenessMetrics(
            drawdown_reduction_score=drawdown_reduction,
            profit_factor_improvement=profit_factor,
            win_rate_impact=win_rate,
            average_risk_reward=risk_reward,
            regime_detection_accuracy=regime_accuracy,
            volatility_prediction_score=vol_prediction,
            adaptation_speed=adaptation_speed,
            calibration_consistency=calibration_consistency,
            parameter_stability=parameter_stability,
            system_reliability=system_reliability,
            self_assessment_accuracy=self_assessment,
            learning_rate=learning_rate,
            confidence_calibration=confidence_cal,
            overall_risk_awareness=overall_score,
            timestamp=datetime.now(timezone.utc),
            observation_period=timedelta(hours=self.observation_window),
            sample_size=len(snapshots),
        )

    def _calculate_drawdown_reduction(
        self, snapshots: List[PerformanceSnapshot]
    ) -> float:
        """Calcula redução de drawdown comparado a baseline."""
        try:
            # Calcular drawdown médio
            drawdowns = [
                s.max_drawdown for s in snapshots if s.max_drawdown is not None
            ]

            if not drawdowns:
                return 0.5  # Neutro se não há dados

            avg_drawdown = np.mean(drawdowns)

            # Baseline (histórico ou teórico)
            baseline_drawdown = self._get_baseline_drawdown()

            if baseline_drawdown <= 0:
                return 0.5

            # Score: 1.0 = 100% redução, 0.0 = sem redução, 0.5 = neutro
            reduction = max(0, (baseline_drawdown - avg_drawdown) / baseline_drawdown)
            return min(1.0, reduction)

        except (ValueError, ZeroDivisionError) as e:
            logger.error("Error calculating drawdown reduction: %s", e)
            return 0.5

    def _calculate_profit_factor_improvement(
        self, snapshots: List[PerformanceSnapshot]
    ) -> float:
        """Calcula melhoria no profit factor."""
        try:
            wins = [s.pnl for s in snapshots if s.pnl is not None and s.pnl > 0]
            losses = [abs(s.pnl) for s in snapshots if s.pnl is not None and s.pnl < 0]

            if not wins or not losses:
                return 0.5

            current_pf = sum(wins) / sum(losses) if sum(losses) > 0 else float("inf")
            baseline_pf = self._get_baseline_profit_factor()

            if baseline_pf <= 0:
                return 0.5

            # Normalizar melhoria
            improvement = (current_pf - baseline_pf) / baseline_pf
            return min(1.0, max(0.0, (improvement + 1) / 2))  # Mapear para 0-1

        except (ValueError, ZeroDivisionError) as e:
            logger.error("Error calculating profit factor improvement: %s", e)
            return 0.5

    def _calculate_regime_detection_accuracy(
        self, snapshots: List[PerformanceSnapshot]
    ) -> float:
        """Calculate accuracy in regime detection."""
        try:
            correct_predictions = sum(
                1 for s in snapshots if s.regime_prediction_correct
            )
            total_predictions = len(snapshots)

            if total_predictions == 0:
                return 0.5

            return correct_predictions / total_predictions

        except (ValueError, ZeroDivisionError) as e:
            logger.error("Error calculating regime detection accuracy: %s", e)
            return 0.5

    def _calculate_volatility_prediction_score(
        self, snapshots: List[PerformanceSnapshot]
    ) -> float:
        """Calculate volatility prediction score."""
        try:
            pairs = [
                (s.volatility_prediction_error, s.actual_volatility)
                for s in snapshots
                if s.actual_volatility > 0
            ]

            if not pairs:
                return 0.5

            mape = np.mean([err / actual for err, actual in pairs])

            # Converter MAPE para score (1.0 = erro 0%, 0.0 = erro 100%+)
            score = max(0.0, 1.0 - mape)
            return min(1.0, score)

        except (ValueError, ZeroDivisionError) as e:
            logger.error("Error calculating volatility score: %s", e)
            return 0.5

    def _calculate_adaptation_speed(
        self, snapshots: List[PerformanceSnapshot]
    ) -> float:
        """Calculate adaptation speed to changes."""
        try:
            # Detectar pontos de mudança de regime
            regime_changes = []
            prev_regime = None

            for snapshot in sorted(snapshots, key=lambda x: x.timestamp):
                if prev_regime and prev_regime != snapshot.actual_regime:
                    regime_changes.append(snapshot.timestamp)
                prev_regime = snapshot.actual_regime

            if len(regime_changes) < 2:
                return 0.5  # Not enough changes to evaluate

            # Compute mean adaptation time (simplified)
            adaptation_times = []
            for change_time in regime_changes:
                # Find snapshots after the regime change
                post_change = [
                    s
                    for s in snapshots
                    if s.timestamp > change_time
                    and s.timestamp <= change_time + timedelta(hours=2)
                ]

                if post_change:
                    # Check whether adaptation occurred (improved accuracy)
                    accuracy_improvement = self._measure_post_change_accuracy(
                        post_change
                    )
                    if accuracy_improvement > 0:
                        adaptation_times.append(len(post_change))  # Time proxy

            if not adaptation_times:
                return 0.5

            # Score inversely proportional to adaptation time
            avg_adaptation_time = np.mean(adaptation_times)
            # Normalize: 1-5 snapshots is good, >10 is bad
            speed_score = max(0.0, 1.0 - (avg_adaptation_time - 1) / 9)
            return min(1.0, speed_score)

        except (ValueError, ZeroDivisionError) as e:
            logger.error("Error calculating adaptation speed: %s", e)
            return 0.5

    def _calculate_calibration_consistency(
        self, snapshots: List[PerformanceSnapshot]
    ) -> float:
        """Calcula consistência das calibrações."""
        try:
            # Agrupar por símbolo e regime
            grouped = defaultdict(list)

            for snapshot in snapshots:
                key = f"{snapshot.symbol}_{snapshot.actual_regime}"
                grouped[key].append(snapshot)

            consistency_scores = []

            for group_snapshots in grouped.values():
                if len(group_snapshots) < 3:
                    continue

                # Calcular variabilidade dos stops
                stop_distances = [s.stop_distance for s in group_snapshots]
                cv_stops = (
                    np.std(stop_distances) / np.mean(stop_distances)
                    if np.mean(stop_distances) > 0
                    else 1.0
                )

                # Score: baixa variabilidade = alta consistência
                consistency = max(0.0, 1.0 - cv_stops)
                consistency_scores.append(consistency)

            if not consistency_scores:
                return 0.5

            return np.mean(consistency_scores)

        except (ValueError, ZeroDivisionError) as e:
            logger.error("Error calculating consistência: %s", e)
            return 0.5

    def _calculate_self_assessment_accuracy(
        self, snapshots: List[PerformanceSnapshot]
    ) -> float:
        """Calcula precisão da auto-avaliação."""
        try:
            # Comparar previsões anteriores com resultados reais
            if len(self.metrics_history) < 2:
                return 0.5

            # Última previsão vs. performance real
            last_metrics = self.metrics_history[-1]

            # Calcular performance real atual
            actual_performance = self._calculate_actual_performance(snapshots)

            # Comparar com auto-avaliação anterior
            predicted_performance = last_metrics.overall_risk_awareness

            error = abs(predicted_performance - actual_performance)
            accuracy = max(0.0, 1.0 - error)

            return accuracy

        except (ValueError, ZeroDivisionError) as e:
            logger.error("Error calculating auto-avaliação: %s", e)
            return 0.5

    def _calculate_learning_rate(self) -> float:
        """Calcula taxa de aprendizado baseada em melhoria temporal."""
        try:
            if len(self.metrics_history) < 5:
                return 0.5

            # Calcular tendência de melhoria
            recent_scores = [
                m.overall_risk_awareness for m in self.metrics_history[-5:]
            ]

            # Regressão linear simples para detectar tendência
            x = np.arange(len(recent_scores))
            slope = np.polyfit(x, recent_scores, 1)[0]

            # Normalizar slope para 0-1
            # Slope positivo = aprendendo, slope negativo = regredindo
            learning_rate = max(0.0, min(1.0, (slope + 0.1) / 0.2))

            return learning_rate

        except (ValueError, np.linalg.LinAlgError) as e:
            logger.error("Error calculating learning rate: %s", e)
            return 0.5

    def _should_update_assessment(self) -> bool:
        """Determina se deve atualizar a avaliação."""
        if not self.last_assessment:
            return True

        # Atualizar a cada hora ou quando há mudanças significativas
        time_threshold = datetime.now(timezone.utc) - timedelta(hours=1)
        return self.last_assessment < time_threshold

    def _should_trigger_optimization(self) -> bool:
        """Determina se deve trigger auto-otimização."""
        if not self.current_metrics:
            return False

        # Trigger se consciência estiver baixa ou em declínio
        if self.current_metrics.overall_risk_awareness < 0.6:
            return True

        # Ou se learning rate for negativo (regredindo)
        if self.current_metrics.learning_rate < 0.3:
            return True

        return False

    # Métodos auxiliares para baselines
    def _get_baseline_drawdown(self) -> float:
        """Retorna baseline de drawdown definido em configurações."""
        return float(self.baseline_drawdown)

    def _get_baseline_profit_factor(self) -> float:
        """Retorna baseline de profit factor definido em configurações."""
        return float(self.baseline_profit_factor)

    # Métodos auxiliares (implementações simplificadas)
    def _calculate_win_rate_impact(self, snapshots: List[PerformanceSnapshot]) -> float:
        """Calcula impacto na win rate."""
        wins = sum(1 for s in snapshots if s.trade_result == "win")
        total = len([s for s in snapshots if s.trade_result in ["win", "loss"]])
        return wins / total if total > 0 else 0.5

    def _calculate_average_risk_reward(
        self, snapshots: List[PerformanceSnapshot]
    ) -> float:
        """Calcula risk/reward médio."""
        ratios = [
            s.risk_reward_realized
            for s in snapshots
            if s.risk_reward_realized is not None
        ]
        return np.mean(ratios) if ratios else 1.0

    def _calculate_parameter_stability(
        self, snapshots: List[PerformanceSnapshot]
    ) -> float:
        """Calcula estabilidade dos parâmetros."""
        try:
            values_map = {
                "stop_distance": [
                    s.stop_distance for s in snapshots if s.stop_distance > 0
                ],
                "take_profit_distance": [
                    s.take_profit_distance
                    for s in snapshots
                    if s.take_profit_distance > 0
                ],
                "predicted_volatility": [
                    s.predicted_volatility
                    for s in snapshots
                    if s.predicted_volatility > 0
                ],
            }

            scores: List[float] = []
            for vals in values_map.values():
                if len(vals) < 2:
                    continue
                mean_val = float(np.mean(vals))
                if mean_val == 0:
                    continue
                cv = float(np.std(vals) / mean_val)
                scores.append(max(0.0, 1.0 - cv))

            if not scores:
                return 0.5

            return float(np.mean(scores))

        except Exception as e:
            logger.error(f"Error calculating estabilidade de parametros: {e}")
            return 0.5

    def _calculate_system_reliability(
        self, snapshots: List[PerformanceSnapshot]
    ) -> float:
        """Calcula confiabilidade do sistema."""
        try:
            total = len(snapshots)
            if total == 0:
                return 0.5

            valid = sum(
                1
                for s in snapshots
                if s.trade_result in ["win", "loss"] and s.pnl is not None
            )

            return valid / total

        except Exception as e:
            logger.error(f"Error calculating confiabilidade do sistema: {e}")
            return 0.5

    def _calculate_confidence_calibration(
        self, snapshots: List[PerformanceSnapshot]
    ) -> float:
        """Calcula calibração de confiança."""
        try:
            scores = []
            for s in snapshots:
                if (
                    s.risk_reward_realized is None
                    or s.stop_distance <= 0
                    or s.take_profit_distance <= 0
                ):
                    continue

                predicted_rr = s.take_profit_distance / s.stop_distance
                if predicted_rr == 0:
                    continue

                err = abs(predicted_rr - s.risk_reward_realized) / predicted_rr
                scores.append(max(0.0, 1.0 - err))

            if not scores:
                return 0.5

            return float(np.mean(scores))

        except Exception as e:
            logger.error(f"Error calculating calibracao de confianca: {e}")
            return 0.5

    def _measure_post_change_accuracy(
        self, post_change_snapshots: List[PerformanceSnapshot]
    ) -> float:
        """Mede precisão após mudança de regime."""
        correct = sum(1 for s in post_change_snapshots if s.regime_prediction_correct)
        return correct / len(post_change_snapshots) if post_change_snapshots else 0.0

    def _calculate_actual_performance(
        self, snapshots: List[PerformanceSnapshot]
    ) -> float:
        """Calcula performance real atual."""
        win_rate = self._calculate_win_rate_impact(snapshots)
        regime_accuracy = self._calculate_regime_detection_accuracy(snapshots)
        return (win_rate + regime_accuracy) / 2

    async def _detect_consciousness_changes(self):
        """Detecta mudanças significativas na consciência."""
        if len(self.metrics_history) < 2:
            return

        current = self.current_metrics.overall_risk_awareness
        previous = self.metrics_history[-2].overall_risk_awareness

        change = current - previous

        if abs(change) > 0.1:  # Mudança significativa
            direction = "melhorou" if change > 0 else "degradou"
            logger.info(
                "Consciência de risco %s significativamente: %+.3f",
                direction,
                change,
            )

    @staticmethod
    def _log_task_exception(task: asyncio.Task) -> None:
        """Callback para registrar exceções de tasks assíncronas."""
        try:
            exc = task.exception()
            if exc:
                logger.error(f"Task error during risk awareness update: {exc}")
        except asyncio.CancelledError:
            logger.debug("Risk awareness update task cancelled")

    def _task_done_callback(self, task: asyncio.Task) -> None:
        """Marca a task atual como concluída e registra exceções."""
        self._log_task_exception(task)
        if task in self._tasks:
            self._tasks.remove(task)
        if self._current_task is task:
            self._current_task = None

    async def shutdown(self) -> None:
        """Aguarda a conclusão das tasks pendentes criadas pelo engine."""
        if not self._tasks:
            return

        tasks = list(self._tasks)
        self._tasks = []

        for task in tasks:
            if not task.done():
                task.cancel()

        results = await asyncio.gather(*tasks, return_exceptions=True)
        for result in results:
            if isinstance(result, Exception) and not isinstance(
                result, asyncio.CancelledError
            ):
                logger.error(f"Task error during shutdown: {result}")


class RiskPatternDetector:
    """Detector de padrões em performance de risco."""

    def __init__(self):
        self.pattern_buffer = deque(maxlen=1000)
        self.regime_shifts: List[Tuple[datetime, str, str]] = []
        self.volatility_spikes: List[Tuple[datetime, float]] = []
        self.volatility_window: int = 5
        self.volatility_threshold: float = 1.5
        self._last_regime: Optional[str] = None

    def process_snapshot(self, snapshot: PerformanceSnapshot):
        """Processa snapshot para detecção de padrões."""
        self.pattern_buffer.append(snapshot)

        # Detectar padrões específicos
        self._detect_regime_shift_patterns()
        self._detect_volatility_patterns()

    def _detect_regime_shift_patterns(self):
        """Detecta padrões de mudança de regime."""
        if not self.pattern_buffer:
            return

        current = self.pattern_buffer[-1]

        if self._last_regime is None:
            self._last_regime = current.actual_regime
            return

        if current.actual_regime != self._last_regime:
            self.regime_shifts.append(
                (current.timestamp, self._last_regime, current.actual_regime)
            )
            logger.info(
                "Mudança de regime detectada: %s -> %s",
                self._last_regime,
                current.actual_regime,
            )
            self._last_regime = current.actual_regime

    def _detect_volatility_patterns(self):
        """Detecta padrões de volatilidade."""
        if len(self.pattern_buffer) < self.volatility_window:
            return

        recent = list(self.pattern_buffer)[-self.volatility_window :]
        *historical, current = recent
        avg_vol = float(np.mean([s.actual_volatility for s in historical]))

        if (
            avg_vol > 0
            and current.actual_volatility > avg_vol * self.volatility_threshold
        ):
            self.volatility_spikes.append(
                (current.timestamp, current.actual_volatility)
            )
            logger.info(
                "Pico de volatilidade detectado: %.3f (média %.3f)",
                current.actual_volatility,
                avg_vol,
            )


class RiskParameterOptimizer:
    """Otimizador automático de parâmetros de risco."""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.optimization_history = []
        # Referência opcional ao DynamicRiskController ou engine
        self._parameter_store = self.config.get("dynamic_risk_parameters", self.config)

    def _update_parameter(
        self, parameter: str, adjustment: float
    ) -> Optional[Tuple[float, float]]:
        """Atualiza o valor do parâmetro no armazenamento configurado."""
        if parameter not in self._parameter_store:
            logger.warning("Parâmetro %s não encontrado para atualização", parameter)
            return None

        try:
            old_value = float(self._parameter_store[parameter])
            new_value = old_value + adjustment
            self._parameter_store[parameter] = type(self._parameter_store[parameter])(
                new_value
            )
            logger.debug(
                "Parâmetro %s atualizado de %.6f para %.6f",
                parameter,
                old_value,
                new_value,
            )
            return old_value, new_value
        except Exception as exc:
            logger.error("Erro ao atualizar parâmetro %s: %s", parameter, exc)
            return None

    async def optimize_parameters(
        self, snapshots: List[PerformanceSnapshot], metrics: RiskAwarenessMetrics
    ):
        """
        Otimiza parâmetros baseado na performance observada.

        Args:
            snapshots: Histórico de performance
            metrics: Métricas atuais de consciência
        """
        try:
            logger.info("Iniciando auto-otimização de parâmetros de risco")

            # Analisar areas de melhoria
            improvement_areas = self._identify_improvement_areas(metrics)

            # Gerar sugestões de otimização
            optimization_suggestions = self._generate_optimization_suggestions(
                snapshots, improvement_areas
            )

            # Aplicar otimizações (com cautela)
            await self._apply_optimizations(optimization_suggestions)

            logger.info(
                "Auto-otimização concluída: %d ajustes aplicados",
                len(optimization_suggestions),
            )

        except Exception as e:
            logger.error(f"Erro na auto-otimização: {e}")

    def _identify_improvement_areas(self, metrics: RiskAwarenessMetrics) -> List[str]:
        """Identifica áreas que precisam de melhoria."""
        areas = []

        if metrics.regime_detection_accuracy < 0.7:
            areas.append("regime_detection")

        if metrics.volatility_prediction_score < 0.6:
            areas.append("volatility_prediction")

        if metrics.drawdown_reduction_score < 0.5:
            areas.append("drawdown_control")

        if metrics.calibration_consistency < 0.8:
            areas.append("calibration_consistency")

        return areas

    def _generate_optimization_suggestions(
        self, snapshots: List[PerformanceSnapshot], areas: List[str]
    ) -> List[Dict[str, Any]]:
        """Gera sugestões de otimização."""
        suggestions = []

        for area in areas:
            if area == "regime_detection":
                suggestions.append(
                    {
                        "parameter": "volatility_threshold_low",
                        "adjustment": -0.01,  # Reduzir threshold
                        "reason": "Melhorar detecção de regime calmo",
                    }
                )

            elif area == "volatility_prediction":
                suggestions.append(
                    {
                        "parameter": "atr_period",
                        "adjustment": 1,  # Aumentar período
                        "reason": "Melhorar estabilidade da previsão",
                    }
                )

            elif area == "drawdown_control":
                suggestions.append(
                    {
                        "parameter": "atr_multiplier_base",
                        "adjustment": 0.1,  # Aumentar multiplicador
                        "reason": "Reduzir drawdown",
                    }
                )

        return suggestions

    async def _apply_optimizations(self, suggestions: List[Dict[str, Any]]):
        """Aplica otimizações com cautela."""
        for suggestion in suggestions:
            # Por segurança, aplicar apenas ajustes pequenos
            adjustment = suggestion["adjustment"] * 0.5  # Reduzir intensidade

            logger.info(
                "Aplicando otimização: %s %+.3f (%s)",
                suggestion["parameter"],
                adjustment,
                suggestion["reason"],
            )

            # Aplicar ajuste
            update_result = self._update_parameter(suggestion["parameter"], adjustment)

            # Registrar histórico
            history_entry = {
                "timestamp": datetime.now(timezone.utc),
                "parameter": suggestion["parameter"],
                "adjustment": adjustment,
                "reason": suggestion["reason"],
            }
            if update_result:
                history_entry.update(
                    {"old_value": update_result[0], "new_value": update_result[1]}
                )
            self.optimization_history.append(history_entry)


# Interface principal para integração
def create_risk_awareness_engine(
    config: Optional[Dict[str, Any]] = None,
) -> RiskAwarenessEngine:
    """
    Factory function para criar engine de consciência de risco.

    Args:
        config: Configuração do sistema. Se ``None``, a configuração padrão é
            carregada a partir do ``ConfigLoader`` para garantir integração com o
            metamodelo de configuração. Pode incluir ``baseline_drawdown`` e
            ``baseline_profit_factor`` para personalizar as métricas de
            referência.

    Returns:
        Instância configurada do :class:`RiskAwarenessEngine`.
    """
    if config is None:
        from ..config import ConfigLoader

        loader = ConfigLoader()
        config = loader.get_section("risk_awareness") or {}

    return RiskAwarenessEngine(config)


if __name__ == "__main__":
    # Exemplo de uso
    config = {
        "observation_window_hours": 24,
        "min_samples": 50,
        "learning_rate": 0.1,
        "confidence_threshold": 0.7,
    }

    engine = create_risk_awareness_engine(config)
    logger.info(
        "Risk Awareness Engine criado: consciência atual = %.3f",
        engine.get_risk_consciousness_level(),
    )
