"""Modelo de dados para métricas do QUALIA Framework."""

from typing import Any, Dict, List, Optional

try:
    from pydantic import BaseModel, Field, ConfigDict
except ImportError:  # pragma: no cover - pydantic opcional
    BaseModel = None

if BaseModel:

    class QUALIAMetrics(BaseModel):
        """Modelo Pydantic para métricas do QUALIA."""

        max_history_size: int = Field(
            default=0,
            description="Número máximo de valores a manter em cada lista de métrica.",
        )
        page_entropy: List[Optional[float]] = Field(default_factory=list)
        renyi2: List[Optional[float]] = Field(default_factory=list)
        mi_bh_rad: List[Optional[float]] = Field(default_factory=list)
        classical_mutual_information: List[Optional[float]] = Field(
            default_factory=list
        )
        loschmidt_echo: List[Optional[float]] = Field(default_factory=list)
        otoc: List[Optional[float]] = Field(default_factory=list)
        eigphase_dist: Optional[List[float]] = None
        quantum_entropy: List[Optional[float]] = Field(default_factory=list)
        sv_entropy: List[Optional[float]] = Field(default_factory=list)
        linear_entropy: List[Optional[float]] = Field(default_factory=list)
        coefficient_evolution: List[Dict[str, float]] = Field(default_factory=list)
        ctc_purity: List[Optional[float]] = Field(default_factory=list)
        informational_mass: List[Optional[float]] = Field(default_factory=list)
        lambda_gravity: List[Optional[float]] = Field(default_factory=list)
        build_circuit_ms: List[Optional[float]] = Field(default_factory=list)
        run_ms: List[Optional[float]] = Field(default_factory=list)
        build_circuit_count: List[int] = Field(default_factory=list)
        run_count: List[int] = Field(default_factory=list)

        def add_entry(self, metric_name: str, value: Any) -> None:
            """Adicionar valor e manter o tamanho do histórico dentro do limite."""

            if not hasattr(self, metric_name):
                setattr(self, metric_name, [])
            metric_list = getattr(self, metric_name)
            metric_list.append(value)
            if self.max_history_size and len(metric_list) > self.max_history_size:
                del metric_list[: -self.max_history_size]

        def get_metrics_dict(self) -> Dict[str, Any]:
            try:
                return self.model_dump()
            except AttributeError:  # pragma: no cover - compat Pydantic v1
                return self.dict()

        model_config = ConfigDict(validate_assignment=True, extra="allow")

else:
    from dataclasses import field, make_dataclass

    _fields = [
        ("max_history_size", int, field(default=0)),
        ("page_entropy", List[Optional[float]], field(default_factory=list)),
        ("renyi2", List[Optional[float]], field(default_factory=list)),
        ("mi_bh_rad", List[Optional[float]], field(default_factory=list)),
        (
            "classical_mutual_information",
            List[Optional[float]],
            field(default_factory=list),
        ),
        ("loschmidt_echo", List[Optional[float]], field(default_factory=list)),
        ("otoc", List[Optional[float]], field(default_factory=list)),
        ("eigphase_dist", Optional[List[float]], None),
        ("quantum_entropy", List[Optional[float]], field(default_factory=list)),
        ("sv_entropy", List[Optional[float]], field(default_factory=list)),
        ("linear_entropy", List[Optional[float]], field(default_factory=list)),
        (
            "coefficient_evolution",
            List[Dict[str, float]],
            field(default_factory=list),
        ),
        ("ctc_purity", List[Optional[float]], field(default_factory=list)),
        ("informational_mass", List[Optional[float]], field(default_factory=list)),
        ("lambda_gravity", List[Optional[float]], field(default_factory=list)),
        ("build_circuit_ms", List[Optional[float]], field(default_factory=list)),
        ("run_ms", List[Optional[float]], field(default_factory=list)),
        ("build_circuit_count", List[int], field(default_factory=list)),
        ("run_count", List[int], field(default_factory=list)),
    ]

    QUALIAMetrics = make_dataclass("QUALIAMetrics", _fields)

    def add_entry(self, metric_name: str, value: Any) -> None:
        """Adicionar valor e manter o tamanho do histórico dentro do limite."""

        if not hasattr(self, metric_name):
            setattr(self, metric_name, [])
        metric_list = getattr(self, metric_name)
        metric_list.append(value)
        if self.max_history_size and len(metric_list) > self.max_history_size:
            del metric_list[: -self.max_history_size]

    def get_metrics_dict(self) -> Dict[str, Any]:
        return {k: getattr(self, k) for k in getattr(self, "__dataclass_fields__", {})}

    QUALIAMetrics.add_entry = add_entry
    QUALIAMetrics.get_metrics_dict = get_metrics_dict


class QUALIAMetricsHelper:
    """Helper para trabalhar com QUALIAMetrics em forma de dicionário."""

    @staticmethod
    def create_metrics() -> Dict[str, Any]:
        return {
            "page_entropy": [],
            "renyi2": [],
            "mi_bh_rad": [],
            "classical_mutual_information": [],
            "loschmidt_echo": [],
            "otoc": [],
            "eigphase_dist": None,
            "quantum_entropy": [],
            "sv_entropy": [],
            "linear_entropy": [],
            "coefficient_evolution": [],
            "ctc_purity": [],
            "informational_mass": [],
            "lambda_gravity": [],
            "build_circuit_ms": [],
            "run_ms": [],
            "build_circuit_count": [],
            "run_count": [],
        }

    @staticmethod
    def add_entry(metrics: Dict[str, Any], metric_name: str, value: Any) -> None:
        if metric_name not in metrics:
            metrics[metric_name] = []
        metrics[metric_name].append(value)

    @staticmethod
    def get_metrics_dict(metrics: Dict[str, Any]) -> Dict[str, Any]:
        return metrics
