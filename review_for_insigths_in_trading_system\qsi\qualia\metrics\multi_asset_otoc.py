from __future__ import annotations

from typing import Dict, Iterable, <PERSON><PERSON>, List

import numpy as np

from ..core.retrocausality import compute_otoc
from ..utils.logger import get_logger

logger = get_logger(__name__)


def _returns(series: Iterable[float]) -> np.ndarray:
    """Return logarithmic returns from price series."""
    arr = np.asarray(list(series), dtype=float)
    if arr.ndim != 1 or len(arr) < 2:
        raise ValueError("Series must be 1D with at least 2 points")
    return np.diff(np.log(arr))


def multi_asset_otoc(
    price_series: Dict[str, Iterable[float]], lag: int = 1
) -> Dict[Tuple[str, str], float]:
    """Calculate cross-asset OTOCs using return series.

    Parameters
    ----------
    price_series : dict
        Mapping of asset identifiers to price series.
    lag : int, optional
        Time lag applied to the second asset when computing the OTOC.

    Returns
    -------
    dict[tuple[str, str], float]
        OTOC values for each ordered asset pair.
    """
    assets = list(price_series)
    returns = {name: _returns(prices) for name, prices in price_series.items()}
    result: Dict[Tuple[str, str], float] = {}

    for name_a in assets:
        ra = returns[name_a]
        for name_b in assets:
            if name_a == name_b:
                continue
            rb = returns[name_b]
            length = min(len(ra), len(rb) - lag)
            if length <= 0:
                logger.warning(
                    "Insufficient data for pair %s/%s with lag=%s",
                    name_a,
                    name_b,
                    lag,
                )
                continue
            state_a = ra[:length]
            state_b = rb[lag : lag + length]
            result[(name_a, name_b)] = compute_otoc(state_a, state_b)
    return result


def multi_asset_otoc_matrix(
    price_series: Dict[str, Iterable[float]], lag: int = 1
) -> Tuple[np.ndarray, List[str]]:
    """Return an OTOC matrix across multiple assets.

    Parameters
    ----------
    price_series
        Mapping of asset identifiers to price series.
    lag
        Temporal offset applied when correlating the second asset.

    Returns
    -------
    ndarray
        ``m x m`` matrix of OTOC values where ``m`` is ``len(price_series)``.
    list of str
        Order of asset identifiers corresponding to matrix indices.
    """

    assets: List[str] = list(price_series)
    m = len(assets)
    returns = {name: _returns(prices) for name, prices in price_series.items()}
    matrix = np.empty((m, m), dtype=float)

    for i, name_a in enumerate(assets):
        ra = returns[name_a]
        for j, name_b in enumerate(assets):
            if i == j:
                matrix[i, j] = 1.0
                continue
            rb = returns[name_b]
            length = min(len(ra), len(rb) - lag)
            if length <= 0:
                logger.warning(
                    "Insufficient data for pair %s/%s with lag=%s",
                    name_a,
                    name_b,
                    lag,
                )
                matrix[i, j] = np.nan
                continue
            state_a = ra[:length]
            state_b = rb[lag : lag + length]
            matrix[i, j] = compute_otoc(state_a, state_b)

    return matrix, assets
