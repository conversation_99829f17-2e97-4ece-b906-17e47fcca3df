"""Logging setup module for QUALIA monitoring."""

import logging
import logging.handlers
from pathlib import Path
from typing import Dict, Any


def setup_logging(config: Dict[str, Any] | None = None) -> None:
    """Setup logging configuration for QUALIA system."""
    if config is None:
        config = {}

    # Default configuration
    level = config.get("level", "INFO")
    log_file = config.get("file", "data/logs/qualia.log")
    max_bytes = config.get("max_bytes", 10485760)  # 10MB
    backup_count = config.get("backup_count", 5)
    format_str = config.get(
        "format", "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )

    # Create logs directory if it doesn't exist
    log_path = Path(log_file)
    log_path.parent.mkdir(parents=True, exist_ok=True)

    # Configure root logger
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format=format_str,
        handlers=[
            logging.StreamHandler(),  # Console output
            logging.handlers.RotatingFileHandler(
                log_file, maxBytes=max_bytes, backupCount=backup_count
            ),
        ],
    )

    # Set specific logger levels
    logging.getLogger("qiskit").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("ccxt").setLevel(logging.INFO)

    logging.info("Logging system initialized")
