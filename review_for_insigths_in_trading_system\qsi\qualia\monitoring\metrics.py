"""Metrics collection module for QUALIA monitoring."""

import asyncio
import logging
import time
from dataclasses import dataclass, field
from typing import Dict, Any, List, Optional, Iterable
from collections import defaultdict, deque

try:  # pragma: no cover - optional dependency
    from datadog import DogStatsd
except Exception:  # pragma: no cover - datadog not installed
    DogStatsd = None  # type: ignore[misc]

from ..memory.event_bus import SimpleEventBus


@dataclass
class Metric:
    """Individual metric data point."""

    name: str
    value: float
    timestamp: float = field(default_factory=time.time)
    tags: Dict[str, str] = field(default_factory=dict)


class MetricsCollector:
    """Collects and aggregates system metrics and forwards to DogStatsD."""

    def __init__(
        self,
        max_metrics: int = 10000,
        *,
        statsd_client: Optional[DogStatsd] = None,
        event_bus: "SimpleEventBus | None" = None,
    ) -> None:
        """Initialize the metrics collector.

        Parameters
        ----------
        max_metrics : int, default 10000
            Maximum number of data points stored for each metric.
        statsd_client : DogStatsd, optional
            Client used to emit metrics to DogStatsD when provided.
        """

        self.max_metrics = max_metrics
        self.metrics: Dict[str, deque] = defaultdict(
            lambda: deque(maxlen=self.max_metrics)
        )
        self.running = False
        self.logger = logging.getLogger(__name__)
        self.statsd = statsd_client
        self.event_bus = event_bus

    async def run(self):
        """Start metrics collection loop."""
        self.running = True
        self.logger.info("Metrics collector started")

        while self.running:
            try:
                await self._collect_system_metrics()
                await asyncio.sleep(60)  # Collect metrics every minute
            except Exception as e:
                self.logger.error(f"Error in metrics collection: {e}")
                await asyncio.sleep(5)  # Short retry delay

    async def shutdown(self):
        """Shutdown metrics collector."""
        self.running = False
        self.logger.info("Metrics collector shutdown")

    def record_metric(
        self, name: str, value: float, tags: Dict[str, str] | None = None
    ) -> None:
        """Record a metric value."""
        metric = Metric(name=name, value=value, tags=tags or {})
        self.metrics[name].append(metric)

        if self.event_bus:
            from ..events import MetricRecordedEvent

            self.event_bus.publish(
                "monitor.metric_recorded",
                MetricRecordedEvent(name=name, value=value, tags=metric.tags),
            )

        if self.statsd:
            tag_list: Iterable[str] | None = None
            if metric.tags:
                tag_list = [f"{k}:{v}" for k, v in metric.tags.items()]
            try:
                if name.endswith("_ms"):
                    self.statsd.timing(name, value, tags=tag_list)
                else:
                    self.statsd.gauge(name, value, tags=tag_list)
            except Exception:  # pragma: no cover - best effort
                self.logger.debug("Failed to emit metric %s via statsd", name)

    async def record_metrics(self, metrics: Dict[str, float | int | bool]) -> None:
        """Record multiple metrics at once."""

        for name, value in metrics.items():
            self.record_metric(name, float(value))

    async def record_retro_comparative_metrics(
        self, retro_active: bool, prediction_confidence: float
    ) -> None:
        """Record prediction confidence tagged by retrocausal activation."""

        tag_state = "1" if retro_active else "0"
        self.record_metric(
            "retrocausality.prediction_confidence",
            prediction_confidence,
            tags={"retro_active": tag_state},
        )

    def get_metrics(self, name: str) -> List[Metric]:
        """Get all metrics for a given name."""
        return list(self.metrics.get(name, []))

    def get_latest_metric(self, name: str) -> Metric | None:
        """Get the latest metric value for a given name."""
        metrics = self.metrics.get(name, [])
        return metrics[-1] if metrics else None

    async def _collect_system_metrics(self):
        """Collect basic system metrics."""
        import psutil

        # CPU usage
        cpu_percent = psutil.cpu_percent(interval=1)
        self.record_metric("system.cpu_percent", cpu_percent)

        # Memory usage
        memory = psutil.virtual_memory()
        self.record_metric("system.memory_percent", memory.percent)
        self.record_metric("system.memory_used_gb", memory.used / (1024**3))

        # Disk usage
        disk = psutil.disk_usage("/")
        self.record_metric("system.disk_percent", disk.percent)

        # Network stats
        network = psutil.net_io_counters()
        self.record_metric("system.bytes_sent", network.bytes_sent)
        self.record_metric("system.bytes_recv", network.bytes_recv)

    def get_summary(self) -> Dict[str, Any]:
        """Get metrics summary."""
        summary = {}

        for name, metric_list in self.metrics.items():
            if not metric_list:
                continue

            values = [m.value for m in metric_list]
            summary[name] = {
                "count": len(values),
                "latest": values[-1] if values else None,
                "min": min(values) if values else None,
                "max": max(values) if values else None,
                "avg": sum(values) / len(values) if values else None,
            }

        return summary


_GLOBAL_EVENT_BUS: SimpleEventBus | None = None
global_collector = MetricsCollector(event_bus=_GLOBAL_EVENT_BUS)


def set_event_bus(event_bus: SimpleEventBus) -> None:
    """Configura barramento global para emissão de métricas."""

    global _GLOBAL_EVENT_BUS, global_collector
    _GLOBAL_EVENT_BUS = event_bus
    global_collector.event_bus = event_bus
    try:  # Atualiza monitor de performance global, se carregado
        from ..monitor import performance as perf_mod

        perf_mod.set_event_bus(event_bus)
    except Exception:  # pragma: no cover - not critical
        pass


def get_collector() -> MetricsCollector:
    """Return the module-level metrics collector."""
    return global_collector
