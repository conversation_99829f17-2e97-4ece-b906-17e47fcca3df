# src/qualia/personas/retail_cluster.py

from __future__ import annotations

from typing import Dict, Any
import numpy as np

from .base import BasePersona

class RetailCluster(BasePersona):
    """
    Persona que representa um cluster de traders de varejo.

    - Altamente influenciada pelo sentimento de notícias e redes sociais.
    - Reage ao Fear & Greed Index.
    - Comportamento tende a ser de manada (comprar na euforia, vender no pânico).
    """

    def _initialize_state(self) -> Dict[str, Any]:
        """
        Inicializa o estado com foco em sentimento e medo/ganância.
        """
        return {
            "confidence": 0.5, # Confiança inicial neutra
            "sentiment_multiplier": self.config.get("sentiment_multiplier", 1.5),
            "fear_greed_score": 50, # Começa neutro (0-100)
            "action_bias": "NEUTRAL" # Pode ser "BUY", "SELL", ou "NEUTRAL"
        }

    def update_state(self, market_data: Dict[str, Any], external_data: Dict[str, Any]) -> None:
        """
        Atualiza o estado com base no sentimento do Farsight e no Fear & Greed Index.
        """
        # Atualizar com base no Fear & Greed Index
        fear_greed = market_data.get("fear_greed_index")
        if fear_greed is not None:
            self.state["fear_greed_score"] = fear_greed

        # Atualizar com base no CollectiveMindState do Farsight
        mind_state = external_data.get("collective_mind_state")
        if mind_state:
            persona_impact = mind_state.persona_impact.get(self.__class__.__name__, {})
            
            confidence_boost = persona_impact.get("confidence_boost", 0.0)
            self.state["confidence"] = np.clip(self.state["confidence"] + confidence_boost, 0.1, 0.9)
            
            action_bias = persona_impact.get("action_bias")
            if action_bias in ["BUY", "SELL", "NEUTRAL"]:
                self.state["action_bias"] = action_bias

    def get_probable_action(self, market_state: Dict[str, Any]) -> Dict[str, float]:
        """
        Gera um `DecisionVector` baseado no medo, ganância e sentimento.
        """
        fear_greed = self.state["fear_greed_score"]
        action_bias = self.state["action_bias"]
        confidence = self.state["confidence"]

        # Mapear Fear & Greed (0-100) para um score de ação (-1 a 1)
        # < 25: Pânico (Venda Forte)
        # > 75: Euforia (Compra Forte)
        if fear_greed > 75:
            action_score = (fear_greed - 75) / 25.0 # 0 a 1
        elif fear_greed < 25:
            action_score = (fear_greed - 25) / -25.0 # 0 a -1
        else:
            action_score = 0 # Neutro

        # Incorporar o viés de ação do Farsight
        if action_bias == "BUY":
            action_score = (action_score + 0.5) / 1.5
        elif action_bias == "SELL":
            action_score = (action_score - 0.5) / 1.5

        action_score = np.clip(action_score, -1.0, 1.0)

        # Calcular probabilidades finais
        if action_score > 0: # Viés de compra
            buy_prob = confidence * action_score
            hold_prob = 1 - buy_prob
            sell_prob = 0.0
        elif action_score < 0: # Viés de venda
            sell_prob = confidence * abs(action_score)
            hold_prob = 1 - sell_prob
            buy_prob = 0.0
        else: # Neutro
            buy_prob = 0.05
            sell_prob = 0.05
            hold_prob = 0.9

        # Normalizar para garantir que a soma seja 1
        total = buy_prob + sell_prob + hold_prob
        return {
            "BUY": buy_prob / total,
            "SELL": sell_prob / total,
            "HOLD": hold_prob / total
        }
