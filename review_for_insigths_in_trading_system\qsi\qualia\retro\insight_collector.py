from __future__ import annotations

"""Utilities for collecting retrocausal insights."""

from dataclasses import dataclass, asdict
from datetime import datetime, timezone
from pathlib import Path
from threading import Lock
from typing import Any, Dict, List
import json

from ..memory.event_bus import SimpleEventBus

RETROCAUSAL_INSIGHT_EVENT = "retrocausal.insight"


@dataclass
class InsightRecord:
    """Stored prediction record for offline analysis."""

    timestamp: float
    prediction: List[float]
    temporal_field_strength: float
    prediction_confidence: float


class InsightCollector:
    """Collects retrocausal predictions and persists them to disk."""

    def __init__(
        self,
        *,
        event_bus: SimpleEventBus | None = None,
        storage_dir: str = "data/retrocausal",
    ) -> None:
        self._records: List[InsightRecord] = []
        self._lock = Lock()
        self.storage_dir = Path(storage_dir)
        self.storage_dir.mkdir(parents=True, exist_ok=True)
        self.file_path = self.storage_dir / "insights.jsonl"
        self.event_bus = event_bus
        if self.event_bus is not None:
            self.event_bus.subscribe(RETROCAUSAL_INSIGHT_EVENT, self._on_event)

    def _on_event(self, payload: Dict[str, Any]) -> None:
        prediction = payload.get("prediction", [])
        if not isinstance(prediction, list):
            try:
                prediction = list(prediction)
            except Exception:
                prediction = [float(prediction)]
        record = InsightRecord(
            timestamp=float(
                payload.get("timestamp", datetime.now(timezone.utc).timestamp())
            ),
            prediction=[float(x) for x in prediction],
            temporal_field_strength=float(payload.get("temporal_field_strength", 0.0)),
            prediction_confidence=float(payload.get("prediction_confidence", 0.0)),
        )
        self.record(record)

    def record(self, record: InsightRecord) -> None:
        with self._lock:
            self._records.append(record)
            with self.file_path.open("a") as f:
                json.dump(asdict(record), f)
                f.write("\n")

    def get_records(self) -> List[InsightRecord]:
        with self._lock:
            return list(self._records)


global_insight_collector = InsightCollector()

__all__ = [
    "InsightCollector",
    "InsightRecord",
    "RETROCAUSAL_INSIGHT_EVENT",
    "global_insight_collector",
]
