from __future__ import annotations

"""Persistence and analysis of retrocausal predictions."""

from dataclasses import dataclass
from datetime import datetime, timezone
from threading import Lock
from typing import Any, Iterable, List, Optional
import json
import sqlite3
from pathlib import Path

import numpy as np


@dataclass
class RetroRecord:
    """Record linking a prediction with its eventual result."""

    id: int
    timestamp: float
    strategy: str
    prediction: List[float]
    result: Optional[List[float]]
    error: Optional[float]
    accuracy: Optional[float]


class RetrocausalInsightCollector:
    """Store predictions and compare them with real outcomes."""

    def __init__(self, db_path: str = "data/retrocausal/retro_insights.db") -> None:
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        self._lock = Lock()
        self.conn = sqlite3.connect(self.db_path.as_posix(), check_same_thread=False)
        self._init_db()

    def _init_db(self) -> None:
        with self.conn:
            self.conn.execute(
                """
                CREATE TABLE IF NOT EXISTS insights (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp REAL,
                    strategy TEXT,
                    prediction TEXT,
                    result TEXT,
                    error REAL,
                    accuracy REAL
                )
                """
            )

    def record_prediction(
        self,
        strategy: str,
        prediction: Iterable[float],
        *,
        timestamp: Optional[float] = None,
    ) -> int:
        """Persist a future prediction emitted by a strategy."""
        ts = timestamp or datetime.now(timezone.utc).timestamp()
        pred_json = json.dumps(list(map(float, prediction)))
        with self._lock, self.conn:
            cur = self.conn.execute(
                "INSERT INTO insights(timestamp, strategy, prediction) VALUES (?, ?, ?)",
                (ts, strategy, pred_json),
            )
            return int(cur.lastrowid)

    def record_result(self, record_id: int, result: Iterable[float]) -> None:
        """Store the real result and compute error and accuracy."""
        result_list = list(map(float, result))
        result_json = json.dumps(result_list)
        with self._lock:
            row = self.conn.execute(
                "SELECT prediction FROM insights WHERE id=?", (record_id,)
            ).fetchone()
            if row is None:
                return
            prediction = json.loads(row[0])
            err = float(np.mean(np.abs(np.array(prediction) - np.array(result_list))))
            acc = max(0.0, 1.0 - err)
            with self.conn:
                self.conn.execute(
                    "UPDATE insights SET result=?, error=?, accuracy=? WHERE id=?",
                    (result_json, err, acc, record_id),
                )

    def fetch_records(self) -> List[RetroRecord]:
        """Retrieve all stored records ordered by id."""
        with self._lock:
            rows = self.conn.execute(
                "SELECT id, timestamp, strategy, prediction, result, error, accuracy FROM insights ORDER BY id"
            ).fetchall()
        records = []
        for r in rows:
            prediction = json.loads(r[3]) if r[3] is not None else []
            result = json.loads(r[4]) if r[4] is not None else None
            records.append(
                RetroRecord(
                    id=int(r[0]),
                    timestamp=float(r[1]),
                    strategy=str(r[2]),
                    prediction=prediction,
                    result=result,
                    error=float(r[5]) if r[5] is not None else None,
                    accuracy=float(r[6]) if r[6] is not None else None,
                )
            )
        return records

    def overall_accuracy(self) -> float:
        """Return mean accuracy over all completed records."""
        with self._lock:
            row = self.conn.execute(
                "SELECT AVG(accuracy) FROM insights WHERE accuracy IS NOT NULL"
            ).fetchone()
        return float(row[0]) if row and row[0] is not None else float("nan")

    def strategy_accuracy(self, strategy: str) -> float:
        """Return mean accuracy for a given strategy."""
        with self._lock:
            row = self.conn.execute(
                "SELECT AVG(accuracy) FROM insights WHERE strategy=? AND accuracy IS NOT NULL",
                (strategy,),
            ).fetchone()
        return float(row[0]) if row and row[0] is not None else float("nan")

    def strategy_error(self, strategy: str) -> float:
        """Return mean prediction error for a given strategy."""
        with self._lock:
            row = self.conn.execute(
                "SELECT AVG(error) FROM insights WHERE strategy=? AND error IS NOT NULL",
                (strategy,),
            ).fetchone()
        return float(row[0]) if row and row[0] is not None else float("nan")

    def close(self) -> None:
        """Close the underlying database connection."""
        with self._lock:
            self.conn.close()
