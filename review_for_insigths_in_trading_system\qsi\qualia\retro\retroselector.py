"""Seleção retrocausal de trajetórias.

Este módulo implementa um filtro simples de trajetórias com base em
limites de lucro e drawdown definidos por :class:`~src.qualia.intentions.IntentionEnvelope`.
"""

from __future__ import annotations

import numpy as np

from ..utils.feature_flags import feature_toggle

from ..intentions import IntentionEnvelope
from ..utils.logger import get_logger

logger = get_logger(__name__)


class RetroSelector:
    """Seleciona trajetórias que atendem a metas de lucro e drawdown."""

    def select(
        self,
        paths: np.ndarray,
        env: IntentionEnvelope,
        *,
        measurement_variance: float = 1.0,
        initial_variance: float = 1.0,
        return_empty: bool = False,
    ) -> np.ndarray:
        """Retorna apenas as trajetórias que respeitam o envelope.

        Quando ``QUALIA_EXPERIMENTAL_RETRO`` está configurado para ``"true"``
        (case insensitive), as métricas de lucro e drawdown são ponderadas por
        uma previsão ARIMA combinada a um filtro de Kalman simples antes da
        comparação com o envelope.

        Parameters
        ----------
        paths
            Matriz ``(n, m)`` com ``n`` trajetórias de ``m`` etapas
            representando incrementos de PnL.
        env
            Envelope definindo ``profit_target`` e ``max_drawdown``.
        measurement_variance
            Ruído de medida assumido no filtro de Kalman.
        initial_variance
            Variância inicial assumida para o filtro de Kalman.

        return_empty
            Quando ``True`` e nenhuma trajetória atender aos critérios,
            retorna um array vazio em vez de ``paths`` inalterado.

        Returns
        -------
        np.ndarray
            Subconjunto das trajetórias que atendem aos critérios ou ``paths``
            inalterado se nenhuma servir (ou array vazio quando
            ``return_empty`` for ``True``).
        """
        if paths.ndim != 2:
            raise ValueError("paths deve ser uma matriz 2D")

        if np.isnan(paths).any():
            raise ValueError("paths nao pode conter valores NaN")
        if not np.isfinite(paths).all():
            raise ValueError("paths nao pode conter valores infinitos")

        cumulative = np.cumsum(paths, axis=1)
        pnl = cumulative[:, -1]
        running_max = np.maximum.accumulate(cumulative, axis=1)
        drawdowns = cumulative - running_max
        min_drawdown = np.min(drawdowns, axis=1)

        if feature_toggle("experimental_retro", env_var="QUALIA_EXPERIMENTAL_RETRO"):
            logger.debug("Modo experimental do RetroSelector ativado")
            pnl, min_drawdown = self._apply_forecast_filter(
                paths,
                pnl,
                min_drawdown,
                measurement_variance,
                initial_variance,
            )

        mask = (pnl >= env.profit_target) & (min_drawdown >= -env.max_drawdown)

        if not np.any(mask):
            logger.debug("Nenhuma trajetória atingiu os critérios de seleção")
            if return_empty:
                return np.empty((0, paths.shape[1]), dtype=paths.dtype)
            return paths

        selected = paths[mask]
        logger.debug(
            "%d de %d trajetórias selecionadas", selected.shape[0], paths.shape[0]
        )
        return selected

    @staticmethod
    def _apply_forecast_filter(
        paths: np.ndarray,
        pnl: np.ndarray,
        min_drawdown: np.ndarray,
        measurement_variance: float,
        initial_variance: float,
    ) -> tuple[np.ndarray, np.ndarray]:
        """Pondera métricas atuais usando previsão ARIMA e filtro de Kalman.

        Parameters
        ----------
        paths
            Matriz ``(n, m)`` contendo as trajetórias originais.
        pnl
            Vetor de PnL acumulado por trajetória.
        min_drawdown
            Vetor com o drawdown mínimo observado em cada trajetória.
        measurement_variance
            Ruído de medida assumido no filtro de Kalman.
        initial_variance
            Variância inicial assumida para o filtro de Kalman.

        Returns
        -------
        Tuple[np.ndarray, np.ndarray]
            Novos vetores ``pnl`` e ``min_drawdown`` após ponderação.
        """

        # Evitamos ``np.apply_along_axis`` para reduzir o overhead de loops
        # implícitos. Operações vetorizadas são usadas sempre que possível.

        kalman_gain = initial_variance / (initial_variance + measurement_variance)

        m = paths.shape[1]
        series_sum = paths.sum(axis=1)
        series_mean = paths.mean(axis=1)
        series_var = paths.var(axis=1)

        if m < 3:
            forecasts = series_mean * (m + 1)
        else:
            prev = paths[:, :-1]
            nxt = paths[:, 1:]
            denom = np.sum(prev * prev, axis=1)
            num = np.sum(nxt * prev, axis=1)
            phi = np.divide(num, denom, out=np.zeros_like(num), where=denom != 0)
            forecasts = series_sum + phi * paths[:, -1]

        high_var = series_var > 100.0
        forecasts = np.where(high_var, series_mean * (m + 1), forecasts)

        filtered_pnl = forecasts + kalman_gain * (pnl - forecasts)

        forecast_increment = forecasts - series_sum
        extended = np.concatenate([paths, forecast_increment[:, None]], axis=1)
        cumulative = np.cumsum(extended, axis=1)
        running_max = np.maximum.accumulate(cumulative, axis=1)
        predicted_drawdown = np.min(cumulative - running_max, axis=1)

        filtered_dd = predicted_drawdown + kalman_gain * (
            min_drawdown - predicted_drawdown
        )

        return filtered_pnl, filtered_dd

    @staticmethod
    def _arima_forecast(series: np.ndarray) -> float:
        """Retorna previsão AR(1) para a soma acumulada da série.

        Para séries muito curtas ou com alta variância, utiliza-se a média móvel
        como fallback para evitar instabilidade numérica.
        """

        if series.size < 3 or np.var(series) > 100.0:
            return float(series.mean() * (series.size + 1))

        x = series
        # phi = sum(x_t * x_{t-1}) / sum(x_{t-1}^2)
        denom = float(np.dot(x[:-1], x[:-1]))
        if denom == 0:
            phi = 0.0
        else:
            phi = float(np.dot(x[1:], x[:-1]) / denom)
        forecast = phi * x[-1]
        # previsão acumulada = soma(x) + phi * x_t
        return float(x.sum() + forecast)

    @staticmethod
    def _forecast_drawdown(series: np.ndarray) -> float:
        """Prevê o drawdown mínimo incluindo um passo adicional."""
        # Inclui o incremento previsto antes de calcular o drawdown mínimo
        forecast_increment = RetroSelector._arima_forecast(series) - series.sum()
        extended = np.append(series, forecast_increment)
        cumulative = np.cumsum(extended)
        running_max = np.maximum.accumulate(cumulative)
        drawdowns = cumulative - running_max
        return float(drawdowns.min())

    @staticmethod
    def _kalman_update(
        prior: float,
        measurement: float,
        measurement_variance: float = 1.0,
        initial_variance: float = 1.0,
    ) -> float:
        """Atualiza valor utilizando um filtro de Kalman unidimensional.

        O ganho do filtro é calculado como ``K = P_prior / (P_prior + R)`` onde
        ``P_prior`` é ``initial_variance`` e ``R`` é ``measurement_variance``.
        O valor retornado corresponde a ``prior + K * (measurement - prior)``.
        """

        # K = P_prior / (P_prior + R)
        k = initial_variance / (initial_variance + measurement_variance)
        # posterior = prior + K * (measurement - prior)
        return prior + k * (measurement - prior)
