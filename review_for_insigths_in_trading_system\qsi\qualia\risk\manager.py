"""
Módulo de gerenciamento de risco para o sistema QUALIA Trading.

Este módulo fornece classes e funções para controlar o risco das operações de trading,
incluindo gerenciamento de posições, controle de drawdown e alocação de capital.
"""

from __future__ import annotations

import math
from collections import deque
from datetime import datetime, timedelta, timezone
from typing import Any, Deque, Dict, Iterator, List, Optional, Tuple, Union
import time

from dataclasses import asdict
import numpy as np
from ..common_types import Position
from ..memory.event_bus import SimpleEventBus
from ..events import RiskUpdateEvent, RiskRecalibratedEvent
from ..config.risk_profiles import (
    RiskProfileSettings,
    _FALLBACK_RISK_PROFILE_SETTINGS,
)

from ..risk_management.risk_manager_base import QUALIARiskManagerBase

from ..utils.logger import get_logger
from ..core.qualia_logger import log_event
from threading import Lock
from contextlib import contextmanager
from datadog import DogStatsd
from ..config.settings import get_env
from ..utils.numba_utils import optional_njit
import ccxt

# Configurar logging
logger = get_logger(__name__)


@optional_njit(cache=True)
def _compute_quantity_njit(
    capital: float, risk_pct: float, stop_points: float, lot_value: float
) -> float:
    risk_amount = capital * (risk_pct / 100.0)
    return risk_amount / (stop_points * lot_value)


def validate_percent(value: Optional[float]) -> Optional[float]:
    """Normalize a numeric value expected to represent a percentage.

    This helper interprets small fractional numbers as percentages while
    safeguarding against invalid ranges. Values below or equal to ``0.05`` are
    assumed to be fractional representations of a percentage (e.g. ``0.01``
    means ``1%``)
    and are scaled accordingly. Numbers between ``1`` and ``100`` are already
    percentages and therefore returned unchanged. Inputs greater than ``100`` or
    negative are considered invalid.

    Parameters
    ----------
    value : Optional[float]
        Value to validate. ``None`` is returned unchanged.

    Returns
    -------
    Optional[float]
        Normalized percentage or ``None`` when ``value`` is ``None``.
    """

    if value is None:
        return None

    if value < 0:
        raise ValueError("valor percentual não pode ser negativo")

    if value <= 0.05:
        return value * 100

    if 1 <= value <= 100:
        return value

    if value > 100:
        raise ValueError("valor percentual não pode exceder 100")

    return value


HISTORY_MAXLEN = 1000

# Cache for fetched minimum lot sizes and the timestamp of retrieval
_MIN_LOT_SIZE_CACHE: Dict[Tuple[str, str], Tuple[Optional[float], float]] = {}
_MIN_LOT_SIZE_CACHE_LOCK = Lock()

# Time-to-live for minimum lot cache entries in seconds.
# Default is 6 hours. Value can be overridden via ``QUALIA_MIN_LOT_TTL``.
MIN_LOT_TTL = float(
    get_env("QUALIA_MIN_LOT_TTL", str(6 * 60 * 60), warn=False) or 6 * 60 * 60
)

# Risk profile defaults are defined in ``src.qualia.config.risk_profiles``.


@contextmanager
def _closing_if_possible(obj: Any) -> Iterator[Any]:
    """Context manager that calls ``close`` on *obj* if available."""
    try:
        yield obj
    finally:
        if hasattr(obj, "close"):
            try:
                obj.close()
            except Exception as close_exc:  # pragma: no cover - close errors
                logger.debug("Erro ao fechar conexao da exchange: %s", close_exc)


def clear_min_lot_size_cache() -> None:
    """Remove all entries from the minimum lot size cache."""
    with _MIN_LOT_SIZE_CACHE_LOCK:
        _MIN_LOT_SIZE_CACHE.clear()


def purge_expired_min_lot_entries() -> None:
    """Remove expired entries from the minimum lot size cache."""
    now = time.time()
    with _MIN_LOT_SIZE_CACHE_LOCK:
        expired_keys = [
            key
            for key, (_, timestamp) in _MIN_LOT_SIZE_CACHE.items()
            if now - timestamp >= MIN_LOT_TTL
        ]
        for key in expired_keys:
            _MIN_LOT_SIZE_CACHE.pop(key, None)


def get_risk_profile_settings(
    profile: str, profiles_cfg: Optional[Dict[str, Dict[str, Any]]] = None
) -> RiskProfileSettings:
    """Return risk settings for ``profile`` as a :class:`RiskProfileSettings`.

    Parameters
    ----------
    profile:
        Name of the desired risk profile.
    profiles_cfg:
        Mapping loaded from JSON with all profile settings. When provided,
        its values take precedence over built-in defaults.

    Returns
    -------
    RiskProfileSettings
        Dataclass instance with the parameters for the requested profile.
    """

    # Campos válidos do RiskProfileSettings
    valid_fields = {
        "max_drawdown_pct",
        "risk_per_trade_pct",
        "max_position_size_pct",
        "mass_based_size_pct",
        "max_daily_loss_pct",
        "max_open_positions",
        "cooling_period_minutes",
        "stop_loss_adjustment",
        "min_lot_size",
        "position_sizing_mode",
        "max_position_percentage",
        "stop_loss_percentage",
        "take_profit_percentage",
        "enable_trailing_stop",
        "enable_dynamic_position_sizing",
        "quantum_sensitivity_boost",
    }

    if profiles_cfg:
        if profile in profiles_cfg:
            # Filtrar apenas campos válidos
            filtered_config = {
                k: v for k, v in profiles_cfg[profile].items() if k in valid_fields
            }
            return RiskProfileSettings(**filtered_config)
        if "default" in profiles_cfg:
            # Filtrar apenas campos válidos
            filtered_config = {
                k: v for k, v in profiles_cfg["default"].items() if k in valid_fields
            }
            return RiskProfileSettings(**filtered_config)

    return _FALLBACK_RISK_PROFILE_SETTINGS.get(
        profile, _FALLBACK_RISK_PROFILE_SETTINGS["balanced"]
    )


def fetch_exchange_min_lot_size(exchange_id: str, symbol: str) -> Optional[float]:
    """Fetch the minimum lot size required by the exchange for a symbol.

    Parameters
    ----------
    exchange_id : str
        Identifier understood by ``ccxt`` (e.g., ``"kraken"``).
    symbol : str
        Trading pair, such as ``"BTC/USD"``.

    Returns
    -------
    Optional[float]
        Minimum tradable amount or ``None`` if it cannot be determined.
    """

    cache_key = (exchange_id, symbol)
    now = time.time()
    purge_expired_min_lot_entries()
    with _MIN_LOT_SIZE_CACHE_LOCK:
        cached = _MIN_LOT_SIZE_CACHE.get(cache_key)
        if cached and now - cached[1] < MIN_LOT_TTL:
            return cached[0]
        if cached:
            _MIN_LOT_SIZE_CACHE.pop(cache_key, None)

    exchange_cls = getattr(ccxt, exchange_id)
    try:
        with _closing_if_possible(exchange_cls()) as exchange:
            exchange.load_markets()
            market = exchange.market(symbol)
            result = market.get("limits", {}).get("amount", {}).get("min")
            with _MIN_LOT_SIZE_CACHE_LOCK:
                _MIN_LOT_SIZE_CACHE[cache_key] = (result, time.time())
            return result
    except Exception as exc:  # pragma: no cover - network errors
        logger.warning(
            "Nao foi possivel obter min_lot_size da %s para %s: %s",
            exchange_id,
            symbol,
            exc,
        )
        with _MIN_LOT_SIZE_CACHE_LOCK:
            _MIN_LOT_SIZE_CACHE.pop(cache_key, None)
        return None


def load_profile_config_from_json(
    profile_specific_config: Optional[Union[Dict[str, Any], RiskProfileSettings]],
) -> Dict[str, Any]:
    """Return profile configuration as a dictionary.

    Parameters
    ----------
    profile_specific_config:
        Mapping loaded from JSON or a :class:`RiskProfileSettings` instance.

    Returns
    -------
    Dict[str, Any]
        Dictionary with the provided configuration or empty when ``None``.

    Raises
    ------
    TypeError
        When ``profile_specific_config`` is not a dict or ``RiskProfileSettings``.
    """

    if profile_specific_config is None:
        return {}
    if isinstance(profile_specific_config, RiskProfileSettings):
        return asdict(profile_specific_config)
    if isinstance(profile_specific_config, dict):
        return profile_specific_config
    raise TypeError("profile_specific_config deve ser dict ou RiskProfileSettings")


def normalize_and_validate_percentages(risk_settings: Dict[str, Any]) -> None:
    """Normalize and validate percentage fields in ``risk_settings``.

    Parameters
    ----------
    risk_settings:
        Configuration dictionary modified in place.

    Raises
    ------
    ValueError
        For invalid percentage values or inconsistent settings.
    """

    percent_keys = [
        "risk_per_trade_pct",
        "max_position_size_pct",
        "mass_based_size_pct",
        "max_drawdown_pct",
        "max_daily_loss_pct",
    ]
    for key in percent_keys:
        if key in risk_settings and risk_settings[key] is not None:
            risk_settings[key] = validate_percent(float(risk_settings[key]))

    if (
        risk_settings.get("risk_per_trade_pct") is not None
        and risk_settings.get("max_position_size_pct") is not None
        and risk_settings["risk_per_trade_pct"] > risk_settings["max_position_size_pct"]
    ):
        raise ValueError("risk_per_trade_pct não pode exceder max_position_size_pct")

    risk_per_trade = risk_settings.get("risk_per_trade_pct")
    if risk_per_trade is not None and risk_per_trade > 10:
        raise ValueError("risk_per_trade_pct irrealista")


def _validate_and_fill_defaults(
    risk_settings: Dict[str, Any], risk_profile: str
) -> None:
    """Validate ``risk_settings`` and apply default values.

    Parameters
    ----------
    risk_settings:
        Dictionary with risk parameters to validate. This mapping is mutated in
        place.
    risk_profile:
        Name of the profile being processed, used only for logging purposes.
    """

    normalize_and_validate_percentages(risk_settings)

    param_validations = {
        "max_drawdown_pct": (
            lambda x: isinstance(x, (float, int)) and 0 < x <= 100,
            "deve ser float/int > 0 e <= 100",
        ),
        "max_position_size_pct": (
            lambda x: isinstance(x, (float, int)) and 0 < x <= 100,
            "deve ser float/int > 0 e <= 100",
        ),
        "risk_per_trade_pct": (
            lambda x: isinstance(x, (float, int)) and 0 < x <= 100,
            "deve ser float/int > 0 e <= 100",
        ),
        "max_daily_loss_pct": (
            lambda x: isinstance(x, (float, int)) and 0 < x <= 100,
            "deve ser float/int > 0 e <= 100",
        ),
        "mass_based_size_pct": (
            lambda x: isinstance(x, (float, int)) and 0 < x <= 100,
            "deve ser float/int > 0 e <= 100",
        ),
        "max_open_positions": (
            lambda x: isinstance(x, int) and x > 0,
            "deve ser int > 0",
        ),
        # Campos opcionais relacionados à recuperação após perdas
        "cooling_period_minutes": (
            lambda x: isinstance(x, int) and x >= 0,
            "deve ser int >= 0",
        ),
        "stop_loss_adjustment": (
            lambda x: isinstance(x, (float, int)) and x > 0,
            "deve ser float/int > 0",
        ),
        "min_lot_size": (
            lambda x: isinstance(x, (float, int)) and x >= 0,
            "deve ser float/int >= 0",
        ),
    }

    optional_params = {
        "cooling_period_minutes",
        "stop_loss_adjustment",
        "min_lot_size",
        "mass_based_size_pct",
    }

    for param, (check, msg) in param_validations.items():
        value = risk_settings.get(param)
        if value is None:
            if param not in optional_params:
                logger.warning(
                    "Parâmetro obrigatório '%s' não encontrado no JSON.", param
                )
            continue

        if (
            param in ("max_open_positions", "cooling_period_minutes")
            and isinstance(value, float)
            and value == int(value)
        ):
            value = int(value)

        if not check(value):
            error_msg = f"Parâmetro de risco inválido: '{param}' ({value}) {msg}."
            logger.error(error_msg)
            raise ValueError(error_msg)

    if risk_settings.get("max_open_positions") is not None:
        risk_settings["max_open_positions"] = int(risk_settings["max_open_positions"])

    if risk_settings.get("cooling_period_minutes") is not None:
        risk_settings["cooling_period_minutes"] = int(
            risk_settings["cooling_period_minutes"]
        )

    if risk_settings.get("mass_based_size_pct") is None:
        risk_settings["mass_based_size_pct"] = risk_settings.get(
            "max_position_size_pct"
        )
        logger.info(
            "mass_based_size_pct nao definido; usando max_position_size_pct=%s",
            risk_settings["mass_based_size_pct"],
        )

    logger.debug("risk_settings apos validacao: %s", risk_settings)
    logger.info("Validação de risk_settings concluída com sucesso.")
    logger.info(
        "RISK_MANAGER create_risk_manager: Configurações de risco finais (risk_settings) a serem usadas para perfil '%s': %s",
        risk_profile,
        risk_settings,
    )
    logger.info(
        "  >> DENTRO DE RISK_SETTINGS, risk_per_trade_pct RESULTANTE: %s%%",
        risk_settings.get("risk_per_trade_pct"),
    )


def build_risk_manager_instance(
    initial_capital: float,
    risk_profile: str,
    risk_settings: Dict[str, Any],
    min_lot_size: Optional[float] = None,
    exchange_id: Optional[str] = None,
    symbol: Optional[str] = None,
    *,
    event_bus: Optional[SimpleEventBus] = None,
) -> QUALIARiskManager:
    """Instantiate :class:`QUALIARiskManager` with resolved parameters."""

    if not isinstance(risk_settings, dict):
        raise TypeError("risk_settings deve ser um dict")

    final_min_lot = min_lot_size or risk_settings.get("min_lot_size")
    if final_min_lot is None and exchange_id and symbol:
        fetched = fetch_exchange_min_lot_size(exchange_id, symbol)
        if fetched is not None:
            final_min_lot = fetched
            logger.info(
                "min_lot_size obtido da exchange %s para %s: %s",
                exchange_id,
                symbol,
                fetched,
            )
    risk_settings["min_lot_size"] = final_min_lot

    # Filtrar apenas parâmetros aceitos pelo QUALIARiskManager
    valid_params = {
        "max_drawdown_pct",
        "max_position_size_pct",
        "max_open_positions",
        "risk_per_trade_pct",
        "max_daily_loss_pct",
        "max_var_pct",
        "cooling_period_minutes",
        "stop_loss_adjustment",
        "min_lot_size",
        "min_lot_check_threshold",
        "mass_based_size_pct",
    }

    filtered_settings = {k: v for k, v in risk_settings.items() if k in valid_params}

    return QUALIARiskManager(
        initial_capital=initial_capital,
        risk_profile=risk_profile,
        event_bus=event_bus,
        **filtered_settings,
    )


def _load_profile_defaults(
    risk_profile: str,
    profile_specific_config: Optional[Union[Dict[str, Any], RiskProfileSettings]],
) -> Tuple[Dict[str, Any], Dict[str, Any]]:
    """Return base risk settings and profile dict for ``risk_profile``."""

    profile_dict = load_profile_config_from_json(profile_specific_config)
    profiles_cfg = {risk_profile: profile_dict} if profile_dict else None
    defaults_dc = get_risk_profile_settings(risk_profile, profiles_cfg)
    return asdict(defaults_dc), profile_dict


def _apply_profile_dict_settings(
    risk_profile: str, risk_settings: Dict[str, Any], profile_dict: Dict[str, Any]
) -> None:
    """Update ``risk_settings`` with values from ``profile_dict``."""

    for key, value in profile_dict.items():
        if value is None:
            risk_settings[key] = None
        elif key == "risk_per_trade_pct":
            risk_settings["risk_per_trade_pct"] = validate_percent(float(value))
        elif key == "max_position_size_pct":
            risk_settings["max_position_size_pct"] = validate_percent(float(value))
        elif key == "min_lot_size":
            risk_settings["min_lot_size"] = float(value)
        elif isinstance(value, (int, float)):
            risk_settings[key] = float(value)
        elif (
            isinstance(value, str)
            and value.replace(".", "", 1).replace("-", "", 1).isdigit()
        ):
            risk_settings[key] = float(value)
        elif isinstance(value, bool):
            risk_settings[key] = value
        else:
            risk_settings[key] = value
        logger.debug(f"Perfil '{risk_profile}': Aplicado '{key}' = {value} do JSON.")


def _apply_cli_overrides(
    risk_profile: str,
    risk_settings: Dict[str, Any],
    custom_risk_per_trade_pct: Optional[float],
    custom_max_position_capital_pct: Optional[float],
) -> None:
    """Override settings with command line parameters when provided."""

    if custom_risk_per_trade_pct is not None:
        risk_settings["risk_per_trade_pct"] = validate_percent(
            custom_risk_per_trade_pct
        )
        logger.debug(
            f"Perfil de risco '{risk_profile}' atualizado com risk_per_trade_pct customizado via CLI: {risk_settings['risk_per_trade_pct']:.2f}%"
        )
    if custom_max_position_capital_pct is not None:
        risk_settings["max_position_size_pct"] = validate_percent(
            custom_max_position_capital_pct
        )
        logger.debug(
            f"Perfil de risco '{risk_profile}' atualizado com max_position_size_pct customizado via CLI: {risk_settings['max_position_size_pct']:.2f}%"
        )


def _merge_json_cli_params(
    risk_profile: str,
    risk_settings: Dict[str, Any],
    profile_dict: Dict[str, Any],
    custom_risk_per_trade_pct: Optional[float],
    custom_max_position_capital_pct: Optional[float],
) -> None:
    """Merge JSON and CLI parameters into ``risk_settings``."""

    if risk_profile not in ["custom", "custom_base"]:
        if profile_dict:
            _apply_profile_dict_settings(risk_profile, risk_settings, profile_dict)

        _apply_cli_overrides(
            risk_profile,
            risk_settings,
            custom_risk_per_trade_pct,
            custom_max_position_capital_pct,
        )

    elif risk_profile == "custom_base":
        if profile_dict:
            for key, value in profile_dict.items():
                try:
                    param_key = (
                        "risk_per_trade_pct"
                        if key == "risk_per_trade_pct_fixed"
                        else key
                    )
                    if isinstance(value, (int, float)):
                        if param_key in ("risk_per_trade_pct", "max_position_size_pct"):
                            risk_settings[param_key] = validate_percent(float(value))
                        else:
                            risk_settings[param_key] = float(value)
                        logger.debug(
                            f"Perfil 'custom_base': Aplicado '{param_key}' = {float(value)} do JSON."
                        )
                    elif isinstance(value, str) and value.replace(".", "", 1).isdigit():
                        val = float(value)
                        if param_key in ("risk_per_trade_pct", "max_position_size_pct"):
                            risk_settings[param_key] = validate_percent(val)
                        else:
                            risk_settings[param_key] = val
                        logger.debug(
                            f"Perfil 'custom_base': Aplicado '{param_key}' = {float(value)} (convertido de string) do JSON."
                        )
                    else:
                        risk_settings[param_key] = value
                        logger.debug(
                            f"Perfil 'custom_base': Aplicado '{param_key}' = '{value}' (não-numérico) do JSON."
                        )
                except ValueError:
                    logger.warning(
                        f"Valor inválido '{value}' para '{key}' no JSON do perfil 'custom_base'. Ignorando esta chave."
                    )
        else:
            logger.warning(
                "Perfil 'custom_base' selecionado, mas profile_specific_config não fornecido."
            )

        _apply_cli_overrides(
            risk_profile,
            risk_settings,
            custom_risk_per_trade_pct,
            custom_max_position_capital_pct,
        )

    elif risk_profile == "custom":
        _apply_cli_overrides(
            risk_profile,
            risk_settings,
            custom_risk_per_trade_pct,
            custom_max_position_capital_pct,
        )

    else:
        logger.warning(
            f"Perfil de risco '{risk_profile}' desconhecido. Nenhuma configuração será aplicada."
        )


def _finalize_risk_settings(risk_profile: str, risk_settings: Dict[str, Any]) -> None:
    """Run final validation and fill default values."""

    _validate_and_fill_defaults(risk_settings, risk_profile)


def create_risk_manager(
    initial_capital: float,
    risk_profile: str,
    custom_risk_per_trade_pct: Optional[float] = None,
    custom_max_position_capital_pct: Optional[float] = None,
    profile_specific_config: Optional[
        Union[Dict[str, Any], RiskProfileSettings]
    ] = None,
    min_lot_size: Optional[float] = None,
    exchange_id: Optional[str] = None,
    symbol: Optional[str] = None,
    *,
    event_bus: Optional[SimpleEventBus] = None,
) -> QUALIARiskManagerBase:
    """
    Cria uma instância de QUALIARiskManager com base no perfil de risco.

    Args:
        initial_capital: Capital inicial
        risk_profile: Perfil de risco ('conservative', 'balanced', 'aggressive', 'custom', 'custom_base')
        custom_risk_per_trade_pct: Porcentagem do capital por operação (ex.: ``0.8`` para ``0.8%``)
        custom_max_position_capital_pct: Tamanho máximo de posição em % do capital (opcional)
        profile_specific_config: Configurações específicas do perfil carregadas
            do JSON ou instância de :class:`RiskProfileSettings` (opcional)
        min_lot_size: Tamanho mínimo de lote permitido pela exchange (opcional)
        exchange_id: Identificador da exchange para validação automática (opcional)
        symbol: Par de trading para validação (opcional)

    Note:
        Os valores no JSON devem ser informados em porcentagem. Por exemplo,
        ``0.8`` representa ``0.8%``. Valores de ``risk_per_trade_pct`` acima de
        ``10%`` são considerados inseguros e resultarão em ``ValueError``.

    Returns:
        Instância configurada de QUALIARiskManagerBase
    """
    logger.info(
        f"RISK_MANAGER create_risk_manager: Chamado com initial_capital={initial_capital}, risk_profile='{risk_profile}', "
        f"custom_risk_per_trade_pct={custom_risk_per_trade_pct}, custom_max_position_capital_pct={custom_max_position_capital_pct}"
    )
    logger.info(
        f"RISK_MANAGER create_risk_manager: profile_specific_config recebido: {profile_specific_config}"
    )

    available_profiles = set(_FALLBACK_RISK_PROFILE_SETTINGS) | {
        "custom",
        "custom_base",
    }
    if risk_profile not in available_profiles:
        raise ValueError(f"Perfil de risco inexistente: {risk_profile}")

    risk_settings, profile_dict = _load_profile_defaults(
        risk_profile, profile_specific_config
    )

    _merge_json_cli_params(
        risk_profile,
        risk_settings,
        profile_dict,
        custom_risk_per_trade_pct,
        custom_max_position_capital_pct,
    )

    _finalize_risk_settings(risk_profile, risk_settings)

    return build_risk_manager_instance(
        initial_capital=initial_capital,
        risk_profile=risk_profile,
        risk_settings=risk_settings,
        min_lot_size=min_lot_size,
        exchange_id=exchange_id,
        symbol=symbol,
        event_bus=event_bus,
    )


class QUALIARiskManager(QUALIARiskManagerBase):
    """
    Gerenciador de risco para o sistema QUALIA Trading.

    Responsável por controlar a exposição ao risco, gerenciar o tamanho das posições,
    monitorar o drawdown e aplicar regras de proteção de capital.
    """

    def __init__(
        self,
        initial_capital: float,
        risk_profile: str = "balanced",
        max_drawdown_pct: Optional[float] = None,
        max_position_size_pct: Optional[float] = None,
        max_open_positions: Optional[int] = None,
        risk_per_trade_pct: Optional[float] = None,
        max_daily_loss_pct: Optional[float] = None,
        max_var_pct: Optional[float] = None,
        cooling_period_minutes: Optional[int] = None,
        stop_loss_adjustment: Optional[float] = None,
        min_lot_size: Optional[float] = None,
        min_lot_check_threshold: Optional[float] = None,
        mass_based_size_pct: Optional[float] = None,
        drawdown_smoothing_alpha: float = 0.2,
        statsd_client: Optional[DogStatsd] = None,
        event_bus: Optional[SimpleEventBus] = None,
    ):
        self.asset_balances = {}  # Asset balances in base currency
        self.statsd = statsd_client or DogStatsd()
        self.drawdown_smoothing_alpha = drawdown_smoothing_alpha
        """
        Inicializa o gerenciador de risco.

        Args:
            initial_capital: Capital inicial em USD
            risk_profile: Perfil de risco ('conservative', 'balanced', 'aggressive', 'custom')
            max_drawdown_pct: Drawdown máximo permitido em % (opcional)
            max_position_size_pct: Tamanho máximo de posição em % do capital (opcional)
            max_open_positions: Número máximo de posições simultâneas (opcional)
            risk_per_trade_pct: Porcentagem do capital por operação (ex.: ``0.8`` representa ``0.8%``)
            max_daily_loss_pct: Limite máximo de perda diária em % (opcional)
            max_var_pct: Limite de VaR diário em % do capital (opcional)
            cooling_period_minutes: Período de resfriamento em minutos após perda significativa (opcional)
            stop_loss_adjustment: Fator de ajuste após série de perdas (opcional)
            min_lot_size: Tamanho mínimo de lote da exchange (opcional)
            min_lot_check_threshold: Valor de ``risk_per_trade_pct`` abaixo do
                qual o lote mínimo é verificado (opcional, padrão ``5.0``)
            mass_based_size_pct: Percentual usado para dimensionamento de
                posições baseado em massa informacional. Quando ``None``, o
                valor de ``max_position_size_pct`` é utilizado.
            drawdown_smoothing_alpha: Fator de suavização exponencial para o
                drawdown. Valores mais altos reagem mais rápido.
            statsd_client: Instância opcional de ``DogStatsd`` para envio de
                métricas de risco.
            event_bus: Barramento de eventos para comunicação assíncrona.
        """
        super().__init__(initial_capital, risk_profile, event_bus=event_bus)

        # Definir parâmetros baseados no perfil de risco (se não especificados)
        if risk_profile == "conservative":
            self.max_drawdown_pct = max_drawdown_pct or 5.0  # 5%
            self.max_position_size_pct = max_position_size_pct or 3.0  # 3%
            self.max_open_positions = max_open_positions or 3
            self.risk_per_trade_pct = risk_per_trade_pct or 2.0  # 2%
            self.risk_reward_ratio = 2.0  # 1:2
            self.max_daily_loss_pct = max_daily_loss_pct or 2.0  # 2%
            # 1h após perda significativa
            self.cooling_period_minutes = cooling_period_minutes or 60
            # Ajuste após série de perdas
            self.stop_loss_adjustment = stop_loss_adjustment or 0.8
        elif risk_profile == "aggressive":
            self.max_drawdown_pct = max_drawdown_pct or 15.0  # 15%
            self.max_position_size_pct = max_position_size_pct or 10.0  # 10%
            self.max_open_positions = max_open_positions or 8
            # Valor alinhado ao JSON (0.012 -> 1.2%)
            self.risk_per_trade_pct = risk_per_trade_pct or 1.2  # 1.2%
            self.risk_reward_ratio = 1.2  # 1:1.2
            self.max_daily_loss_pct = max_daily_loss_pct or 6.0  # 6%
            # 15min após perda significativa
            self.cooling_period_minutes = cooling_period_minutes or 15
            # Sem ajuste após série de perdas
            self.stop_loss_adjustment = stop_loss_adjustment or 1.0
        elif risk_profile == "custom":
            # Para 'custom', os valores de **risk_settings de create_risk_manager devem prevalecer.
            # Os kwargs aqui (max_drawdown_pct, etc.) já contêm os valores
            # corretos.
            self.max_drawdown_pct = max_drawdown_pct
            self.max_position_size_pct = max_position_size_pct
            self.max_open_positions = max_open_positions
            # Este já é o valor correto (ex: 2.1%)
            self.risk_per_trade_pct = risk_per_trade_pct
            # Revertendo para um default fixo para 'custom', pois não é passado
            # via kwargs atualmente.
            self.risk_reward_ratio = 1.5
            self.max_daily_loss_pct = max_daily_loss_pct
            self.cooling_period_minutes = cooling_period_minutes
            self.stop_loss_adjustment = stop_loss_adjustment
        else:  # balanced (default)
            self.max_drawdown_pct = max_drawdown_pct or 10.0  # 10%
            self.max_position_size_pct = max_position_size_pct or 5.0  # 5%
            self.max_open_positions = max_open_positions or 5
            self.risk_per_trade_pct = risk_per_trade_pct or 3.0  # 3%
            self.risk_reward_ratio = 1.5  # 1:1.5
            self.max_daily_loss_pct = max_daily_loss_pct or 4.0  # 4%
            # 30min após perda significativa
            self.cooling_period_minutes = cooling_period_minutes or 30
            # Ajuste após série de perdas
            self.stop_loss_adjustment = stop_loss_adjustment or 0.9

        self.min_lot_size = min_lot_size
        self.min_lot_check_threshold = (
            5.0 if min_lot_check_threshold is None else min_lot_check_threshold
        )
        self.mass_based_size_pct = (
            self.max_position_size_pct
            if mass_based_size_pct is None
            else mass_based_size_pct
        )
        self.max_var_pct = 12.0 if max_var_pct is None else max_var_pct

        # Valores base para restaurar apos modo conservador
        self._base_risk_per_trade_pct = self.risk_per_trade_pct
        self._base_max_position_size_pct = self.max_position_size_pct
        self.conservative_mode = False

        # Estado interno
        self.peak_capital = initial_capital
        self.current_drawdown_pct = 0.0
        self.daily_pnl = 0.0
        self.daily_reset_time = None
        self.last_loss_time = None
        self.consecutive_losses = 0

        # Histórico (mantém apenas os últimos HISTORY_MAXLEN registros)
        self.capital_history: Deque[Dict[str, Any]] = deque(maxlen=HISTORY_MAXLEN)
        self.trade_history: Deque[Dict[str, Any]] = deque(maxlen=HISTORY_MAXLEN)
        self.drawdown_history: Deque[Dict[str, Any]] = deque(maxlen=HISTORY_MAXLEN)
        self.smoothed_drawdown_pct = 0.0
        self.position_sizing_history: Deque[Dict[str, Any]] = deque(
            maxlen=HISTORY_MAXLEN
        )

        # Posições abertas gerenciadas (suporta múltiplas por símbolo)
        self.open_positions: Dict[str, List[Position]] = {}
        # Estresse de mercado (0-1)
        self.market_stress: float = 0.0

        # Registrar estado inicial
        self._log_capital_state()
        logger.info(
            (
                "QUALIARiskManager inicializado (%s) com parametros: "
                "initial_capital=%s, current_capital=%s, risk_per_trade_pct=%s, "
                "max_drawdown_pct=%s, max_position_size_pct=%s, max_open_positions=%s, "
                "max_daily_loss_pct=%s, cooling_period_minutes=%s, "
                "stop_loss_adjustment=%s, min_lot_size=%s, min_lot_check_threshold=%s, "
                "mass_based_size_pct=%s"
            ),
            self.risk_profile,
            self.initial_capital,
            self.current_capital,
            self.risk_per_trade_pct,
            self.max_drawdown_pct,
            self.max_position_size_pct,
            self.max_open_positions,
            self.max_daily_loss_pct,
            self.cooling_period_minutes,
            self.stop_loss_adjustment,
            self.min_lot_size,
            self.min_lot_check_threshold,
            self.mass_based_size_pct,
        )

    def _log_capital_state(self) -> None:
        """
        Registra o estado atual do capital no histórico.
        """
        timestamp = datetime.now(timezone.utc)
        self.capital_history.append(
            {
                "timestamp": timestamp,
                "capital": self.current_capital,
                "drawdown_pct": self.current_drawdown_pct,
            }
        )
        self.drawdown_history.append(
            {"timestamp": timestamp, "drawdown_pct": self.current_drawdown_pct}
        )
        alpha = self.drawdown_smoothing_alpha
        self.smoothed_drawdown_pct = (
            self.smoothed_drawdown_pct * (1 - alpha) + self.current_drawdown_pct * alpha
        )

    def _daily_loss_pct(self) -> float:
        """Return the current daily loss percentage."""
        if self.current_capital <= 0:
            return 0.0
        return (self.daily_pnl / self.current_capital) * 100

    def set_conservative(self, enable: bool = True) -> None:
        """Toggle conservative mode adjusting risk parameters.

        Parameters
        ----------
        enable : bool, default True
            When ``True`` the manager operates with reduced risk, otherwise it
            restores the original configuration.
        """

        self.conservative_mode = bool(enable)
        if self.conservative_mode:
            self.risk_per_trade_pct = self._base_risk_per_trade_pct * 0.5
            self.max_position_size_pct = self._base_max_position_size_pct * 0.5
        else:
            self.risk_per_trade_pct = self._base_risk_per_trade_pct
            self.max_position_size_pct = self._base_max_position_size_pct

    def update_from_metacognition(self, metacog_confidence: float) -> None:
        """Adjust conservative mode based on metacognitive confidence."""

        if metacog_confidence < 0.3:
            self.set_conservative(True)
        else:
            self.set_conservative(False)

    def _compute_var_pct(self, volatility: Optional[float]) -> float:
        """Return a simplified VaR percentage based on volatility."""
        if volatility is None:
            return self.risk_per_trade_pct * 1.65
        return volatility * 1.65

    def _calculate_dynamic_stop_loss(
        self,
        symbol: str,
        current_price: float,
        volatility: Optional[float],
        confidence: float,
    ) -> float:
        """Calcula stop loss baseado em volatilidade e confidence.

        Este método implementa um stop loss adaptativo que:
        - Usa volatilidade para determinar a distância base do stop
        - Ajusta baseado na confidence: maior confidence = stop mais próximo
        - Aplica limites mínimos e máximos para segurança

        Parameters
        ----------
        symbol
            Símbolo do ativo para logging
        current_price
            Preço atual do ativo
        volatility
            Volatilidade medida do ativo (pode ser None)
        confidence
            Nível de confiança da estratégia (0.0 a 1.0)

        Returns
        -------
        float
            Preço do stop loss calculado
        """
        if volatility is None or volatility == 0:
            volatility = 0.02  # 2% padrão
            logger.debug(f"Usando volatilidade padrão 2% para {symbol}")

        # Stop loss adaptativo: mais apertado com maior confidence
        base_stop_pct = volatility * 2.0
        confidence_factor = max(0.5, confidence)  # Mínimo 50%
        adjusted_stop_pct = base_stop_pct / confidence_factor

        # Aplicar limites de segurança
        min_stop_pct = 0.005  # Mínimo 0.5%
        max_stop_pct = 0.10  # Máximo 10%
        adjusted_stop_pct = max(min_stop_pct, min(max_stop_pct, adjusted_stop_pct))

        stop_loss_price = current_price * (1 - adjusted_stop_pct)

        logger.debug(
            f"Stop loss dinâmico para {symbol}: preço={current_price:.4f}, "
            f"volatilidade={volatility:.3f}, confidence={confidence:.3f}, "
            f"stop_pct={adjusted_stop_pct:.3f}, stop_price={stop_loss_price:.4f}"
        )

        return stop_loss_price

    def compute_position_size(
        self,
        capital: float,
        risk_per_trade_pct: float,
        stop_loss_points: float,
        lot_value: float,
        min_lot_size: Optional[float] = None,
    ) -> float:
        """Calcula a quantidade de unidades baseada no risco.

        Parameters
        ----------
        capital : float
            Capital disponível para a operação.
        risk_per_trade_pct : float
            Porcentagem do capital a ser arriscada por trade (``1.2`` representa
            ``1.2%``).
        stop_loss_points : float
            Distância em pontos até o stop loss.
        lot_value : float
            Valor monetário de um ponto para uma unidade.
        min_lot_size : Optional[float], optional
            Lote mínimo exigido pela exchange.

        Returns
        -------
        float
            Quantidade calculada ou ``0.0`` quando inválida ou abaixo do mínimo.
        """

        if (
            capital <= 0
            or risk_per_trade_pct <= 0
            or stop_loss_points <= 0
            or lot_value <= 0
        ):
            logger.debug("compute_position_size: valores invalidos")
            return 0.0

        quantity = _compute_quantity_njit(
            capital, risk_per_trade_pct, stop_loss_points, lot_value
        )

        if min_lot_size is not None and quantity < min_lot_size:
            logger.debug(
                "compute_position_size: quantidade %.6f abaixo do min_lot_size %.6f",
                quantity,
                min_lot_size,
            )
            return 0.0

        return quantity

    def compute_position_size_batch(
        self,
        capital: float,
        risk_per_trade_pct: float,
        stop_loss_points: np.ndarray,
        lot_value: float,
        min_lot_size: Optional[float] = None,
    ) -> np.ndarray:
        """Return quantities for multiple stop distances using NumPy.

        Parameters
        ----------
        capital : float
            Available capital.
        risk_per_trade_pct : float
            Capital percentage to risk per trade.
        stop_loss_points : ndarray
            Distances in points for each trade's stop loss.
        lot_value : float
            Monetary value of one point.
        min_lot_size : Optional[float], optional
            Minimum tradable amount.

        Returns
        -------
        ndarray
            Calculated quantities for each stop distance.
        """
        points = np.asarray(stop_loss_points, dtype=float)
        mask = (capital > 0) & (risk_per_trade_pct > 0) & (points > 0) & (lot_value > 0)
        quantities = np.zeros_like(points, dtype=float)
        if not mask.any():
            return quantities
        risk_amount = capital * (risk_per_trade_pct / 100.0)
        quantities[mask] = risk_amount / (points[mask] * lot_value)
        if min_lot_size is not None:
            quantities = np.where(quantities >= min_lot_size, quantities, 0.0)
        return quantities

    def compute_position_size_batch_loop(
        self,
        capital: float,
        risk_per_trade_pct: float,
        stop_loss_points: np.ndarray,
        lot_value: float,
        min_lot_size: Optional[float] = None,
    ) -> np.ndarray:
        """Loop-based batch position sizing for benchmarking."""
        result = np.empty_like(stop_loss_points, dtype=float)
        for i, pts in enumerate(stop_loss_points):
            result[i] = self.compute_position_size(
                capital,
                risk_per_trade_pct,
                float(pts),
                lot_value,
                min_lot_size,
            )
        return result

    # ------------------------------------------------------------------
    # Helpers extracted from calculate_position_size

    def _check_risk_limits(self) -> Optional[str]:
        """Return a string reason when risk limits block a position."""

        if self.smoothed_drawdown_pct > self.max_drawdown_pct:
            return (
                f"Drawdown máximo excedido: {self.smoothed_drawdown_pct:.2f}% "
                f"> {self.max_drawdown_pct:.2f}%"
            )

        daily_loss_pct = self._daily_loss_pct()
        if daily_loss_pct < -self.max_daily_loss_pct:
            return f"Limite de perda diária atingido: {daily_loss_pct:.2f}% < -{self.max_daily_loss_pct:.2f}%"

        if self.last_loss_time is not None and self.consecutive_losses >= 3:
            cooling_period_passed = (
                datetime.now(timezone.utc) - self.last_loss_time
            ) > timedelta(minutes=self.cooling_period_minutes)
            if not cooling_period_passed:
                return (
                    f"Período de resfriamento ({self.cooling_period_minutes}m) "
                    f"após {self.consecutive_losses} perdas consecutivas"
                )

        return None

    def _adjust_risk_pct(
        self, base_pct: float, confidence: float, lambda_factor: Optional[float]
    ) -> float:
        """Return risk percentage after applying adjustments."""

        risk_pct = base_pct
        if self.consecutive_losses >= 2:
            risk_pct *= self.stop_loss_adjustment ** (self.consecutive_losses - 1)

        confidence_factor = 0.5 + (confidence * 0.5)
        risk_pct *= confidence_factor

        factor = lambda_factor if lambda_factor is not None else 1.0
        return risk_pct * factor

    def _apply_min_lot_limit(
        self,
        quantity: float,
        current_price: float,
        risk_amount: float,
        risk_pct: float,
        stop_distance_pct: float,
        min_lot_size: Optional[float],
        confidence: float,
        volume: Optional[float],
        volatility: Optional[float],
    ) -> Tuple[float, Optional[Dict[str, Any]]]:
        """Adjust quantity according to the minimum lot size.

        Parameters
        ----------
        quantity
            Quantidade calculada inicialmente.
        current_price
            Preço corrente do ativo.
        risk_amount
            Valor em risco na operação.
        risk_pct
            Percentual de risco por trade.
        stop_distance_pct
            Distância percentual até o stop loss.
        min_lot_size
            Lote mínimo da exchange.
        confidence
            Confiança do sinal.
        volume
            Volume negociado no período.
        volatility
            Volatilidade observada.

        Returns
        -------
        Tuple[float, Optional[Dict[str, Any]]]
            Quantidade ajustada e, opcionalmente, informação de rejeição.
        """

        if risk_pct >= self.min_lot_check_threshold:
            return quantity, None

        lot = min_lot_size if min_lot_size is not None else self.min_lot_size
        if not lot:
            logger.debug(
                "risk_per_trade_pct abaixo de %s%% e sem lote mínimo definido. "
                "Posição ignorada.",
                self.min_lot_check_threshold,
            )
            return 0.0, {
                "position_allowed": False,
                "reason": "min_lot_unknown",
                "position_size": 0.0,
                "quantity": 0.0,
                "risk_amount": risk_amount,
                "risk_pct": risk_pct,
                "stop_distance_pct": stop_distance_pct,
            }

        if quantity < lot:
            lot_cost = lot * current_price
            if risk_amount < lot_cost:
                logger.warning(
                    "size_below_min_lot: risk_amount %.2f, min_lot_size %.6f, "
                    "lot_cost %.2f, requested_qty %.6f | conf=%.2f vol=%s volat=%.2f",
                    risk_amount,
                    lot,
                    lot_cost,
                    quantity,
                    confidence,
                    volume if volume is not None else "n/a",
                    volatility if volatility is not None else float("nan"),
                )
                return 0.0, {
                    "position_allowed": False,
                    "reason": "size_below_min_lot",
                    "position_size": 0.0,
                    "quantity": 0.0,
                    "risk_amount": risk_amount,
                    "risk_pct": risk_pct,
                    "stop_distance_pct": stop_distance_pct,
                }
            logger.debug(
                "Quantidade %.6f abaixo do lote mínimo %.6f. Aplicando lote mínimo.",
                quantity,
                lot,
            )
            quantity = lot

        return quantity, None

    def update_capital(self, new_capital: float) -> Dict[str, Any]:
        """
        Atualiza o capital atual e recalcula métricas de risco.

        Args:
            new_capital: Novo valor do capital em USD

        Returns:
            Dicionário com estado atualizado do capital e métricas de risco
        """
        previous_capital = self.current_capital
        self.current_capital = new_capital

        # Atualizar capital de pico se necessário
        if new_capital > self.peak_capital:
            self.peak_capital = new_capital

        # Calcular drawdown atual
        self.current_drawdown_pct = (
            (self.peak_capital - new_capital) / self.peak_capital
        ) * 100

        # Atualizar PnL diário
        if (
            self.daily_reset_time is None
            or datetime.now(timezone.utc) > self.daily_reset_time
        ):
            # Reiniciar contador diário
            self.daily_pnl = new_capital - previous_capital
            # Definir próximo reset para meia-noite (UTC)
            now = datetime.now(timezone.utc)
            self.daily_reset_time = datetime(
                now.year,
                now.month,
                now.day,
                0,
                0,
                0,
                tzinfo=timezone.utc,
            ) + timedelta(days=1)
        else:
            # Acumular PnL do dia
            self.daily_pnl += new_capital - previous_capital

        # Verificar se é uma perda
        is_loss = new_capital < previous_capital
        if is_loss:
            self.last_loss_time = datetime.now(timezone.utc)
            self.consecutive_losses += 1
        else:
            self.consecutive_losses = 0

        # Registrar estado atual
        self._log_capital_state()

        tags = [f"profile:{self.risk_profile}"]
        self.statsd.gauge("risk.current_capital", self.current_capital, tags=tags)
        self.statsd.gauge("risk.drawdown_pct", self.current_drawdown_pct, tags=tags)
        self.statsd.gauge("risk.daily_pnl", self.daily_pnl, tags=tags)

        log_event(
            event_type="risk.update",
            payload={"new_capital": self.current_capital},
            source="risk_management",
            level="info",
        )

        if self.event_bus:
            self.event_bus.publish(
                "risk.update",
                RiskUpdateEvent(new_capital=self.current_capital),
            )
            self.event_bus.publish(
                "risk.recalibrated",
                RiskRecalibratedEvent(
                    current_capital=self.current_capital,
                    drawdown_pct=self.current_drawdown_pct,
                ),
            )
            log_event(
                event_type="risk.recalibrated",
                payload={
                    "current_capital": self.current_capital,
                    "drawdown_pct": self.current_drawdown_pct,
                },
                source="risk_management",
            )

        # Retornar estado atualizado
        return {
            "previous_capital": previous_capital,
            "current_capital": self.current_capital,
            "peak_capital": self.peak_capital,
            "drawdown_pct": self.current_drawdown_pct,
            "daily_pnl": self.daily_pnl,
            "daily_pnl_pct": self._daily_loss_pct(),
            "consecutive_losses": self.consecutive_losses,
        }

    def calculate_position_size(
        self,
        symbol: str,
        current_price: float,
        stop_loss_price: float,
        confidence: float = 0.5,
        volatility: Optional[float] = None,
        volume: Optional[float] = None,
        min_lot_size: Optional[float] = None,
        informational_mass: Optional[float] = None,
        initial_informational_mass: Optional[float] = None,
        lambda_factor: Optional[float] = None,
        *,
        trace_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Calcula o tamanho ideal de posição baseado em gerenciamento de risco.

        Args:
            symbol: Símbolo do ativo
            current_price: Preço atual do ativo
            stop_loss_price: Preço de stop loss
            confidence: Nível de confiança do sinal (0.0 a 1.0)
            volatility: Volatilidade do ativo (opcional)
            volume: Volume negociado no período (opcional)
            min_lot_size: Lote mínimo da exchange (opcional)
            informational_mass: Massa informacional atual do universo (opcional)
            initial_informational_mass: Massa informacional inicial do universo (opcional)

        Returns:
            Dicionário com informações sobre o tamanho da posição
        """
        logger.debug(f"QUALIARiskManager calculate_position_size para '{symbol}':")
        logger.debug(f"  - Usando self.risk_per_trade_pct: {self.risk_per_trade_pct}%")
        logger.debug(f"  - Capital Atual: {self.current_capital}")
        logger.debug(
            f"  - Preço Atual: {current_price}, Stop Loss: {stop_loss_price}, Confiança: {confidence}"
        )
        reason = self._check_risk_limits()
        if reason is not None:
            logger.warning(
                "%s | confidence=%.2f volume=%s volatility=%s limits: drawdown<=%.2f%% daily_loss<=%.2f%%",
                reason,
                confidence,
                volume if volume is not None else "n/a",
                volatility if volatility is not None else "n/a",
                self.max_drawdown_pct,
                self.max_daily_loss_pct,
            )
            return {
                "position_allowed": False,
                "reason": reason,
                "position_size": 0.0,
                "quantity": 0.0,
            }

        # Calcular risco por operação (em USD)
        risk_per_trade_pct = self._adjust_risk_pct(
            self.risk_per_trade_pct, confidence, lambda_factor
        )
        factor = lambda_factor if lambda_factor is not None else 1.0

        # Calcular valor em risco por operação
        risk_amount = self.current_capital * (risk_per_trade_pct / 100)
        logger.debug(f"  - Valor em Risco Calculado (risk_amount): {risk_amount}")

        # Verificar se stop_loss_price não é None antes de calcular a distância
        if stop_loss_price is None:
            # YAA: Usar stop loss dinâmico em vez de valor fixo
            dynamic_stop_loss = self._calculate_dynamic_stop_loss(
                symbol=symbol,
                current_price=current_price,
                volatility=volatility,
                confidence=confidence,
            )
            stop_distance_pct = (
                abs((dynamic_stop_loss - current_price) / current_price) * 100
            )
            logger.info(
                f"Stop loss dinâmico calculado para {symbol}: {dynamic_stop_loss:.4f} (distância: {stop_distance_pct:.2f}%)"
            )
        else:
            # Calcular distância percentual até o stop loss
            stop_distance_pct = (
                abs((stop_loss_price - current_price) / current_price) * 100
            )

        # Ajustar stop distance com base na volatilidade (se disponível)
        if volatility is not None:
            # Se o ativo for muito volátil, podemos precisar de um stop mais
            # largo
            if volatility > 5.0:  # Volatilidade anualizada > 5%
                # Garantir que o stop é pelo menos 1/3 da volatilidade diária
                daily_vol = volatility / math.sqrt(252)
                min_stop_distance = daily_vol / 3
                if stop_distance_pct < min_stop_distance:
                    stop_distance_pct = min_stop_distance
                    logger.debug(
                        f"Stop distance ajustado para {stop_distance_pct:.2f}% devido à alta volatilidade"
                    )

        var_pct = self._compute_var_pct(volatility)
        if var_pct > self.max_var_pct:
            logger.warning(
                "VaR limit hit for %s: %.2f%% > %.2f%% | confidence=%.2f volume=%s",
                symbol,
                var_pct,
                self.max_var_pct,
                confidence,
                volume if volume is not None else "n/a",
            )
            return {
                "position_allowed": False,
                "reason": "var_limit",
                "position_size": 0.0,
                "quantity": 0.0,
                "var_pct": var_pct,
            }

        # Calcular tamanho da posição baseado no risco aceito e distância do stop
        # Posição = Risco / (% Stop Loss)
        position_size = (
            risk_amount / (stop_distance_pct / 100) if stop_distance_pct > 0 else 0
        )
        logger.debug(f"  - Tamanho da Posição (antes de max_limit): {position_size}")

        # Limitar ao tamanho máximo de posição
        mass_ratio = 1.0
        if (
            informational_mass is not None
            and initial_informational_mass is not None
            and initial_informational_mass > 0
        ):
            mass_ratio = informational_mass / initial_informational_mass

        dynamic_pct = self.mass_based_size_pct * mass_ratio * factor
        effective_pct = min(self.max_position_size_pct * factor, dynamic_pct)
        max_position_size = self.current_capital * (effective_pct / 100)
        if position_size > max_position_size:
            position_size = max_position_size
            logger.debug(
                f"Posição limitada ao máximo de ${position_size:.2f} ({effective_pct:.1f}% do capital)"
            )

        # Calcular quantidade de unidades
        quantity = position_size / current_price if current_price > 0 else 0

        # Ajustar para o lote mínimo quando o risco efetivo por trade for pequeno
        quantity, reject = self._apply_min_lot_limit(
            quantity,
            current_price,
            risk_amount,
            risk_per_trade_pct,
            stop_distance_pct,
            min_lot_size,
            confidence,
            volume,
            volatility,
        )
        if reject is not None:
            return reject
        position_size = quantity * current_price

        # Registrar tamanho da posição
        self.position_sizing_history.append(
            {
                "timestamp": datetime.now(timezone.utc),
                "symbol": symbol,
                "price": current_price,
                "position_size": position_size,
                "quantity": quantity,
                "risk_pct": risk_per_trade_pct,
                "capital": self.current_capital,
            }
        )

        tags = [f"symbol:{symbol}", f"profile:{self.risk_profile}"]
        if trace_id:
            tags.append(f"trace_id:{trace_id}")
        self.statsd.gauge("risk.position_size", position_size, tags=tags)
        self.statsd.gauge("risk.risk_amount", risk_amount, tags=tags)

        return {
            "position_allowed": True,
            "position_size": position_size,
            "quantity": quantity,
            "risk_amount": risk_amount,
            "risk_pct": risk_per_trade_pct,
            "stop_distance_pct": stop_distance_pct,
            "var_pct": var_pct,
        }

    def can_open_new_position(
        self, current_positions: int, *, trace_id: Optional[str] = None
    ) -> Tuple[bool, str]:
        """
        Verifica se é permitido abrir uma nova posição.

        Args:
            current_positions: Número atual de posições abertas

        Returns:
            Tupla com (permitido, razão)
        """
        # Verificar número máximo de posições
        if current_positions >= self.max_open_positions:
            return (
                False,
                f"Número máximo de posições atingido: {current_positions}/{self.max_open_positions}",
            )

        # Verificar drawdown excessivo
        if self.smoothed_drawdown_pct > self.max_drawdown_pct:
            return (
                False,
                f"Drawdown máximo excedido: {self.smoothed_drawdown_pct:.2f}%",
            )

        # Verificar perda diária excessiva
        daily_loss_pct = self._daily_loss_pct()
        if daily_loss_pct < -self.max_daily_loss_pct:
            return False, f"Limite de perda diária atingido: {daily_loss_pct:.2f}%"

        # Verificar período de resfriamento
        if self.last_loss_time is not None and self.consecutive_losses >= 3:
            cooling_period_passed = datetime.now(
                timezone.utc
            ) - self.last_loss_time > timedelta(minutes=self.cooling_period_minutes)
            if not cooling_period_passed:
                return (
                    False,
                    f"Período de resfriamento após {self.consecutive_losses} perdas consecutivas",
                )

        return True, "Permitido abrir nova posição"

    def process_trade_result(self, trade_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        Processa o resultado de uma operação finalizada.

        Args:
            trade_info: Informações sobre a operação finalizada

        Returns:
            Dicionário com métricas atualizadas
        """
        # Extrair informações da operação
        pnl = trade_info.get("realized_pnl", 0)
        entry_price = trade_info.get("entry_price", 0)
        exit_price = trade_info.get("exit_price", 0)
        quantity = trade_info.get("quantity", 0)

        # Atualizar capital
        new_capital = self.current_capital + pnl
        capital_metrics = self.update_capital(new_capital)

        # Registrar operação no histórico
        self.trade_history.append(
            {
                "timestamp": datetime.now(timezone.utc),
                "symbol": trade_info.get("symbol", "unknown"),
                "side": trade_info.get("side", "unknown"),
                "entry_price": entry_price,
                "exit_price": exit_price,
                "quantity": quantity,
                "pnl": pnl,
                "capital_after": new_capital,
            }
        )

        # Retornar métricas atualizadas
        return {
            "capital": new_capital,
            "pnl": pnl,
            "entry_price": entry_price,
            "exit_price": exit_price,
            "quantity": quantity,
            "capital_metrics": capital_metrics,
        }
