from __future__ import annotations

"""Stress simulation utilities for QUALIA risk management."""

import time
from typing import Sequence, Dict, Any

from .advanced_risk_manager import AdvancedRiskManager
from ..utils.logger import get_logger


logger = get_logger(__name__)


class StressSimulator:
    """Simulate extreme liquidity and volatility scenarios.

    This helper executes a series of synthetic trades using an
    :class:`~qualia.risk_management.advanced_risk_manager.AdvancedRiskManager`
    instance and records drawdown and latency metrics.
    """

    def __init__(self, risk_manager: AdvancedRiskManager) -> None:
        self.risk_manager = risk_manager

    def run(
        self,
        price_series: Sequence[float],
        liquidity: Sequence[float],
    ) -> Dict[str, float]:
        """Execute the stress simulation.

        Parameters
        ----------
        price_series
            Sequential close prices used to generate PnL.
        liquidity
            Relative liquidity in ``[0, 1]`` for each price point. Values below
            ``0.5`` amplify negative PnL to mimic slippage.

        Returns
        -------
        Dict[str, float]
            Summary metrics including ``max_drawdown_pct`` and latency stats.
        """
        if len(price_series) != len(liquidity):
            raise ValueError("price_series and liquidity must have same length")

        start_time = time.perf_counter()
        latencies: list[float] = []
        max_drawdown = 0.0

        for i in range(1, len(price_series)):
            step_start = time.perf_counter()
            pnl = price_series[i] - price_series[i - 1]

            if liquidity[i] < 0.5:
                pnl *= 1.5

            self.risk_manager.process_trade_result({"realized_pnl": pnl})
            max_drawdown = max(max_drawdown, self.risk_manager.current_drawdown_pct)
            latencies.append((time.perf_counter() - step_start) * 1000)

        total_latency_ms = (time.perf_counter() - start_time) * 1000
        avg_latency_ms = sum(latencies) / len(latencies) if latencies else 0.0

        metrics = {
            "max_drawdown_pct": max_drawdown,
            "average_latency_ms": avg_latency_ms,
            "total_latency_ms": total_latency_ms,
        }
        logger.info(
            "Stress simulation finished: max_drawdown=%.2f%% avg_latency=%.2fms",
            max_drawdown,
            avg_latency_ms,
        )
        return metrics


__all__ = ["StressSimulator"]
