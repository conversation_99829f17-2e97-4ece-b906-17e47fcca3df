"""
Signal Generation Module

Provides signal generation capabilities for creating trading signals
based on quantum operator results and market analysis.
"""

from .generator import SignalGenerator, SignalConfig, TradingSignal
from .signal_type import SignalType
from .quantum_analyzer import QuantumSignalAnalyzer
from .technical_analyzer import TechnicalSignalAnalyzer
from .sentiment_analyzer import SentimentSignalAnalyzer

__all__ = [
    "SignalGenerator",
    "SignalConfig",
    "TradingSignal",
    "SignalType",
    "QuantumSignalAnalyzer",
    "TechnicalSignalAnalyzer",
    "SentimentSignalAnalyzer",
]
