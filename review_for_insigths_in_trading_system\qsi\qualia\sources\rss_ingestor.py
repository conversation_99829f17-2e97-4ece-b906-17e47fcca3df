from __future__ import annotations

"""RSS feed ingestor for QUALIA NEXUS.

Fetches headlines from one or multiple RSS/Atom feeds, encodes sentiment using
`RSSTextSentimentEncoder`, and publishes vectors to the EventBus under
`NEWS_STREAM`.

Runs in an asynchronous loop to periodically poll the feeds.
"""

import asyncio
import time
from typing import Dict, List, Set

import feedparser  # type: ignore

from qualia.core.rss_encoder import RSSTextSentimentEncoder
from qualia.memory.event_bus import SimpleEventBus, NEWS_STREAM
from qualia.utils.logger import get_logger
import contextlib

logger = get_logger(__name__)


class RSSIngestor:
    """Poll RSS feeds and push encoded snapshots to the event bus."""

    def __init__(
        self,
        urls: List[str],
        event_bus: SimpleEventBus,
        poll_seconds: int = 60,
        seen_id_ttl: int | None = 3600,
        max_seen_ids: int | None = 1000,
    ) -> None:
        """Initialize the ingestor.

        Parameters
        ----------
        urls
            Lista de URLs RSS/Atom para consulta.
        event_bus
            Barramento de eventos que receberá as manchetes codificadas.
        poll_seconds
            Intervalo entre coletas do feed.
        seen_id_ttl
            Tempo em segundos para manter IDs no cache de vistos. ``None`` desativa a expiração.
        max_seen_ids
            Quantidade máxima de IDs armazenados para evitar crescimento ilimitado.
        """
        if not urls:
            raise ValueError("urls list cannot be empty")
        self.urls = urls
        self.poll_sec = max(10, int(poll_seconds))
        self.bus = event_bus
        self.encoder = RSSTextSentimentEncoder()
        self.seen_id_ttl = seen_id_ttl
        self.max_seen_ids = max_seen_ids
        self._seen_ids: Dict[str, float] = {}
        self._task: asyncio.Task | None = None

    # ------------------------------------------------------------------

    def _prune_seen_ids(self, now: float) -> None:
        """Remove stale entries from ``_seen_ids``."""

        if self.seen_id_ttl is not None:
            expired = [
                k for k, ts in self._seen_ids.items() if now - ts > self.seen_id_ttl
            ]
            for k in expired:
                self._seen_ids.pop(k, None)

        if self.max_seen_ids is not None and len(self._seen_ids) > self.max_seen_ids:
            # remove oldest entries first
            to_remove = sorted(self._seen_ids.items(), key=lambda item: item[1])[
                : len(self._seen_ids) - self.max_seen_ids
            ]
            for k, _ in to_remove:
                self._seen_ids.pop(k, None)

    # ------------------------------------------------------------------

    async def _poll_once(self) -> None:
        now = time.time()
        self._prune_seen_ids(now)
        for url in self.urls:
            try:
                feed = feedparser.parse(url)
            except Exception as exc:  # pragma: no cover
                logger.warning("RSSIngestor: erro ao ler %s: %s", url, exc)
                continue
            for entry in feed.entries:
                entry_id = entry.get("id") or entry.get("link") or entry.get("title")
                if entry_id is None:
                    continue
                ts = self._seen_ids.get(entry_id)
                if ts is not None and (
                    self.seen_id_ttl is None or now - ts <= self.seen_id_ttl
                ):
                    continue
                self._seen_ids[entry_id] = now
                self._prune_seen_ids(now)
                title = entry.get("title", "")
                snap = {"text": title}
                vec = self.encoder.encode(snap)
                ts_event = time.time()
                self.bus.publish(
                    NEWS_STREAM,
                    {
                        "vector": vec.tolist(),  # convert for JSON-friendly handlers
                        "title": title,
                        "timestamp": ts_event,
                        "source": url,
                    },
                )
                logger.debug("RSSIngestor publicou headline '%s'", title[:50])

    async def _loop(self) -> None:
        while True:
            await self._poll_once()
            await asyncio.sleep(self.poll_sec)

    def start(self) -> None:
        if self._task is None or self._task.done():
            self._task = asyncio.create_task(self._loop())
            logger.info("RSSIngestor iniciado para %d feeds", len(self.urls))

    async def stop(self) -> None:
        if self._task:
            self._task.cancel()
            with contextlib.suppress(asyncio.CancelledError):
                await self._task
            self._task = None
