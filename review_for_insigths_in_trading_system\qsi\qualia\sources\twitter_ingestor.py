from __future__ import annotations

"""Twitter ingestor for NEXUS.

Streams tweets via Tweepy (v2) filtered by keywords, encodes sentiment with
`RSSTextSentimentEncoder`, publishes to `NEWS_STREAM`.

Requires environment variable `TWITTER_BEARER_TOKEN` or explicit token.
"""

import os
import threading
from typing import List

from qualia.core.rss_encoder import RSSTextSentimentEncoder
from qualia.memory.event_bus import SimpleEventBus, NEWS_STREAM
from qualia.utils.logger import get_logger

logger = get_logger(__name__)

try:
    import tweepy  # type: ignore
except ModuleNotFoundError:  # pragma: no cover
    tweepy = None  # type: ignore


class TwitterIngestor:
    """Streams tweets via Tweepy v4 StreamingClient inside a background thread."""

    def __init__(
        self,
        keywords: List[str],
        event_bus: SimpleEventBus,
        bearer_token: str | None = None,
    ) -> None:
        if tweepy is None:
            raise RuntimeError("tweepy não instalado; `pip install tweepy` (>=4.14).")

        self.encoder = RSSTextSentimentEncoder()
        self.bus = event_bus
        self.keywords = keywords
        token = bearer_token or os.getenv("TWITTER_BEARER_TOKEN")
        if not token:
            raise ValueError(
                "TWITTER_BEARER_TOKEN não definido no .env ou variáveis de ambiente"
            )

        # Create streaming client (sync) and attach callbacks
        self.client = tweepy.StreamingClient(token, wait_on_rate_limit=True)
        self.client.on_tweet = self._on_tweet  # type: ignore[attr-defined]
        self.thread: threading.Thread | None = None

    # ------------------------------------------------------------------

    def start(self) -> None:
        """Start streaming in background thread."""

        def _run():
            try:
                self._setup_rules()
                logger.info(
                    "TwitterIngestor iniciando stream para: %s",
                    ", ".join(self.keywords),
                )
                self.client.filter(
                    expansions=[], tweet_fields=["created_at", "lang"], threaded=False
                )
            except Exception as exc:  # pragma: no cover
                logger.error("TwitterIngestor finalizado: %s", exc)

        self.thread = threading.Thread(target=_run, daemon=True)
        self.thread.start()

    def stop(self) -> None:
        """Stop the Twitter stream, keeping the thread for later join."""
        if self.thread and self.thread.is_alive():
            self.client.disconnect()

    # -------------------------- helpers ------------------------------

    def _setup_rules(self):
        # Clear existing
        existing = self.client.get_rules()
        if existing.data:
            self.client.delete_rules([r.id for r in existing.data])
        for kw in self.keywords:
            self.client.add_rules(tweepy.StreamRule(kw))

    # Tweepy callback -------------------------------------------------

    def _on_tweet(self, tweet):  # type: ignore[override]
        if getattr(tweet, "lang", "en") != "en":
            return
        text = tweet.text
        vec = self.encoder.encode({"text": text})
        self.bus.publish(
            NEWS_STREAM,
            {
                "vector": vec.tolist(),
                "tweet": text,
                "timestamp": tweet.created_at.timestamp() if tweet.created_at else None,
            },
        )
