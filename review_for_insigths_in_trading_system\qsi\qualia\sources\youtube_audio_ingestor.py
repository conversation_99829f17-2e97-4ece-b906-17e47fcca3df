from __future__ import annotations

"""YouTube audio ingestor for NEXUS.

Downloads audio streams via `yt_dlp`, salva em um arquivo temporário (primeiros
N segundos), extrai o vetor MFCC com `MFCCQuantumEncoder` e publica no
`AUDIO_STREAM`.

Dependências pesadas são opcionais e o ingestor funciona de forma degradada
caso ausentes.
"""

import asyncio
import os
import tempfile
from typing import List

from qualia.core.audio_encoders import MFCCQuantumEncoder
from qualia.memory.event_bus import SimpleEventBus, AUDIO_STREAM
from qualia.utils.logger import get_logger

logger = get_logger(__name__)

try:
    import yt_dlp  # type: ignore
except ModuleNotFoundError:  # pragma: no cover
    yt_dlp = None  # type: ignore

try:
    import soundfile as sf  # type: ignore
except ModuleNotFoundError:  # pragma: no cover
    sf = None  # type: ignore


class YouTubeAudioIngestor:
    """Fetch audio from video IDs and publish MFCC vectors."""

    def __init__(
        self, video_ids: List[str], event_bus: SimpleEventBus, seconds: int = 20
    ) -> None:
        if yt_dlp is None or sf is None:
            raise RuntimeError("yt_dlp e soundfile requeridos; instale-os.")
        self.ids = video_ids
        self.bus = event_bus
        self.seconds = seconds
        self.encoder = MFCCQuantumEncoder()

    async def ingest_all(self):
        loop = asyncio.get_running_loop()
        for vid in self.ids:
            await loop.run_in_executor(None, self._process_single, vid)

    def start(self) -> asyncio.Task:
        """Agendar a ingestão com ``asyncio.create_task``."""
        return asyncio.create_task(self.ingest_all())

    # ------------------------------------------------------------------

    def _process_single(self, vid: str):  # blocking
        url = f"https://www.youtube.com/watch?v={vid}"
        tmp_path = ""
        try:
            ffmpeg_loc = os.getenv(
                "FFMPEG_BIN", r"C:\ffmpeg-7.1.1-essentials_build\bin"
            )
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp:
                tmp_path = tmp.name
            ydl_opts = {
                "format": "bestaudio/best",
                "quiet": True,
                "noplaylist": True,
                "outtmpl": tmp_path,
                "postprocessors": [
                    {
                        "key": "FFmpegExtractAudio",
                        "preferredcodec": "wav",
                        "preferredquality": "192",
                    }
                ],
                "ffmpeg_location": ffmpeg_loc,
            }
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                ydl.download([url])

            samples, sr = sf.read(tmp_path)
            if samples.ndim > 1:
                samples = samples.mean(axis=1)
            samples = samples[: sr * self.seconds]
        except Exception as exc:
            logger.warning("Falha ao baixar/decodificar audio %s: %s", vid, exc)
            return
        finally:
            try:
                os.remove(tmp_path)
            except Exception:
                pass
        vec = self.encoder.encode({"samples": samples, "sample_rate": sr})
        self.bus.publish(AUDIO_STREAM, {"vector": vec.tolist(), "video_id": vid})
        logger.info("YouTubeAudioIngestor publicou vetor para %s", vid)
