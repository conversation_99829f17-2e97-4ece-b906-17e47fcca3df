from __future__ import annotations

"""Aggregation of strategy signals using weighted voting."""

from dataclasses import dataclass
from typing import Any, Dict

from ..event_bus import AsyncEventBus, TradingSignalGenerated
from ..utils.logger import get_logger


@dataclass
class AggregationMetrics:
    """Summary metrics for the aggregated signal."""

    agreement_ratio: float
    voters: int


class CoherenceAggregator:
    """Listen to individual strategy signals and emit a consolidated decision."""

    def __init__(
        self, event_bus: AsyncEventBus, strategy_weights: Dict[str, float] | None = None
    ) -> None:
        self.event_bus = event_bus
        self.strategy_weights = strategy_weights or {}
        self._signals: Dict[str, Dict[str, float]] = {}
        self.logger = get_logger(__name__)
        self._last_metrics = AggregationMetrics(0.0, 0)

        self.event_bus.subscribe_async("strategy.signal", self._handle_signal)

    async def _handle_signal(self, payload: Dict[str, Any]) -> None:
        """Handle ``strategy.signal`` events."""
        strategy = str(payload.get("strategy", ""))
        signal = str(payload.get("signal", "hold")).lower()
        confidence = float(payload.get("confidence", 0.0))
        if not strategy:
            self.logger.warning(
                "Signal payload missing strategy identifier: %s", payload
            )
            return
        self._signals[strategy] = {"signal": signal, "confidence": confidence}
        await self._publish_aggregated()

    def _aggregate(self) -> tuple[str, float, AggregationMetrics]:
        totals = {"buy": 0.0, "sell": 0.0, "hold": 0.0}
        agreements = {"buy": 0.0, "sell": 0.0, "hold": 0.0}
        total_weight = 0.0
        for strat, data in self._signals.items():
            weight = self.strategy_weights.get(strat, 1.0)
            total_weight += weight
            sig = data.get("signal", "hold")
            conf = float(data.get("confidence", 0.0))
            if sig == "buy":
                totals["buy"] += weight * conf
                agreements["buy"] += weight
            elif sig == "sell":
                totals["sell"] += weight * conf
                agreements["sell"] += weight
            else:
                totals["hold"] += weight * conf
                agreements["hold"] += weight
        if total_weight == 0.0:
            self._last_metrics = AggregationMetrics(0.0, 0)
            return "hold", 0.0, self._last_metrics
        best = max(totals.items(), key=lambda x: x[1])[0]
        confidence = totals[best] / total_weight
        confidence = max(0.0, min(confidence, 1.0))
        ratio = agreements[best] / total_weight
        self._last_metrics = AggregationMetrics(
            agreement_ratio=ratio,
            voters=len(self._signals),
        )
        return best, confidence, self._last_metrics

    async def _publish_aggregated(self) -> None:
        signal, confidence, metrics = self._aggregate()
        self.logger.debug(
            "Aggregated signal: %s %.3f (agreement %.3f)",
            signal,
            confidence,
            metrics.agreement_ratio,
        )
        self.event_bus.publish(
            "trading.signal.generated",
            TradingSignalGenerated(
                decisions=[
                    {
                        "signal": signal.upper(),
                        "confidence": confidence,
                        "agreement_ratio": metrics.agreement_ratio,
                        "voters": metrics.voters,
                    }
                ]
            ),
        )

    def get_metrics(self) -> AggregationMetrics:
        """Return metrics of the most recent aggregation."""

        return self._last_metrics
