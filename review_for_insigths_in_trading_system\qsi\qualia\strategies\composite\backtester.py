"""Backtesting helpers for CompositeStrategy."""

from __future__ import annotations

from typing import Any, Dict, TYPE_CHECKING

import pandas as pd

from ...utils.logger import get_logger

if TYPE_CHECKING:  # pragma: no cover - for type hints only
    from .strategy import CompositeStrategy

logger = get_logger(__name__)


def _calculate_min_lookback_for_trading(strategy: "CompositeStrategy") -> int:
    """Calculate minimal lookback based on composite and sub strategies."""
    lookbacks = []

    for param in ["rsi_period", "ema_long", "atr_period"]:
        value = strategy.common_params.get(param)
        if isinstance(value, int) and value > 0:
            lookbacks.append(value)

    for strat in strategy.strategies:
        for attr in ["data_lookback_period", "lookback_period"]:
            if hasattr(strat, attr):
                attr_val = getattr(strat, attr)
                if isinstance(attr_val, int) and attr_val > 0:
                    if attr == "lookback_period":
                        attr_val += 10
                    lookbacks.append(attr_val)
        for param in ["rsi_period", "ema_long", "atr_period"]:
            if hasattr(strat, param):
                val = getattr(strat, param)
                if isinstance(val, int) and val > 0:
                    lookbacks.append(val)

    return max(max(lookbacks, default=1), 1)


def backtest(
    strategy: "CompositeStrategy",
    market_data_map: Dict[str, pd.DataFrame],
    initial_capital: float,
    risk_per_trade_pct: float,
) -> Dict[str, Any]:
    """Execute a simple backtest for the given composite strategy."""
    logger.info(
        f"Iniciando backtest para {strategy.name} com capital inicial {initial_capital:.2f}"
    )

    if not market_data_map or not any(
        df is not None and not df.empty for df in market_data_map.values()
    ):
        logger.error(
            "Dados de mercado para backtest estão vazios ou mapa não contém DataFrames válidos."
        )
        return {"error": "Dados de mercado vazios ou inválidos", "score": -float("inf")}

    main_asset_df = None
    main_asset_symbol = None
    for symbol, df_market in market_data_map.items():
        if df_market is not None and not df_market.empty:
            main_asset_df = df_market
            main_asset_symbol = symbol
            break

    if main_asset_df is None:
        logger.error(
            "Nenhum DataFrame de dados de mercado válido encontrado para backtest."
        )
        return {"error": "Nenhum DataFrame válido"}

    logger.info(f"Backtest da CompositeStrategy usando dados para: {main_asset_symbol}")

    capital = initial_capital
    positions = []
    trade_history = []
    num_trades = 0
    pnl_total = 0.0

    backtest_context = {
        "risk_per_trade_pct": risk_per_trade_pct,
        "common_params": strategy.common_params,
        "sub_strategy_configs": strategy.sub_strategy_configs,
        "qast_controller": None,
        "metacognition": None,
    }
    strategy.initialize(context=backtest_context)

    min_lookback_for_trading = _calculate_min_lookback_for_trading(strategy)

    for i in range(1, len(main_asset_df)):
        current_market_slice = main_asset_df.iloc[: i + 1]

        if len(current_market_slice) == 0:
            continue
        current_price = current_market_slice["close"].iloc[-1]

        analysis_results = strategy.analyze_market(current_market_slice)

        if len(current_market_slice) < min_lookback_for_trading:
            logger.debug(
                f"Backtest: Aquecendo... Barra {i+1}/{len(main_asset_df)}. Dados no slice: {len(current_market_slice)}. Preço: {current_price:.2f}"
            )
            continue

        logger.debug(
            f"Backtest: Pós-aquecimento. Barra {i+1}/{len(main_asset_df)}. Dados no slice: {len(current_market_slice)}. Analisando para trade."
        )
        signal_df = strategy.generate_signals(analysis_results)

        if not signal_df.empty:
            latest = signal_df.iloc[-1].to_dict()
            sig_type = latest.get("signal", "hold").lower()
            size = 0.0
            if sig_type == "buy" and capital > 0:
                risk_amount = capital * risk_per_trade_pct
                size = risk_amount / current_price
                positions.append(
                    {
                        "entry_timestamp": current_market_slice.index[-1],
                        "entry_price": current_price,
                        "size": size,
                        "side": "buy",
                        "take_profit": latest.get("take_profit"),
                        "stop_loss": latest.get("stop_loss"),
                    }
                )
                capital -= size * current_price
                logger.info(
                    f"TRADE_HISTORY: {current_market_slice.index[-1]} - BUY @ {current_price:.2f}, Size: {size:.4f}, Capital: {capital:.2f}"
                )
            elif sig_type == "sell" and positions:
                entry_pos = positions.pop(0)
                size = entry_pos["size"]
                pnl_abs = (current_price - entry_pos["entry_price"]) * size
                capital += size * current_price
                pnl_total += pnl_abs
                num_trades += 1
                pnl_pct = (
                    (pnl_abs / (entry_pos["entry_price"] * size)) * 100
                    if (entry_pos["entry_price"] * size) != 0
                    else 0
                )
                logger.info(
                    f"TRADE_HISTORY: {current_market_slice.index[-1]} - EXIT SELL @ {current_price:.2f}, Size: {size:.4f}, PnL: {pnl_abs:.2f}, Capital: {capital:.2f}"
                )
                trade_history.append(
                    {
                        "timestamp": current_market_slice.index[-1],
                        "type": "exit",
                        "side": "sell",
                        "price": current_price,
                        "size": size,
                        "pnl_abs": pnl_abs,
                        "pnl_pct": pnl_pct,
                        "exit_reason": "signal",
                        "entry_timestamp": entry_pos["entry_timestamp"],
                    }
                )
        elif positions and positions[0]["side"] == "buy":
            entry_pos = positions[0]
            entry_price = entry_pos["entry_price"]
            take_profit = entry_pos.get("take_profit")
            stop_loss = entry_pos.get("stop_loss")

            if take_profit is None:
                take_profit = entry_price * 1.05
            if stop_loss is None:
                stop_loss = entry_price * 0.98

            if current_price >= take_profit or current_price <= stop_loss:
                positions.pop(0)
                size = entry_pos["size"]
                pnl_abs = (current_price - entry_price) * size
                capital += size * current_price
                pnl_total += pnl_abs
                num_trades += 1
                pnl_pct = (
                    (pnl_abs / (entry_price * size)) * 100
                    if (entry_price * size) != 0
                    else 0
                )
                exit_reason = (
                    "take_profit" if current_price >= take_profit else "stop_loss"
                )
                logger.info(
                    f"TRADE_HISTORY: {current_market_slice.index[-1]} - EXIT SELL @ {current_price:.2f}, Size: {size:.4f}, PnL: {pnl_abs:.2f}, Capital: {capital:.2f}"
                )
                trade_history.append(
                    {
                        "timestamp": current_market_slice.index[-1],
                        "type": "exit",
                        "side": "sell",
                        "price": current_price,
                        "size": size,
                        "pnl_abs": pnl_abs,
                        "pnl_pct": pnl_pct,
                        "exit_reason": exit_reason,
                        "entry_timestamp": entry_pos["entry_timestamp"],
                    }
                )

    if positions and positions[0]["side"] == "buy":
        logger.info(
            "Backtest: Liquidando posição aberta no final do período de backtest."
        )
        entry_pos = positions.pop(0)
        entry_price = entry_pos["entry_price"]
        size = entry_pos["size"]
        final_price = main_asset_df["close"].iloc[-1]

        pnl_abs = (final_price - entry_price) * size
        capital += size * final_price
        pnl_total += pnl_abs
        pnl_pct = (
            (pnl_abs / (entry_price * size)) * 100 if (entry_price * size) != 0 else 0
        )
        logger.info(
            f"TRADE_HISTORY (LIQUIDATION): {main_asset_df.index[-1]} - EXIT SELL @ {final_price:.2f}, Size: {size:.4f}, PnL: {pnl_abs:.2f}, Capital: {capital:.2f}"
        )
        trade_history.append(
            {
                "timestamp": main_asset_df.index[-1],
                "type": "exit",
                "side": "sell",
                "price": final_price,
                "size": size,
                "pnl_abs": pnl_abs,
                "pnl_pct": pnl_pct,
                "exit_reason": "end_of_backtest",
                "entry_timestamp": entry_pos["entry_timestamp"],
            }
        )

    final_capital_calc = capital
    total_return_pct = (
        ((final_capital_calc - initial_capital) / initial_capital) * 100
        if initial_capital > 0
        else 0
    )
    score = total_return_pct

    logger.info(
        f"Backtest concluído para {strategy.name}. Capital Inicial: {initial_capital:.2f}, Capital Final: {final_capital_calc:.2f}, PnL Total: {pnl_total:.2f}, Retorno: {total_return_pct:.2f}%, Trades: {num_trades}"
    )

    return {
        "score": float(score),
        "initial_capital": initial_capital,
        "final_capital": final_capital_calc,
        "total_pnl": pnl_total,
        "total_return_pct": total_return_pct,
        "num_trades": num_trades,
        "trade_history": trade_history,
        "params_used": strategy.get_parameters(),
    }
