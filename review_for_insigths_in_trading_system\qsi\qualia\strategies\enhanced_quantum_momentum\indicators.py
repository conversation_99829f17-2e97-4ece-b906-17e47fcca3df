"""Indicator helpers for :mod:`enhanced_quantum_momentum`."""

from __future__ import annotations

from typing import <PERSON>ple
import numpy as np
from numpy.typing import ArrayLike

from ... import indicators as shared_indicators


def calculate_rsi(prices: ArrayLike, period: int) -> np.ndarray:
    """Wrapper around :func:`qualia.indicators.rsi`."""
    return np.asarray(shared_indicators.rsi(prices, period), dtype=float)


def calculate_ema(prices: ArrayLike, period: int) -> np.ndarray:
    """Wrapper around :func:`qualia.indicators.ema`."""
    return np.asarray(shared_indicators.ema(prices, period), dtype=float)


def calculate_macd(
    prices: ArrayLike,
    fast_period: int = 12,
    slow_period: int = 26,
    signal_period: int = 9,
) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
    """Wrapper around :func:`qualia.indicators.macd`."""
    return tuple(
        np.asarray(x, dtype=float)
        for x in shared_indicators.macd(prices, fast_period, slow_period, signal_period)
    )


def calculate_macd_from_emas(
    fast_ema: ArrayLike,
    slow_ema: ArrayLike,
    signal_period: int = 9,
) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
    """Wrapper around :func:`qualia.indicators.calculate_macd_from_emas`."""
    return tuple(
        np.asarray(x, dtype=float)
        for x in shared_indicators.calculate_macd_from_emas(
            fast_ema, slow_ema, signal_period
        )
    )
