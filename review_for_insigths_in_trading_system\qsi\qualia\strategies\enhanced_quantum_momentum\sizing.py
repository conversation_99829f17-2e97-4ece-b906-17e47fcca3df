from __future__ import annotations

from typing import Any
import pandas as pd
import numpy as np

from ...utils.logger import get_logger

logger = get_logger(__name__)


def position_sizing(
    strategy: Any, signals: pd.DataFrame, capital: float, risk_per_trade: float
) -> pd.DataFrame:
    """Basic position sizing using fixed risk percentage."""
    # Verificar se signals é um numpy array e converter se necessário
    if isinstance(signals, np.ndarray):
        logger.warning(
            f"{strategy.__class__.__name__} - position_sizing: Recebido numpy array em vez de DataFrame. Convertendo..."
        )
        if signals.size == 0:
            return pd.DataFrame()
        signals = pd.DataFrame({"signal": signals.flatten()})

    # Verificar se é DataFrame antes de usar .empty
    if not isinstance(signals, pd.DataFrame):
        logger.error(
            f"{strategy.__class__.__name__} - position_sizing: Tipo inesperado: {type(signals)}"
        )
        return pd.DataFrame()

    if signals.empty:
        logger.debug(
            f"{strategy.__class__.__name__} - position_sizing: Nenhum sinal recebido."
        )
        return signals

    sized = signals.copy()
    risk_amount = capital * risk_per_trade

    if "price" not in sized or "stop_loss" not in sized:
        logger.warning("Sinais sem 'price' ou 'stop_loss'; retornando sem alterações.")
        sized["quantity"] = 0.0
        return sized

    price = sized["price"].astype(float)
    stop = sized["stop_loss"].astype(float)
    risk_per_unit = (price - stop).abs().replace(0, pd.NA)
    quantity = (risk_amount / risk_per_unit).fillna(0.0)
    sized["quantity"] = quantity
    return sized
