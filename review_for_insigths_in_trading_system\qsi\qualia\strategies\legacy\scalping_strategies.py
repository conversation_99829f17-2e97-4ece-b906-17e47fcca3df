"""Legacy scalping strategies.

This module contains the original implementation of :class:`QUALIAEnhancedScalpingStrategy`.
It remains here for reference and compatibility with older experiments.
New development should use ``qualia.strategies.scalping`` instead.
"""

import copy
from collections import Counter
from dataclasses import dataclass
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, List, Optional, Tuple, Union
from dataclasses import asdict, is_dataclass

from .params import EnhancedScalpingParams

import numpy as np
import pandas as pd

from ..utils.logger import get_logger
from ..market.decision_context import TradeContext, ScalpingDecision
from ..market.qast_evolutionary_strategy import (
    QASTEvolutionaryStrategy,
)
from ..core.consciousness import QUALIAConsciousness
from ..strategies.strategy_interface import (
    TradingStrategy,
    register_strategy,
)
from ..strategies.strategy_factory import StrategyFactory


# Logger centralizado do projeto
logger = get_logger(__name__)


@dataclass
class ScalpingParameters:
    """
    Parâmetros para configurar estratégias de scalping.
    """

    risk_profile: str = "balanced"
    use_quantum_metrics: bool = True


class ScalpingStrategy(TradingStrategy):
    """
    Estratégia de scalping de alta frequência para o sistema QUALIA Trading.

    Utiliza diversos indicadores técnicos, análise de microestrutura de mercado
    e, opcionalmente, métricas quânticas do framework QUALIA para identificar
    oportunidades de scalping.
    """

    def __init__(
        self,
        risk_profile: str = "balanced",
        use_quantum_metrics: bool = True,
        # Parâmetros para um modo piloto simplificado
        simple_pilot_mode: bool = False,
        simple_rsi_period: int = 14,
        simple_rsi_threshold_buy: int = 30,
        simple_rsi_threshold_sell: int = 70,
        simple_pilot_confidence: float = 0.7,
    ):
        """
        Inicializa a estratégia de scalping.

        Args:
            risk_profile: Perfil de risco ('conservative', 'balanced', 'aggressive')
            use_quantum_metrics: Se True, incorpora métricas quânticas do QUALIA
            simple_pilot_mode: Se True, ativa o modo piloto simples
            simple_rsi_period: Período para o cálculo do RSI no modo piloto
            simple_rsi_threshold_buy: Limiar de RSI para gerar sinal BUY no modo piloto
            simple_rsi_threshold_sell: Limiar de RSI para gerar sinal SELL no modo piloto
            simple_pilot_confidence: Confiança base para o modo piloto
        """
        super().__init__()

        self.risk_profile = risk_profile
        self.use_quantum_metrics = use_quantum_metrics

        # Atributos auxiliares para QPM test mode
        self.qpm_test_mode_sma_override: bool = False
        self.force_qpm_test_signal_type: Optional[str] = None

        # Armazenar configurações do modo piloto
        self.simple_pilot_mode = simple_pilot_mode
        self.simple_rsi_period = simple_rsi_period
        self.simple_rsi_threshold_buy = simple_rsi_threshold_buy
        self.simple_rsi_threshold_sell = simple_rsi_threshold_sell
        self.simple_pilot_confidence = simple_pilot_confidence

        # Configurar parâmetros com base no perfil de risco
        if risk_profile == "conservative":
            self.profit_target_pct = 0.2  # 0.2%
            self.stop_loss_pct = 0.15  # 0.15%
            self.max_position_size = 0.1  # 10% do capital
            self.min_volatility = 0.5  # Volatilidade mínima (% anualizada)
            self.max_spread_pct = 0.05  # Spread máximo como % do preço
            self.mean_reversion_threshold = 2.5  # Z-score para mean reversion
            self.momentum_threshold = 0.5  # Limiar de momentum (0-1)
            self.min_volume_percentile = 50  # Percentil mínimo de volume
        elif risk_profile == "aggressive":
            self.profit_target_pct = 0.5  # 0.5%
            self.stop_loss_pct = 0.4  # 0.4%
            self.max_position_size = 0.25  # 25% do capital
            self.min_volatility = 0.3  # Volatilidade mínima (% anualizada)
            self.max_spread_pct = 0.15  # Spread máximo como % do preço
            self.mean_reversion_threshold = 1.8  # Z-score para mean reversion
            self.momentum_threshold = 0.3  # Limiar de momentum (0-1)
            self.min_volume_percentile = 30  # Percentil mínimo de volume
        else:  # balanced (default)
            self.profit_target_pct = 0.3  # 0.3%
            self.stop_loss_pct = 0.25  # 0.25%
            self.max_position_size = 0.15  # 15% do capital
            self.min_volatility = 0.4  # Volatilidade mínima (% anualizada)
            self.max_spread_pct = 0.1  # Spread máximo como % do preço
            self.mean_reversion_threshold = 2.0  # Z-score para mean reversion
            self.momentum_threshold = 0.4  # Limiar de momentum (0-1)
            self.min_volume_percentile = 40  # Percentil mínimo de volume

        # Inicializar indicadores e estado
        # Estes são exemplos de configuração; podem ser ajustados ou tornados
        # configuráveis
        self.vwap_periods = {"1m": 60, "5m": 24, "15m": 16, "1h": 8}
        self.ema_periods = [9, 21, 55, 100]
        self.rsi_period = 14
        self.atr_period = 14
        self.bb_period = 20
        self.bb_std = 2.0

        # Estado da estratégia para cada símbolo (geralmente não usado
        # diretamente pela ScalpingStrategy base)
        self.state = {}
        self.signals = (
            {}
        )  # Pode ser usado para armazenar o último sinal gerado por símbolo
        self.last_update = (
            {}
        )  # Timestamp da última atualização de dados ou sinal por símbolo

        # Timestamp da última análise geral (pode ser útil para throttling ou
        # cooldown)
        self.last_analysis_time = datetime.now(timezone.utc)

        # Nota: self.sma_short_period e self.sma_long_period não são definidos aqui intencionalmente.
        # Eles devem ser fornecidos pela configuração da QUALIAEnhancedScalpingStrategy (via params)
        # ou definidos explicitamente em subclasses que os utilizem
        # diretamente.

        logger.info(
            f"Estratégia de Scalping inicializada com perfil de risco: {risk_profile}"
        )

        # Cache de indicadores calculados
        self.indicator_cache: Dict[str, Any] = {}
        self.last_indicator_calc_timestamp: Optional[pd.Timestamp] = None

    def calculate_indicators(self, ohlcv_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Calcula indicadores técnicos a partir dos dados OHLCV.

        Args:
            ohlcv_data: Dicionário com dados OHLCV (timestamps, open, high, low, close, volume)

        Returns:
            Dicionário com indicadores calculados
        """
        # Converter para pandas DataFrame para facilitar cálculos
        df = pd.DataFrame(
            {
                "timestamp": pd.to_datetime(ohlcv_data["timestamps"]),
                "open": ohlcv_data["open"],
                "high": ohlcv_data["high"],
                "low": ohlcv_data["low"],
                "close": ohlcv_data["close"],
                "volume": ohlcv_data["volume"],
            }
        )

        # Definir timestamp como índice
        df.set_index("timestamp", inplace=True)

        # Reutiliza indicadores já calculados se o timestamp não mudou
        if not df.empty:
            current_latest_timestamp = df.index[-1]
            if (
                self.last_indicator_calc_timestamp == current_latest_timestamp
                and self.indicator_cache
            ):
                logger.debug(
                    f"Usando cache de indicadores para timestamp {current_latest_timestamp}"
                )
                return self.indicator_cache
        else:
            logger.debug(
                "DataFrame de entrada para calculate_indicators vazio, não é possível usar/atualizar cache."
            )
            return {}  # Retornar vazio se não há dados para calcular

        # Inicializar resultados
        indicators = {}

        # Calcular médias móveis exponenciais (EMAs)
        for period in self.ema_periods:
            indicators[f"ema_{period}"] = (
                df["close"].ewm(span=period, adjust=False).mean().values
            )

        # Calcular VWAP (Volume-Weighted Average Price)
        df["vwap"] = (df["close"] * df["volume"]).cumsum() / df["volume"].cumsum()
        indicators["vwap"] = df["vwap"].values

        # Calcular RSI (Relative Strength Index)
        delta = df["close"].diff()
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)
        avg_gain = gain.rolling(window=self.rsi_period).mean()
        avg_loss = loss.rolling(window=self.rsi_period).mean()
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        indicators["rsi"] = rsi.fillna(50).values

        # Calcular ATR (Average True Range)
        tr1 = df["high"] - df["low"]
        tr2 = abs(df["high"] - df["close"].shift())
        tr3 = abs(df["low"] - df["close"].shift())
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = tr.rolling(window=self.atr_period).mean()
        indicators["atr"] = atr.fillna(tr.mean()).values

        # Calcular Bollinger Bands
        sma = df["close"].rolling(window=self.bb_period).mean()
        std = df["close"].rolling(window=self.bb_period).std()
        upper_band = sma + (std * self.bb_std)
        lower_band = sma - (std * self.bb_std)
        indicators["bb_upper"] = upper_band.fillna(df["close"]).values
        indicators["bb_lower"] = lower_band.fillna(df["close"]).values
        indicators["bb_middle"] = sma.fillna(df["close"]).values

        # Calcular momentum
        indicators["momentum"] = df["close"].pct_change(5).values

        # Calcular volatilidade
        indicators["volatility"] = df["close"].pct_change().rolling(
            window=20
        ).std().fillna(0).values * np.sqrt(252 * 24 * 60)

        # Calcular volume relativo
        indicators["volume_ma"] = (
            df["volume"].rolling(window=20).mean().fillna(df["volume"]).values
        )
        # Converter relative_volume para numpy array para evitar warnings ao
        # indexar Series
        indicators["relative_volume"] = (df["volume"] / indicators["volume_ma"]).values

        # Calcular distância percentual do preço à média
        for period in self.ema_periods:
            ema_col = f"ema_{period}"
            indicators[f"dist_to_{ema_col}"] = (
                (df["close"] - indicators[ema_col]) / df["close"] * 100
            ).values

        # Atualizar cache e timestamp após o cálculo
        self.indicator_cache = indicators
        if not df.empty:
            self.last_indicator_calc_timestamp = df.index[-1]
            logger.debug(
                f"Cache de indicadores atualizado para timestamp {self.last_indicator_calc_timestamp}"
            )

        return indicators

    def generate_signal(
        self, df: pd.DataFrame, quantum_metrics: Optional[Dict[str, Any]] = None
    ) -> Tuple[str, float]:
        """
        Gera um sinal de trading com base nos dados do mercado.
        Quando disponíveis, métricas quânticas modulam o resultado.
        """
        if df.empty:
            logger.warning(
                "DataFrame vazio fornecido para generate_signal. Retornando HOLD."
            )
            return "hold", 0.5

        # Implementação do modo piloto simples baseado em RSI
        if self.simple_pilot_mode:
            # Calcular RSI
            delta = df["close"].diff()
            gain = delta.where(delta > 0, 0)
            loss = -delta.where(delta < 0, 0)

            # Usar ewm para média móvel exponencial que é mais comum para RSI
            # do que rolling.mean
            avg_gain = gain.ewm(
                com=self.simple_rsi_period - 1, min_periods=self.simple_rsi_period
            ).mean()
            avg_loss = loss.ewm(
                com=self.simple_rsi_period - 1, min_periods=self.simple_rsi_period
            ).mean()

            # Evitar divisão por zero se não houver perdas no período
            if avg_loss.iloc[-1] == 0:
                # Se há ganhos, RS infinito, senão 0
                rs = float("inf") if avg_gain.iloc[-1] > 0 else 0
            else:
                rs = avg_gain.iloc[-1] / avg_loss.iloc[-1]

            rsi = 100 - (100 / (1 + rs))
            # Usar 50 se RSI for NaN (ocorre no início)
            rsi_value = rsi if pd.notna(rsi) else 50

            logger.debug(
                f"Modo Piloto Simples: RSI({self.simple_rsi_period}) = {rsi_value:.2f}"
            )

            if rsi_value < self.simple_rsi_threshold_buy:
                logger.info(
                    f"Sinal Piloto: BUY (RSI {rsi_value:.2f} < {self.simple_rsi_threshold_buy})"
                )
                return "buy", self.simple_pilot_confidence
            elif rsi_value > self.simple_rsi_threshold_sell:
                logger.info(
                    f"Sinal Piloto: SELL (RSI {rsi_value:.2f} > {self.simple_rsi_threshold_sell})"
                )
                return "sell", self.simple_pilot_confidence
            else:
                return "hold", 0.5  # Confiança base para HOLD

        # Usar o algoritmo de SMA para gerar o sinal inicial
        # Garantir que os períodos de SMA estejam definidos (devem vir de
        # QUALIAEnhancedScalpingStrategy)
        if not hasattr(self, "sma_short_period") or not hasattr(
            self, "sma_long_period"
        ):
            logger.error(
                "Períodos de SMA (sma_short_period, sma_long_period) não definidos na estratégia. Retornando HOLD."
            )
            return "hold", 0.1  # Confiança muito baixa devido a erro de config

        sma_short = df["close"].rolling(self.sma_short_period).mean()
        sma_long = df["close"].rolling(self.sma_long_period).mean()

        current_price = df["close"].iloc[-1]
        signal = "hold"
        base_confidence = 0.5

        # Lógica básica de cruzamento de SMA para o sinal inicial
        if (
            sma_short.iloc[-1] > sma_long.iloc[-1]
            and sma_short.iloc[-2] <= sma_long.iloc[-2]
        ):
            signal = "buy"
            base_confidence = 0.6
        elif (
            sma_short.iloc[-1] < sma_long.iloc[-1]
            and sma_short.iloc[-2] >= sma_long.iloc[-2]
        ):
            signal = "sell"
            base_confidence = 0.6
        else:
            # Se não houver cruzamento, o sinal ainda pode ser BUY ou SELL
            # dependendo da posição relativa
            if sma_short.iloc[-1] > sma_long.iloc[-1]:
                # Tendência de alta continua
                distance = (sma_short.iloc[-1] - sma_long.iloc[-1]) / current_price
                if distance > 0.01:  # Se a distância for significativa
                    signal = "buy"
                    base_confidence = 0.5 + min(distance * 10, 0.4)  # Limita a 0.9
            elif sma_short.iloc[-1] < sma_long.iloc[-1]:
                # Tendência de baixa continua
                distance = (sma_long.iloc[-1] - sma_short.iloc[-1]) / current_price
                if distance > 0.01:  # Se a distância for significativa
                    signal = "sell"
                    base_confidence = 0.5 + min(distance * 10, 0.4)  # Limita a 0.9

        # Agora, integra as métricas quânticas para ajustar o sinal e a
        # confiança
        final_confidence = base_confidence
        qm_influence = {}
        # Se as métricas quânticas estiverem disponíveis e devemos usá-las na
        # decisão
        if self.use_quantum_metrics and quantum_metrics:
            # Métricas de interesse
            coherence = quantum_metrics.get("quantum_coherence", [])
            entropy = quantum_metrics.get("quantum_entropy", [])
            otoc = quantum_metrics.get("otoc", [])
            last_measurement = quantum_metrics.get("last_measurement_counts", {})

            # Calcular a proporção de medições que resultaram em '1' vs '0'
            measurement_ratio = 0.5  # Valor padrão equilibrado
            if last_measurement:
                count_0 = last_measurement.get("0", 0)
                count_1 = last_measurement.get("1", 0)
                total_count = count_0 + count_1
                if total_count > 0:
                    measurement_ratio = count_1 / total_count

            # Ajuste 1: A medição quântica indica tendência direta
            if measurement_ratio > 0.6:  # Mais medições de '1'
                qm_influence["measurement"] = 0.1  # Viés para BUY
                if signal == "sell":
                    # Reduz confiança se contradiz
                    qm_influence["measurement"] = -0.05
            elif measurement_ratio < 0.4:  # Mais medições de '0'
                qm_influence["measurement"] = -0.1  # Viés para SELL
                if signal == "buy":
                    # Reduz confiança se contradiz
                    qm_influence["measurement"] = -0.05

            # Ajuste 2: O valor de OTOC indica instabilidade/caos no mercado
            if otoc and len(otoc) > 0:
                latest_otoc = otoc[-1]
                if latest_otoc < 0.8:  # Alto caos/instabilidade
                    # Reduz confiança em qualquer sinal
                    qm_influence["otoc"] = -0.15
                elif latest_otoc > 0.95:  # Baixo caos/alta previsibilidade
                    qm_influence["otoc"] = 0.1  # Aumenta confiança

            # Ajuste 3: Coerência quântica indica clareza da tendência
            if coherence and len(coherence) > 0:
                latest_coherence = coherence[-1]
                if latest_coherence > 0.8:  # Alta coerência
                    qm_influence["coherence"] = 0.1  # Aumenta confiança
                elif latest_coherence < 0.4:  # Baixa coerência
                    qm_influence["coherence"] = -0.1  # Diminui confiança

            # Aplicar os ajustes de influência quântica
            qm_adjustment = sum(qm_influence.values())

            # Potencial mudança de sinal em casos extremos
            if base_confidence < 0.55 and qm_adjustment > 0.2 and signal == "hold":
                # O sinal é fraco, mas métricas quânticas mostram forte
                # potencial
                signal = "buy" if measurement_ratio > 0.6 else "sell"

            # Ajustar a confiança final
            final_confidence = min(max(base_confidence + qm_adjustment, 0.1), 0.95)

            logger.debug(
                f"Ajuste quântico aplicado: {qm_influence}, Confiança base: {base_confidence:.2f}, Confiança final: {final_confidence:.2f}"
            )

        # Teste de QPM - depuração - usar um sinal específico
        if self.qpm_test_mode_sma_override and self.force_qpm_test_signal_type:
            signal = self.force_qpm_test_signal_type
            final_confidence = 0.75  # Confiança arbitrária para testes
            logger.debug(f"QPM Test Mode ativado. Usando sinal forçado: {signal}")

        return signal, final_confidence


@register_strategy()
class QUALIAEnhancedScalpingStrategy(ScalpingStrategy):
    """
    Estratégia de scalping avançada que integra o ciclo QAST e métricas quânticas.
    """

    strategy_alias = "qualia_enhanced_scalping"

    def __init__(
        self,
        symbol: str,
        timeframe: str,
        params: Optional[Union[EnhancedScalpingParams, Dict[str, Any]]] = None,
        risk_profile_settings: Optional[Dict[str, Any]] = None,
    ):
        """
        Inicializa a estratégia de scalping avançada QUALIA.

        Args:
            symbol: Símbolo de trading (ex: "BTC/USD").
            timeframe: Timeframe da estratégia (ex: "5m").
            params: Dicionário de parâmetros específicos da estratégia, incluindo os da ScalpingStrategy base.
            risk_profile_settings: Configurações de perfil de risco globais.
        """
        if params is None:
            params_obj = EnhancedScalpingParams()
        elif is_dataclass(params):
            params_obj = params  # type: ignore[assignment]
        elif isinstance(params, dict):
            params_obj = EnhancedScalpingParams(**params)
        else:
            raise TypeError("params must be a dict or EnhancedScalpingParams")

        effective_params = asdict(params_obj)

        # Extrair parâmetros para ScalpingStrategy base e para
        # QUALIAEnhancedScalpingStrategy
        risk_profile = effective_params.get("risk_profile", "balanced")
        use_quantum_metrics = effective_params.get("use_quantum_metrics", True)

        # Parâmetros do modo piloto extraídos de ``effective_params``
        simple_pilot_mode = effective_params.get("simple_pilot_mode", False)
        simple_rsi_period = effective_params.get("simple_rsi_period", 14)
        simple_rsi_threshold_buy = effective_params.get("simple_rsi_threshold_buy", 30)
        simple_rsi_threshold_sell = effective_params.get(
            "simple_rsi_threshold_sell", 70
        )
        simple_pilot_confidence = effective_params.get("simple_pilot_confidence", 0.7)

        # Chamar o __init__ da classe base (ScalpingStrategy)
        super().__init__(
            risk_profile=risk_profile,
            use_quantum_metrics=use_quantum_metrics,
            simple_pilot_mode=simple_pilot_mode,
            simple_rsi_period=simple_rsi_period,
            simple_rsi_threshold_buy=simple_rsi_threshold_buy,
            simple_rsi_threshold_sell=simple_rsi_threshold_sell,
            simple_pilot_confidence=simple_pilot_confidence,
        )

        self.name = f"QUALIARKI_{symbol.replace('/', '_')}_{timeframe}"
        self.version = "1.0.0-qast-enhanced"
        self.symbol = symbol
        self.timeframe = timeframe
        self.strategy_params = effective_params
        self.params_obj = params_obj
        self.risk_profile_settings = (
            risk_profile_settings if risk_profile_settings is not None else {}
        )

        # Popular atributos em ``self`` a partir de ``self.strategy_params``
        # para que os métodos da classe base os utilizem.
        configurable_risk_params = [
            "profit_target_pct",
            "stop_loss_pct",
            "max_position_size",
            "min_volatility",
            "max_spread_pct",
            "mean_reversion_threshold",
            "momentum_threshold",
            "min_volume_percentile",
        ]
        for param_name in configurable_risk_params:
            if param_name in self.strategy_params:
                setattr(self, param_name, self.strategy_params[param_name])

        indicator_params_mapping = {
            "ema_periods_list": "ema_periods",
            "rsi_period_val": "rsi_period",
            "atr_period_val": "atr_period",
            "bb_period_val": "bb_period",
            "bb_std_val": "bb_std",
            "sma_short_period_val": "sma_short_period",
            "sma_long_period_val": "sma_long_period",
        }
        for key_in_params, attr_name_in_self in indicator_params_mapping.items():
            if key_in_params in self.strategy_params:
                setattr(self, attr_name_in_self, self.strategy_params[key_in_params])
            elif not hasattr(self, attr_name_in_self):
                if attr_name_in_self == "sma_short_period":
                    self.sma_short_period = 10
                if attr_name_in_self == "sma_long_period":
                    self.sma_long_period = 20

        # Configura Stop-Loss adaptativo por ATR
        self.atr_sl_multiplier_k = effective_params.get(
            "atr_sl_multiplier_k", 0.0
        )  # Default 0.0 para desabilitar ATR SL
        self.atr_period = getattr(
            self, "atr_period", 14
        )  # Garante que atr_period esteja definido (já deve estar por mapeamento)
        if self.atr_sl_multiplier_k > 0:
            logger.info(
                f"{self.name}: Stop-loss adaptativo por ATR ATIVADO. Multiplicador k = {self.atr_sl_multiplier_k}, Período ATR = {self.atr_period}"
            )
        else:
            logger.info(
                f"{self.name}: Stop-loss adaptativo por ATR DESABILITADO (k={self.atr_sl_multiplier_k}). Usará stop_loss_pct."
            )

        logger.info(f"{self.name} inicializada para {symbol} em {timeframe}.")
        logger.debug(
            f"[DEBUG_PARAMS] {self.name}.__init__: self.strategy_params = {self.strategy_params}"
        )
        logger.debug(
            f"[DEBUG_PARAMS] {self.name}.__init__: Atributos relevantes para ScalpingStrategy: "
            f"sma_short_period={getattr(self, 'sma_short_period', 'N/A')}, "
            f"sma_long_period={getattr(self, 'sma_long_period', 'N/A')}, "
            f"rsi_period={getattr(self, 'rsi_period', 'N/A')}"
        )

    def update_parameters(self, new_params: Dict[str, Any]) -> None:
        """Update internal parameters without resetting runtime state."""
        if not isinstance(new_params, dict):
            raise TypeError("new_params must be a dict")

        logger.info(f"{self.name}: Atualizando parâmetros: {new_params}")

        self.strategy_params.update(new_params)
        self.params_obj = EnhancedScalpingParams(**self.strategy_params)
        effective_params = asdict(self.params_obj)

        for key, value in effective_params.items():
            setattr(self, key, value)

        self.params = self.strategy_params

        logger.debug(
            f"[DEBUG_PARAMS] {self.name}.update_parameters: self.strategy_params = {self.strategy_params}"
        )

    def get_params(self) -> Dict[str, Any]:
        """Return current parameters for this strategy."""

        return getattr(self, "strategy_params", {})

    def set_params(self, params: Dict[str, Any]) -> None:
        """Alias for ``update_parameters``."""

        self.update_parameters(params)

    def backtest(
        self,
        market_data: pd.DataFrame,
        initial_capital: float = 10000.0,
        risk_per_trade_capital_pct: float = 0.01,
    ) -> Dict[str, Any]:
        """
        Executa um backtest da estratégia usando os dados de mercado fornecidos.

        Args:
            market_data: DataFrame com colunas ['timestamp', 'open', 'high', 'low', 'close', 'volume'].
                         O timestamp deve ser o índice.
            initial_capital: Capital inicial para o backtest.
            risk_per_trade_capital_pct: Percentual do capital a arriscar por trade para definir o tamanho.

        Returns:
            Dicionário com métricas de performance do backtest.
        """
        if not isinstance(market_data.index, pd.DatetimeIndex):
            if "timestamp" in market_data.columns:
                market_data["timestamp"] = pd.to_datetime(market_data["timestamp"])
                market_data = market_data.set_index("timestamp")
            else:
                logger.error(
                    f"{self.name}: market_data deve ter um DatetimeIndex ou uma coluna 'timestamp'."
                )
                raise ValueError(
                    "market_data deve ter um DatetimeIndex ou uma coluna 'timestamp'."
                )

        required_columns = ["open", "high", "low", "close", "volume"]
        if not all(col in market_data.columns for col in required_columns):
            logger.error(
                f"{self.name}: market_data não contém todas as colunas necessárias: {required_columns}"
            )
            # Retornar métricas vazias ou levantar erro
            return {
                "pnl_history_pct": [],
                "sharpe_ratio": 0.0,
                "max_drawdown": 0.0,
                "total_pnl_pct": 0.0,
                "num_trades": 0,
                "win_rate": 0.0,
                "avg_trade_pnl_pct": 0.0,
                "trades_details": [],
            }

        min_data_len = getattr(self, "sma_long_period", 20) + 5  # Adicionar uma margem
        if market_data.empty or len(market_data) < min_data_len:
            logger.warning(
                f"{self.name}: Dados de mercado insuficientes para backtest ({len(market_data)} candles, precisa de aprox. {min_data_len}). Retornando métricas vazias."
            )
            return {
                "pnl_history_pct": [],
                "sharpe_ratio": 0.0,
                "max_drawdown": 0.0,
                "total_pnl_pct": 0.0,
                "num_trades": 0,
                "win_rate": 0.0,
                "avg_trade_pnl_pct": 0.0,
                "trades_details": [],
            }

        ohlcv_dict_for_indicators = {
            "timestamps": market_data.index.tolist(),
            "open": market_data["open"].values,
            "high": market_data["high"].values,
            "low": market_data["low"].values,
            "close": market_data["close"].values,
            "volume": market_data["volume"].values,
        }
        indicators_df_part = super().calculate_indicators(ohlcv_dict_for_indicators)

        # Criar um DataFrame para os indicadores com o mesmo índice que
        # market_data
        indicators_df = pd.DataFrame(index=market_data.index)
        for key, val_array in indicators_df_part.items():
            # Alinhar arrays de indicadores ao DataFrame principal, preenchendo
            # NaNs no início se necessário
            if len(val_array) < len(market_data):
                padding = [np.nan] * (len(market_data) - len(val_array))
                indicators_df[key] = padding + list(val_array)
            else:
                # Truncar se for mais longo
                indicators_df[key] = val_array[: len(market_data)]

        # Combinar dados de mercado originais com indicadores
        # Usar pd.concat para evitar problemas de alinhamento e manter o tipo
        # de índice original
        data_with_indicators = pd.concat([market_data, indicators_df], axis=1)

        capital = initial_capital
        peak_capital = initial_capital
        max_drawdown_pct = 0.0
        active_position = None
        entry_price = 0.0
        position_size_asset = 0.0
        trade_pnl_percentages = []
        winning_trades = 0
        losing_trades = 0
        stop_loss_pct_val = getattr(self, "stop_loss_pct", 0.02)
        profit_target_pct_val = getattr(self, "profit_target_pct", 0.04)
        trades_details = []  # Detalhes dos trades utilizados pela QPM

        # Loop de backtesting principal
        # Garantir que os períodos de SMA estejam disponíveis
        sma_short_p = getattr(self, "sma_short_period", 10)
        sma_long_p = getattr(self, "sma_long_period", 20)
        # Iniciar após o aquecimento dos indicadores SMA
        start_index = max(sma_short_p, sma_long_p)

        for i in range(start_index, len(data_with_indicators)):
            current_row = data_with_indicators.iloc[i]
            current_price = current_row["close"]

            if pd.isna(current_price):
                continue  # Pular se o preço atual for NaN

            # Variáveis para armazenar informações do trade para QPM
            entry_timestamp = None
            active_position_side_for_log = None
            signal_at_entry_for_log = None
            confidence_at_entry_for_log = None

            # Lógica de Stop Loss / Take Profit
            if active_position:
                pnl_ratio = (
                    (current_price - entry_price) / entry_price
                    if entry_price != 0
                    else 0
                )
                hit_sl_tp = False
                exit_reason = ""

                if active_position == "buy":
                    if current_price <= entry_price * (1 - stop_loss_pct_val):
                        exit_price = entry_price * (1 - stop_loss_pct_val)
                        hit_sl_tp = True
                        exit_reason = "SL"
                    elif current_price >= entry_price * (1 + profit_target_pct_val):
                        exit_price = entry_price * (1 + profit_target_pct_val)
                        hit_sl_tp = True
                        exit_reason = "TP"
                elif active_position == "sell":
                    if current_price >= entry_price * (1 + stop_loss_pct_val):
                        exit_price = entry_price * (1 + stop_loss_pct_val)
                        hit_sl_tp = True
                        exit_reason = "SL"
                    elif current_price <= entry_price * (1 - profit_target_pct_val):
                        exit_price = entry_price * (1 - profit_target_pct_val)
                        hit_sl_tp = True
                        exit_reason = "TP"

                if hit_sl_tp:
                    trade_pnl = (
                        (exit_price - entry_price) * position_size_asset
                        if active_position == "buy"
                        else (entry_price - exit_price) * position_size_asset
                    )
                    capital += trade_pnl
                    cost_basis = entry_price * position_size_asset
                    trade_pnl_pct = (
                        (trade_pnl / cost_basis) * 100 if cost_basis != 0 else 0
                    )
                    trade_pnl_percentages.append(trade_pnl_pct)
                    if trade_pnl > 0:
                        winning_trades += 1
                    else:
                        losing_trades += 1

                    # Registrar detalhes do trade quando SL ou TP for atingido
                    trades_details.append(
                        {
                            "entry_timestamp": entry_timestamp,  # Precisa ser definido no início do trade
                            "exit_timestamp": current_row.name,  # Timestamp do candle de saída
                            "entry_price": entry_price,
                            "exit_price": exit_price,
                            # Precisa ser definido no início do trade
                            "position_side": active_position_side_for_log,
                            "pnl_abs": trade_pnl,
                            "pnl_pct": trade_pnl_pct,
                            "exit_reason": exit_reason,  # SL ou TP
                            # Precisa ser definido no início do trade
                            "signal_at_entry": signal_at_entry_for_log,
                            # Precisa ser definido no início do trade
                            "confidence_at_entry": confidence_at_entry_for_log,
                        }
                    )
                    active_position = None  # Resetar posição após SL/TP
                    continue

            # Gerar Sinal (usando o método da classe mãe)
            # generate_signal precisa de um df que represente o histórico até o ponto atual para calcular SMAs, etc.
            # Passamos data_with_indicators até a linha i (inclusive)
            signal_df_slice = data_with_indicators.iloc[: i + 1]
            signal, confidence = super().generate_signal(
                df=signal_df_slice, quantum_metrics=None
            )

            if signal == "buy" and not active_position:
                active_position = "buy"
                entry_price = current_price
                # Simplificado: usar 10% do capital
                position_size_asset = (capital * 0.1) / entry_price
                if position_size_asset * entry_price > capital:
                    position_size_asset = 0
                # Registrar informações para QPM
                entry_timestamp = current_row.name
                active_position_side_for_log = "buy"
                signal_at_entry_for_log = signal  # O sinal que abriu a posição
                confidence_at_entry_for_log = (
                    confidence  # A confiança no momento da entrada
                )

            elif signal == "sell" and not active_position:
                active_position = "sell"
                entry_price = current_price
                position_size_asset = (capital * 0.1) / entry_price
                if position_size_asset * entry_price > capital:
                    position_size_asset = 0
                # Registrar informações para QPM
                entry_timestamp = current_row.name
                active_position_side_for_log = "sell"
                signal_at_entry_for_log = signal
                confidence_at_entry_for_log = confidence

            elif (
                signal == "CLOSE"
                or (signal == "buy" and active_position == "sell")
                or (signal == "sell" and active_position == "buy")
            ) and active_position:
                exit_price = current_price
                trade_pnl = (
                    (exit_price - entry_price) * position_size_asset
                    if active_position == "buy"
                    else (entry_price - exit_price) * position_size_asset
                )
                capital += trade_pnl
                cost_basis = entry_price * position_size_asset
                trade_pnl_pct = (trade_pnl / cost_basis) * 100 if cost_basis != 0 else 0
                trade_pnl_percentages.append(trade_pnl_pct)
                if trade_pnl > 0:
                    winning_trades += 1
                else:
                    losing_trades += 1

                # Registrar detalhes do trade quando fechado por sinal
                trades_details.append(
                    {
                        # entry_timestamp precisa ser capturado e persistido
                        "entry_timestamp": entry_timestamp,
                        "exit_timestamp": current_row.name,
                        "entry_price": entry_price,  # entry_price precisa ser capturado e persistido
                        "exit_price": exit_price,
                        # active_position_side_for_log capturado
                        "position_side": active_position_side_for_log,
                        "pnl_abs": trade_pnl,
                        "pnl_pct": trade_pnl_pct,
                        "exit_reason": "signal_close",  # Sinal de fechamento explícito ou reversão
                        "signal_at_entry": signal_at_entry_for_log,  # signal_at_entry_for_log capturado
                        # confidence_at_entry_for_log capturado
                        "confidence_at_entry": confidence_at_entry_for_log,
                        "signal_at_exit": signal,  # O sinal que fechou a posição
                    }
                )
                active_position = None

            if capital > peak_capital:
                peak_capital = capital
            drawdown = (
                (peak_capital - capital) / peak_capital if peak_capital > 0 else 0
            )
            if drawdown > max_drawdown_pct:
                max_drawdown_pct = drawdown

        num_trades = len(trade_pnl_percentages)
        total_pnl_pct_overall = (
            ((capital - initial_capital) / initial_capital) * 100
            if initial_capital > 0
            else 0.0
        )
        win_rate_val = (winning_trades / num_trades) * 100 if num_trades > 0 else 0.0
        avg_trade_pnl_pct_val = (
            np.mean(trade_pnl_percentages) if num_trades > 0 else 0.0
        )
        sharpe_ratio_val = 0.0
        if num_trades > 1 and np.std(trade_pnl_percentages) != 0:
            mean_trade_return = np.mean(trade_pnl_percentages) / 100
            std_trade_return = np.std(trade_pnl_percentages) / 100
            # Simplificado: sqrt(num_trades) como fator de "anualização" para
            # QAST.
            sharpe_ratio_val = (
                (mean_trade_return / std_trade_return) * np.sqrt(num_trades)
                if std_trade_return != 0
                else 0.0
            )

        return {
            "pnl_history_pct": trade_pnl_percentages,
            "sharpe_ratio": sharpe_ratio_val if not np.isnan(sharpe_ratio_val) else 0.0,
            "max_drawdown": max_drawdown_pct * 100,
            "total_pnl_pct": total_pnl_pct_overall,
            "num_trades": num_trades,
            "win_rate": win_rate_val,
            "avg_trade_pnl_pct": avg_trade_pnl_pct_val,
            "final_capital": capital,
            "trades_details": trades_details,
        }

    def analyze(
        self,
        data: pd.DataFrame,
        quantum_metrics: Optional[Dict[str, Any]] = None,
        context: Optional[TradeContext] = None,
        similar_past_patterns: Optional[List[Dict[str, Any]]] = None,
    ) -> Dict[str, Any]:
        """
        Analisa os dados de mercado para gerar um sinal de trading.
        Esta implementação processa métricas quânticas e padrões similares quando disponíveis.
        """
        logger.debug(
            f"{self.name}: Iniciando análise com dados de {len(data)} candles. Contexto fornecido: {context is not None}"
        )

        # Prepara o contexto para o ScalpingDecision
        current_price = data["close"].iloc[-1]
        current_time = (
            data.index[-1]
            if isinstance(data.index, pd.DatetimeIndex)
            else datetime.now(timezone.utc)
        )

        # Preparar "qpm_insights" para passar ao ScalpingDecision
        qpm_insights_for_decision = {}
        MIN_SAMPLES_QPM = 3  # Mínimo de padrões para QPM influenciar ativamente

        if similar_past_patterns and len(similar_past_patterns) > 0:
            pnl_perc_values = []
            for p_item in similar_past_patterns:
                outcome = p_item.get("pattern", {}).get("outcome", {})
                if outcome:
                    pnl_perc = outcome.get("pnl_percentage")
                    if pnl_perc is not None and isinstance(pnl_perc, (int, float)):
                        pnl_perc_values.append(pnl_perc)

            avg_pnl_perc = np.mean(pnl_perc_values) if pnl_perc_values else 0.0

            # Condição para QPM influenciar ativamente a decisão
            if len(similar_past_patterns) >= MIN_SAMPLES_QPM and avg_pnl_perc > 0:
                predicted_directions = [
                    p.get("pattern", {})
                    .get("outcome", {})
                    .get("final_signal_before_close", "hold")
                    for p in similar_past_patterns
                    if p.get("pattern", {}).get("outcome")
                ]
                if not any(d != "hold" for d in predicted_directions):
                    predicted_directions = [
                        p.get("pattern", {})
                        .get("outcome", {})
                        .get("side", "hold")
                        .upper()
                        for p in similar_past_patterns
                        if p.get("pattern", {}).get("outcome")
                    ]

                direction_counts = Counter(
                    d for d in predicted_directions if d in ["buy", "sell", "hold"]
                )
                if direction_counts:
                    if direction_counts.get("buy", 0) == direction_counts.get(
                        "sell", 0
                    ) and direction_counts.get("buy", 0) > direction_counts.get(
                        "hold", 0
                    ):
                        dominant_direction = "hold"
                    else:
                        dominant_direction = direction_counts.most_common(1)[0][0]
                    direction_confidence = (
                        direction_counts[dominant_direction] / len(predicted_directions)
                        if predicted_directions
                        else 0.0
                    )
                else:
                    dominant_direction = "hold"
                    direction_confidence = 0.0

                pnl_values_abs = [
                    p.get("pattern", {}).get("outcome", {}).get("pnl", 0.0)
                    for p in similar_past_patterns
                    if p.get("pattern", {}).get("outcome")
                    and isinstance(p["pattern"]["outcome"].get("pnl"), (int, float))
                ]
                avg_pnl_abs = np.mean(pnl_values_abs) if pnl_values_abs else 0.0

                qpm_insights_for_decision = {
                    "dominant_direction": dominant_direction,
                    "direction_confidence": direction_confidence,
                    "avg_pnl": avg_pnl_abs,
                    "avg_pnl_percentage": avg_pnl_perc,
                    "similar_patterns_count": len(similar_past_patterns),
                    "qpm_override_active": True,
                }
                logger.debug(
                    f"QPM insights (ATIVO - Condições atendidas): {qpm_insights_for_decision}"
                )
            else:
                # Condições não atendidas, QPM não força sinal, mas pode
                # fornecer info
                avg_pnl_abs_fallback = 0.0
                if (
                    similar_past_patterns
                ):  # Calcular PNL abs médio apenas se houver padrões
                    pnl_values_abs_fallback = [
                        p.get("pattern", {}).get("outcome", {}).get("pnl", 0.0)
                        for p in similar_past_patterns
                        if p.get("pattern", {}).get("outcome")
                        and isinstance(p["pattern"]["outcome"].get("pnl"), (int, float))
                    ]
                    avg_pnl_abs_fallback = (
                        np.mean(pnl_values_abs_fallback)
                        if pnl_values_abs_fallback
                        else 0.0
                    )

                qpm_insights_for_decision = {
                    "dominant_direction": "hold",  # Neutro
                    "direction_confidence": 0.0,
                    "avg_pnl": avg_pnl_abs_fallback,
                    # Logar o PNL perc médio mesmo se <0 ou poucos samples
                    "avg_pnl_percentage": avg_pnl_perc,
                    "similar_patterns_count": (
                        len(similar_past_patterns) if similar_past_patterns else 0
                    ),
                    "qpm_override_active": False,
                }
                logger.debug(
                    f"QPM insights (INATIVO - Condições não atendidas: {len(similar_past_patterns) if similar_past_patterns else 0} padrões, Avg PNL Perc: {avg_pnl_perc:.4f}): {qpm_insights_for_decision}"
                )
        else:  # Nenhum padrão similar encontrado
            qpm_insights_for_decision = {
                "dominant_direction": "hold",
                "direction_confidence": 0.0,
                "avg_pnl": 0.0,
                "avg_pnl_percentage": 0.0,
                "similar_patterns_count": 0,
                "qpm_override_active": False,
            }
            logger.debug(
                f"QPM insights (INATIVO - Nenhum padrão similar encontrado): {qpm_insights_for_decision}"
            )

        # Calcula ATR para a decisão caso o stop-loss adaptativo esteja ativado
        atr_value_for_decision: Optional[float] = None
        if self.atr_sl_multiplier_k > 0 and not data.empty:
            # Converter DataFrame para o formato esperado por calculate_indicators
            # (timestamps como lista, ohlcv como arrays numpy)
            # Nota: idealmente, calculate_indicators aceitaria um DataFrame diretamente.
            if not isinstance(data.index, pd.DatetimeIndex):
                logger.warning(
                    f"{self.name}: Índice de dados não é DatetimeIndex. Não é possível calcular indicadores dependentes de tempo de forma confiável."
                )
                # Pode ser necessário um tratamento de erro mais robusto aqui

            ohlcv_dict_for_atr = {
                "timestamps": (
                    data.index.tolist()
                    if isinstance(data.index, pd.DatetimeIndex)
                    else list(range(len(data)))
                ),
                "open": data["open"].values,
                "high": data["high"].values,
                "low": data["low"].values,
                "close": data["close"].values,
                "volume": (
                    data["volume"].values
                    if "volume" in data.columns
                    else np.zeros(len(data))
                ),
            }
            try:
                # Chama o método da classe base ScalpingStrategy
                calculated_indicators = super().calculate_indicators(ohlcv_dict_for_atr)
                atr_series = calculated_indicators.get("atr")
                if atr_series is not None and len(atr_series) > 0:
                    atr_value_for_decision = atr_series[
                        -1
                    ]  # Pega o último valor do ATR
                    if pd.isna(atr_value_for_decision):
                        logger.warning(
                            f"{self.name}: Último valor do ATR calculado é NaN. SL por ATR pode não funcionar."
                        )
                        atr_value_for_decision = None  # Tratar como None se for NaN
                    else:
                        logger.debug(
                            f"{self.name}: ATR calculado para decisão: {atr_value_for_decision:.4f}"
                        )
                else:
                    logger.warning(
                        f"{self.name}: Série ATR não retornada ou vazia por calculate_indicators."
                    )
            except Exception as e_atr:
                logger.error(
                    f"{self.name}: Erro ao calcular indicadores (ATR) em analyze(): {e_atr}",
                    exc_info=True,
                )

        # Instancia o ``ScalpingDecision`` passando contexto e as
        # informações calculadas, incluindo ATR quando o SL adaptativo está ativo
        decision_engine = ScalpingDecision(
            symbol=self.symbol,
            timeframe=self.timeframe,
            ohlcv_data=data,
            current_price=current_price,
            trade_context=context,  # Passa o TradeContext completo
            strategy_params=self.strategy_params,  # Parâmetros gerais da estratégia
            risk_profile_settings=self.risk_profile_settings,
            qpm_insights=qpm_insights_for_decision,
            atr_value=atr_value_for_decision,
            atr_sl_multiplier_k=self.atr_sl_multiplier_k,
        )

        # A lógica de decisão é encapsulada em ``ScalpingDecision`` e os
        # atributos (signal, confidence, stop_loss, take_profit, reasons)
        # são definidos em seu ``__init__``.

        # Montagem do resultado preliminar da ``ScalpingDecision``
        result_from_decision_engine = {
            "signal": decision_engine.signal,
            "confidence": decision_engine.confidence,
            "stop_loss": decision_engine.stop_loss,
            "take_profit": decision_engine.take_profit,
            # Garantir que é uma lista copiável
            "reasons": list(decision_engine.reasons),
            "quantum_influence": (
                True
                if (
                    quantum_metrics
                    and self.strategy_params.get(
                        "use_quantum_metrics_in_decision", False
                    )
                )
                else False
            ),
            "timestamp": current_time,
        }

        # Verifica o spread antes de retornar o resultado final usando
        # ``self.params`` (parametros da QUALIAEnhancedScalpingStrategy) e
        # ``current_price`` do contexto.
        if (
            result_from_decision_engine["signal"] in ["buy", "sell"]
            and context
            and context.current_bid is not None
            and context.current_ask is not None
            and context.current_price is not None
            and context.current_price > 0
        ):
            current_bid_val = context.current_bid
            current_ask_val = context.current_ask

            if current_ask_val > current_bid_val:  # Assegurar ask > bid
                spread_abs = current_ask_val - current_bid_val
                # Usar context.current_price para calcular spread percentual,
                # pois é o preço de referência do candle atual
                spread_pct = spread_abs / context.current_price

                # Obter max_spread_pct dos parâmetros da estratégia, com um default.
                # self.params em QUALIAEnhancedScalpingStrategy contém os parâmetros passados no __init__.
                # O valor default de max_spread_pct na ScalpingStrategy base é 0.1 para balanced.
                # A sugestão do usuário é subir para 0.2% (0.002)
                max_spread_config = self.params.get(
                    "max_spread_pct", 0.001
                )  # Default se não estiver em params

                logger.debug(
                    f"{self.name}: Verificando spread para {self.symbol}. Bid: {current_bid_val}, Ask: {current_ask_val}, Price: {context.current_price}, Spread_pct: {spread_pct:.4f}, Max_spread_pct_config: {max_spread_config:.4f}"
                )

                if spread_pct > max_spread_config:
                    logger.info(
                        f"{self.name}: Spread ({spread_pct*100:.3f}%) excede max_spread_pct ({max_spread_config*100:.3f}%) para {self.symbol}. Sinal alterado de {result_from_decision_engine['signal']} para HOLD."
                    )
                    result_from_decision_engine["signal"] = "hold"
                    # Reduzir confiança e adicionar razão se o spread for muito
                    # alto
                    result_from_decision_engine["confidence"] = min(
                        result_from_decision_engine.get("confidence", 0.5) * 0.5, 0.3
                    )
                    result_from_decision_engine["reasons"].append(
                        f"Spread alto ({spread_pct*100:.3f}% > {max_spread_config*100:.3f}%)"
                    )
        else:
            logger.warning(
                f"{self.name}: Ask ({current_ask_val}) não é maior que Bid ({current_bid_val}) para {self.symbol}. Não foi possível verificar spread."
            )

        logger.info(
            f"{self.name}: Decisão final da análise: {result_from_decision_engine['signal']} SL: {result_from_decision_engine['stop_loss']} TP: {result_from_decision_engine['take_profit']}"
        )
        return result_from_decision_engine

    def analyze_market(
        self,
        market_data: pd.DataFrame,
        trading_context: TradeContext,
        quantum_metrics: Optional[Dict[str, Any]] = None,
        similar_past_patterns: Optional[List[Dict[str, Any]]] = None,
        *,
        context: TradeContext | None = None,
    ) -> Dict[str, Any]:
        """Wrapper compatível com :class:`TradingStrategy`.

        Accepts ``context`` as a deprecated alias for ``trading_context``.
        """

        actual_context = trading_context or context
        return self.analyze(
            market_data,
            quantum_metrics=quantum_metrics,
            context=actual_context,
            similar_past_patterns=similar_past_patterns,
        )

    def get_core_strategy_parameters(self) -> Dict[str, Any]:
        """Return the main scalping parameters currently in use.

        The returned dictionary includes:
            - ``risk_profile``: Profile defining the overall risk posture.
            - ``use_quantum_metrics``: Whether quantum metrics influence the
              strategy.
            - ``profit_target_pct``: Target percentage gain for each trade.
            - ``stop_loss_pct``: Percentage loss tolerated before exiting.
            - ``max_position_size``: Maximum position size as fraction of
              capital.
            - ``min_volatility``: Minimum annualized volatility required to
              trade.
            - ``max_spread_pct``: Maximum allowed bid/ask spread percentage.
            - ``mean_reversion_threshold``: Z-score threshold for mean
              reversion setups.
            - ``momentum_threshold``: Threshold used in the momentum filter.
            - ``min_volume_percentile``: Minimum volume percentile for entries.
            - ``vwap_periods``: Lookback periods used to compute VWAP.
            - ``ema_periods``: List of periods for exponential moving averages.
            - ``rsi_period``: Period used for the RSI indicator.
            - ``atr_period``: Period used for the ATR calculation.
            - ``bb_period``: Period used for Bollinger Bands.
            - ``bb_std``: Standard deviation multiplier for Bollinger Bands.
            - ``sma_short_period``: Short period for simple moving average
              crossovers.
            - ``sma_long_period``: Long period for simple moving average
              crossovers.

        Returns
        -------
        Dict[str, Any]
            Dictionary mapping parameter names to their current values.
        """

        return {
            "risk_profile": self.risk_profile,
            "use_quantum_metrics": self.use_quantum_metrics,
            "profit_target_pct": self.profit_target_pct,
            "stop_loss_pct": self.stop_loss_pct,
            "max_position_size": self.max_position_size,
            "min_volatility": self.min_volatility,
            "max_spread_pct": self.max_spread_pct,
            "mean_reversion_threshold": self.mean_reversion_threshold,
            "momentum_threshold": self.momentum_threshold,
            "min_volume_percentile": self.min_volume_percentile,
            "vwap_periods": self.vwap_periods,
            "ema_periods": self.ema_periods,
            "rsi_period": self.rsi_period,
            "atr_period": self.atr_period,
            "bb_period": self.bb_period,
            "bb_std": self.bb_std,
            "sma_short_period": getattr(self, "sma_short_period", None),
            "sma_long_period": getattr(self, "sma_long_period", None),
        }

    def update_from_evolved(
        self, evolved_strategy: "QUALIAEnhancedScalpingStrategy"
    ) -> None:
        """Atualiza parâmetros a partir de ``evolved_strategy`` mantendo o estado."""
        if hasattr(evolved_strategy, "strategy_params"):
            new_params = evolved_strategy.strategy_params
        elif hasattr(evolved_strategy, "params"):
            new_params = evolved_strategy.params
        else:
            new_params = {}

        logger.info(f"{self.name}: Aplicando parâmetros evoluídos: {new_params}")
        self.update_parameters(new_params)
