from __future__ import annotations

from typing import Any, Dict

import pandas as pd

from ...utils.logger import get_logger
from .signal_generation import generate_signals
from .backtesting import simulate_portfolio
from ...metrics.performance_metrics import calculate_quantum_advantage

logger = get_logger(__name__)


def backtest(
    strategy: Any,
    market_data_map: Dict[str, pd.DataFrame],
    initial_capital: float = 10000.0,
    risk_per_trade_pct: float | None = None,
) -> Dict[str, Any]:
    """Run a basic backtest for the given strategy."""
    if not isinstance(market_data_map, dict) or strategy.symbol not in market_data_map:
        logger.error(
            "Formato de market_data_map inválido ou símbolo %s ausente.",
            strategy.symbol,
        )
        raise ValueError(
            f"market_data_map deve ser um dict contendo dados para {strategy.symbol}"
        )

    data_1h = market_data_map[strategy.symbol]
    if not isinstance(data_1h, pd.DataFrame) or "close" not in data_1h.columns:
        logger.error("Dados de 1h inválidos ou coluna 'close' ausente.")
        raise ValueError("Dados de 1h devem ser DataFrame com coluna 'close'.")

    logger.info(
        "Iniciando backtest para %s com %s velas de 1h.", strategy.name, len(data_1h)
    )

    signals = generate_signals(strategy, data_1h)

    simulation = simulate_portfolio(
        strategy,
        signals["positions_s1"],
        signals["positions_s2"],
        signals["positions_s3"],
        signals["market_returns"],
        initial_capital,
    )

    simulation.update(
        {
            "returns_s1": signals["returns_s1"].tolist(),
            "returns_s2": signals["returns_s2"].tolist(),
            "returns_s3": signals["returns_s3"].tolist(),
        }
    )

    simulation["quantum_advantage"] = calculate_quantum_advantage(
        signals["returns_s1"], signals["returns_s2"]
    )

    return simulation
