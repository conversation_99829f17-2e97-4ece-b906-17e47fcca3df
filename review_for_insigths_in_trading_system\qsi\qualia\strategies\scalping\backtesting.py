from __future__ import annotations

from typing import Any, Dict

import pandas as pd

from ...utils.logger import get_logger
from .signals import generate_signal

logger = get_logger(__name__)


def backtest(
    strategy: Any, df: pd.DataFrame, initial_capital: float = 10000.0
) -> Dict[str, Any]:
    """Simple backtest using generated signals."""
    if df.empty:
        logger.warning("DataFrame vazio para backtest.")
        return {"final_capital": initial_capital, "pnl_pct": 0.0}

    capital = initial_capital
    position = 0
    prev_price = df["close"].iloc[0]

    for i in range(1, len(df)):
        current_df = df.iloc[: i + 1]
        signal, _ = generate_signal(
            current_df,
            sma_short_period=strategy.sma_short_period,
            sma_long_period=strategy.sma_long_period,
            simple_pilot_mode=strategy.simple_pilot_mode,
            simple_rsi_period=strategy.simple_rsi_period,
            simple_rsi_threshold_buy=strategy.simple_rsi_threshold_buy,
            simple_rsi_threshold_sell=strategy.simple_rsi_threshold_sell,
            simple_pilot_confidence=strategy.simple_pilot_confidence,
            use_quantum_metrics=False,
            quantum_metrics=None,
            qpm_test_mode_sma_override=False,
            force_qpm_test_signal_type=None,
        )
        price = df["close"].iloc[i]
        ret = (price - prev_price) / prev_price
        capital *= 1 + position * ret
        if signal == "BUY":
            position = 1
        elif signal == "SELL":
            position = -1
        prev_price = price

    pnl_pct = (capital - initial_capital) / initial_capital * 100
    return {"final_capital": capital, "pnl_pct": pnl_pct}
