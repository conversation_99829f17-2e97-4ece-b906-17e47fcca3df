"""Core classes for scalping strategies."""

from __future__ import annotations

from dataclasses import asdict, is_dataclass
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Tuple, Union
from ...config.config_manager import ConfigManager

import pandas as pd

from ...market.decision_context import TradeContext
from ..strategy_utils import make_signal_df

from ...utils.logger import get_logger
from ...strategies.strategy_interface import TradingStrategy, register_strategy
from ...strategies.params import EnhancedScalpingParams

from .indicators import calculate_indicators
from .signals import SignalGenerator, generate_signal
from .backtesting import backtest

logger = get_logger(__name__)


class ScalpingStrategy(TradingStrategy):
    """Base scalping strategy providing indicator and signal helpers."""

    def __init__(
        self,
        risk_profile: str = "balanced",
        use_quantum_metrics: bool = True,
        *,
        simple_pilot_mode: bool = False,
        simple_rsi_period: int = 14,
        simple_rsi_threshold_buy: int = 30,
        simple_rsi_threshold_sell: int = 70,
        simple_pilot_confidence: float = 0.7,
        context: Optional[Dict[str, Any]] = None,
        config_manager: Optional[ConfigManager] = None,
    ) -> None:
        super().__init__(context=context, config_manager=config_manager)
        self.risk_profile = risk_profile
        self.use_quantum_metrics = use_quantum_metrics
        self.simple_pilot_mode = simple_pilot_mode
        self.simple_rsi_period = simple_rsi_period
        self.simple_rsi_threshold_buy = simple_rsi_threshold_buy
        self.simple_rsi_threshold_sell = simple_rsi_threshold_sell
        self.simple_pilot_confidence = simple_pilot_confidence

        # Flags used during development and testing with QPM
        self.qpm_test_mode_sma_override: bool = False
        self.force_qpm_test_signal_type: Optional[str] = None

        if risk_profile == "conservative":
            self.profit_target_pct = 0.2
            self.stop_loss_pct = 0.15
            self.max_position_size = 0.1
            self.min_volatility = 0.5
            self.max_spread_pct = 0.05
            self.mean_reversion_threshold = 2.5
            self.momentum_threshold = 0.5
            self.min_volume_percentile = 50
        elif risk_profile == "aggressive":
            self.profit_target_pct = 0.5
            self.stop_loss_pct = 0.4
            self.max_position_size = 0.25
            self.min_volatility = 0.3
            self.max_spread_pct = 0.15
            self.mean_reversion_threshold = 1.8
            self.momentum_threshold = 0.3
            self.min_volume_percentile = 30
        else:
            self.profit_target_pct = 0.3
            self.stop_loss_pct = 0.25
            self.max_position_size = 0.15
            self.min_volatility = 0.4
            self.max_spread_pct = 0.1
            self.mean_reversion_threshold = 2.0
            self.momentum_threshold = 0.4
            self.min_volume_percentile = 40

        self.vwap_periods = {"1m": 60, "5m": 24, "15m": 16, "1h": 8}
        self.ema_periods = [9, 21, 55, 100]
        self.rsi_period = 14
        self.atr_period = 14
        self.bb_period = 20
        self.bb_std = 2.0

        self.state: Dict[str, Any] = {}
        self.signals: Dict[str, Any] = {}
        self.last_update: Dict[str, Any] = {}

        self.last_analysis_time = datetime.now(timezone.utc)
        self.indicator_cache: Dict[str, Any] = {}
        self.last_indicator_calc_timestamp: Optional[pd.Timestamp] = None

        logger.info(
            "Estrat\u00e9gia de Scalping inicializada com perfil de risco: %s",
            risk_profile,
        )

    def calculate_indicators(self, ohlcv_data: Dict[str, Any]) -> Dict[str, Any]:
        df = pd.DataFrame(
            {
                "timestamp": pd.to_datetime(ohlcv_data["timestamps"]),
                "open": ohlcv_data["open"],
                "high": ohlcv_data["high"],
                "low": ohlcv_data["low"],
                "close": ohlcv_data["close"],
                "volume": ohlcv_data["volume"],
            }
        )
        df.set_index("timestamp", inplace=True)

        if not df.empty:
            current_ts = df.index[-1]
            if (
                self.last_indicator_calc_timestamp == current_ts
                and self.indicator_cache
            ):
                logger.debug(
                    "Usando cache de indicadores para timestamp %s", current_ts
                )
                return self.indicator_cache
        else:
            logger.debug(
                "DataFrame de entrada para calculate_indicators vazio, n\u00e3o \u00e9 poss\u00edvel usar/atualizar cache."
            )
            return {}

        indicators = calculate_indicators(
            df,
            ema_periods=self.ema_periods,
            rsi_period=self.rsi_period,
            atr_period=self.atr_period,
            bb_period=self.bb_period,
            bb_std=self.bb_std,
        )

        self.indicator_cache = indicators
        self.last_indicator_calc_timestamp = df.index[-1]
        logger.debug(
            "Cache de indicadores atualizado para timestamp %s",
            self.last_indicator_calc_timestamp,
        )
        return indicators

    def analyze_market(
        self,
        market_data: pd.DataFrame,
        trading_context: "TradeContext",
        quantum_metrics: Optional[Dict[str, Any]] = None,
        similar_past_patterns: Optional[List[Dict[str, Any]]] = None,
        *,
        context: "TradeContext" | None = None,
    ) -> Dict[str, Any]:
        """Analyze ``market_data`` and return a trading decision.

        Parameters
        ----------
        market_data:
            DataFrame containing OHLCV data. The index must be a
            ``DatetimeIndex``.
        trading_context:
            Additional context for the decision. Unused for now.
        quantum_metrics:
            Optional metrics that modulate the signal generation.
        similar_past_patterns:
            Not used in this basic implementation.
        context:
            Deprecated alias for ``trading_context``.

        Returns
        -------
        Dict[str, Any]
            Dictionary with ``signal``, ``confidence`` and optional risk
            management levels.
        """

        _ = trading_context or context
        if market_data.empty:
            logger.warning("DataFrame vazio em analyze_market. Retornando HOLD.")
            return {
                "signal": "hold",
                "confidence": 0.0,
                "stop_loss": None,
                "take_profit": None,
            }

        if (
            not isinstance(market_data.index, pd.DatetimeIndex)
            and "timestamp" in market_data.columns
        ):
            market_data = market_data.set_index(
                pd.to_datetime(market_data["timestamp"])
            )

        ohlcv_dict = {
            "timestamps": market_data.index.tolist(),
            "open": market_data["open"].values,
            "high": market_data["high"].values,
            "low": market_data["low"].values,
            "close": market_data["close"].values,
            "volume": (
                market_data["volume"].values
                if "volume" in market_data.columns
                else [0] * len(market_data)
            ),
        }

        indicators = self.calculate_indicators(ohlcv_dict)
        signal, confidence = self.generate_signal(market_data, quantum_metrics)

        price = float(market_data["close"].iloc[-1])
        vwap_value = float(indicators.get("vwap", [price])[-1])
        atr_value = float(indicators.get("atr", [0])[-1])
        bb_upper = float(indicators.get("bb_upper", [price])[-1])
        bb_lower = float(indicators.get("bb_lower", [price])[-1])

        # Ajuste do sinal com base na VWAP e nas bandas de Bollinger.
        if signal == "hold":
            if price < bb_lower:
                # Preço abaixo da banda inferior indica sobrevenda.
                signal = "buy"
                confidence = max(confidence, 0.55)
            elif price > bb_upper:
                # Preço acima da banda superior indica sobrecompra.
                signal = "sell"
                confidence = max(confidence, 0.55)

        if signal == "buy" and price > vwap_value:
            # Compra com preço acima da VWAP reforça momentum de alta.
            confidence += 0.05
        elif signal == "sell" and price < vwap_value:
            # Venda com preço abaixo da VWAP reforça momentum de baixa.
            confidence += 0.05

        stop_loss = None
        take_profit = None
        if atr_value > 0:
            # ATR define amplitude média do movimento de preços.
            sl_offset = atr_value
            tp_offset = atr_value * 1.5
        else:
            sl_offset = price * self.stop_loss_pct
            tp_offset = price * self.profit_target_pct

        if signal == "buy":
            stop_loss = price - sl_offset
            take_profit = price + tp_offset
        elif signal == "sell":
            stop_loss = price + sl_offset
            take_profit = price - tp_offset

        if quantum_metrics:
            coherence_val = quantum_metrics.get("coherence", 0.0)
            entropy_val = quantum_metrics.get("entropy", 0.0)
            otoc_val = quantum_metrics.get("otoc", 0.0)

            if isinstance(coherence_val, list):
                coherence_val = coherence_val[-1]
            if isinstance(entropy_val, list):
                entropy_val = entropy_val[-1]
            if isinstance(otoc_val, list):
                otoc_val = otoc_val[-1]

            coherence = float(coherence_val)
            entropy = float(entropy_val)
            otoc = float(otoc_val)

            # Coerência alta e entropia baixa sugerem ordenação do sistema e
            # aumentam a confiança no sinal identificado.
            if entropy > 0:
                confidence *= 1 + (coherence / entropy - 1) * 0.1

            # OTOC mede o grau de caos; valores elevados reduzem confiança e
            # ampliam stop/take profit para acomodar volatilidade.
            confidence *= 1 - otoc * 0.05
            if stop_loss is not None:
                stop_loss *= 1 + otoc * 0.1
            if take_profit is not None:
                take_profit *= 1 + coherence * 0.1

        confidence = float(min(max(confidence, 0.0), 1.0))

        self.last_analysis_time = datetime.now(timezone.utc)

        result = {
            "signal": signal,
            "confidence": confidence,
            "stop_loss": stop_loss,
            "take_profit": take_profit,
            "price": price,
            "indicators": indicators,
        }
        logger.debug(
            "analyze_market result: %s",
            {k: v for k, v in result.items() if k != "indicators"},
        )
        return result

    def generate_signal(
        self, df: pd.DataFrame, quantum_metrics: Optional[Dict[str, Any]] = None
    ) -> Tuple[str, float]:
        generator = SignalGenerator(
            sma_short_period=self.sma_short_period,
            sma_long_period=self.sma_long_period,
            simple_pilot_mode=self.simple_pilot_mode,
            simple_rsi_period=self.simple_rsi_period,
            simple_rsi_threshold_buy=self.simple_rsi_threshold_buy,
            simple_rsi_threshold_sell=self.simple_rsi_threshold_sell,
            simple_pilot_confidence=self.simple_pilot_confidence,
            use_quantum_metrics=self.use_quantum_metrics,
            qpm_test_mode_sma_override=self.qpm_test_mode_sma_override,
            force_qpm_test_signal_type=self.force_qpm_test_signal_type,
        )
        return generator.generate(df, quantum_metrics)

    def generate_signals(self, analysis_result: Dict[str, Any]) -> pd.DataFrame:
        """Return standardized signal DataFrame from ``analysis_result``."""
        logger.debug(
            "%s.generate_signals chamado com analysis_result: %s",
            self.__class__.__name__,
            analysis_result,
        )
        if not analysis_result:
            return pd.DataFrame()
        return make_signal_df(
            analysis_result.get("signal", "hold"),
            float(analysis_result.get("confidence", 0.0)),
        )

    def update_parameters(self, new_params: Dict[str, Any]) -> None:
        if not isinstance(new_params, dict):
            raise TypeError("new_params must be a dict")
        logger.info(
            "%s: Atualizando par\u00e2metros: %s", self.__class__.__name__, new_params
        )
        self.strategy_params.update(new_params)
        self.params_obj = EnhancedScalpingParams(**self.strategy_params)
        effective_params = asdict(self.params_obj)
        for key, value in effective_params.items():
            setattr(self, key, value)
        self.params = self.strategy_params
        logger.debug(
            "%s.update_parameters: self.strategy_params = %s",
            self.__class__.__name__,
            self.strategy_params,
        )

    def update_from_evolved(
        self, evolved_strategy: "QUALIAEnhancedScalpingStrategy"
    ) -> None:
        if hasattr(evolved_strategy, "strategy_params"):
            new_params = evolved_strategy.strategy_params
        elif hasattr(evolved_strategy, "params"):
            new_params = evolved_strategy.params
        else:
            new_params = {}
        logger.info(
            "%s: Aplicando par\u00e2metros evolu\u00eddos: %s",
            self.__class__.__name__,
            new_params,
        )
        self.update_parameters(new_params)

    def get_params(self) -> Dict[str, Any]:
        """Return current parameters of the scalping strategy."""

        return getattr(self, "strategy_params", {})

    def set_params(self, params: Dict[str, Any]) -> None:
        """Alias for ``update_parameters`` to comply with interface."""

        self.update_parameters(params)


@register_strategy()
class QUALIAEnhancedScalpingStrategy(ScalpingStrategy):
    """Enhanced scalping strategy integrating QUALIA features."""

    strategy_alias = "qualia_enhanced_scalping"

    def __init__(
        self,
        symbol: str,
        timeframe: str,
        params: Optional[Union[EnhancedScalpingParams, Dict[str, Any]]] = None,
        risk_profile_settings: Optional[Dict[str, Any]] = None,
        config_manager: Optional[ConfigManager] = None,
    ) -> None:
        if params is None:
            params_obj = EnhancedScalpingParams()
        elif is_dataclass(params):
            params_obj = params  # type: ignore[assignment]
        elif isinstance(params, dict):
            params_obj = EnhancedScalpingParams(**params)
        else:
            raise TypeError("params must be a dict or EnhancedScalpingParams")

        effective_params = asdict(params_obj)
        risk_profile = effective_params.get("risk_profile", "balanced")
        use_quantum_metrics = effective_params.get("use_quantum_metrics", True)
        simple_pilot_mode = effective_params.get("simple_pilot_mode", False)
        simple_rsi_period = effective_params.get("simple_rsi_period", 14)
        simple_rsi_threshold_buy = effective_params.get("simple_rsi_threshold_buy", 30)
        simple_rsi_threshold_sell = effective_params.get(
            "simple_rsi_threshold_sell", 70
        )
        simple_pilot_confidence = effective_params.get("simple_pilot_confidence", 0.7)

        super().__init__(
            risk_profile=risk_profile,
            use_quantum_metrics=use_quantum_metrics,
            simple_pilot_mode=simple_pilot_mode,
            simple_rsi_period=simple_rsi_period,
            simple_rsi_threshold_buy=simple_rsi_threshold_buy,
            simple_rsi_threshold_sell=simple_rsi_threshold_sell,
            simple_pilot_confidence=simple_pilot_confidence,
            context={"symbol": symbol, "timeframe": timeframe},
            config_manager=config_manager,
        )

        self.name = f"QUALIARKI_{symbol.replace('/', '_')}_{timeframe}"
        self.version = "1.0.0-qast-enhanced"
        self.symbol = symbol
        self.timeframe = timeframe
        self.strategy_params = effective_params
        self.params_obj = params_obj
        self.risk_profile_settings = risk_profile_settings or {}

        self.sma_short_period = effective_params.get("sma_short_period", 10)
        self.sma_long_period = effective_params.get("sma_long_period", 20)
        self.atr_sl_multiplier_k = effective_params.get("atr_sl_multiplier_k", 0.0)
        self.atr_period = effective_params.get("atr_period", self.atr_period)

        logger.info("%s inicializada para %s em %s.", self.name, symbol, timeframe)

    def get_core_strategy_parameters(self) -> Dict[str, Any]:
        return {
            "risk_profile": self.risk_profile,
            "use_quantum_metrics": self.use_quantum_metrics,
            "profit_target_pct": self.profit_target_pct,
            "stop_loss_pct": self.stop_loss_pct,
            "max_position_size": self.max_position_size,
            "min_volatility": getattr(self, "min_volatility", None),
            "max_spread_pct": getattr(self, "max_spread_pct", None),
            "mean_reversion_threshold": getattr(self, "mean_reversion_threshold", None),
            "momentum_threshold": getattr(self, "momentum_threshold", None),
            "min_volume_percentile": getattr(self, "min_volume_percentile", None),
            "vwap_periods": self.vwap_periods,
            "ema_periods": self.ema_periods,
            "rsi_period": self.rsi_period,
            "atr_period": self.atr_period,
            "bb_period": self.bb_period,
            "bb_std": self.bb_std,
            "sma_short_period": self.sma_short_period,
            "sma_long_period": self.sma_long_period,
        }

    def backtest(
        self, market_data: pd.DataFrame, initial_capital: float = 10000.0
    ) -> Dict[str, Any]:
        return backtest(self, market_data, initial_capital)
