"""Utility functions for scalping indicators."""

from __future__ import annotations

from typing import Any, Dict, List

import numpy as np
import pandas as pd

from ...utils.logger import get_logger
from ... import indicators as shared_indicators

logger = get_logger(__name__)


def calculate_indicators(
    df: pd.DataFrame,
    ema_periods: List[int],
    rsi_period: int,
    atr_period: int,
    bb_period: int,
    bb_std: float,
) -> Dict[str, Any]:
    """Calculate common scalping indicators.

    Parameters
    ----------
    df:
        DataFrame with columns ``open``, ``high``, ``low``, ``close`` and ``volume``.
        The index must be ``DatetimeIndex``.
    ema_periods:
        Periods used for exponential moving averages.
    rsi_period:
        Window size for RSI calculation.
    atr_period:
        Window size for ATR calculation.
    bb_period:
        Period for Bollinger Bands.
    bb_std:
        Standard deviation multiplier for Bollinger Bands.

    Returns
    -------
    Dict[str, Any]
        Mapping with arrays for each indicator.
    """
    indicators: Dict[str, Any] = {}

    for period in ema_periods:
        indicators[f"ema_{period}"] = shared_indicators.ema(df["close"], period)

    df["vwap"] = (df["close"] * df["volume"]).cumsum() / df["volume"].cumsum()
    indicators["vwap"] = df["vwap"].values

    indicators["rsi"] = shared_indicators.rsi(df["close"], rsi_period).to_numpy()

    indicators["atr"] = shared_indicators.atr(
        df["high"], df["low"], df["close"], period=atr_period
    )

    sma = df["close"].rolling(window=bb_period).mean()
    std = df["close"].rolling(window=bb_period).std()
    indicators["bb_upper"] = (sma + std * bb_std).fillna(df["close"]).values
    indicators["bb_lower"] = (sma - std * bb_std).fillna(df["close"]).values
    indicators["bb_middle"] = sma.fillna(df["close"]).values

    indicators["momentum"] = df["close"].pct_change(5).values
    indicators["volatility"] = df["close"].pct_change().rolling(window=20).std().fillna(
        0
    ).values * np.sqrt(252 * 24 * 60)

    indicators["volume_ma"] = (
        df["volume"].rolling(window=20).mean().fillna(df["volume"]).values
    )
    indicators["relative_volume"] = (df["volume"] / indicators["volume_ma"]).values

    for period in ema_periods:
        ema_col = indicators[f"ema_{period}"]
        indicators[f"dist_to_ema_{period}"] = (
            (df["close"] - ema_col) / df["close"] * 100
        ).values

    return indicators
