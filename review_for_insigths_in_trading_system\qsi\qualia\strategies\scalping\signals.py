"""Signal generation utilities for scalping strategies."""

from __future__ import annotations

from typing import Any, Dict, Optional, Tuple

import pandas as pd

from ...utils.logger import get_logger

logger = get_logger(__name__)


class SignalGenerator:
    """Gerador de sinais baseado em médias móveis simples."""

    def __init__(
        self,
        *,
        sma_short_period: int,
        sma_long_period: int,
        simple_pilot_mode: bool = False,
        simple_rsi_period: int = 14,
        simple_rsi_threshold_buy: int = 30,
        simple_rsi_threshold_sell: int = 70,
        simple_pilot_confidence: float = 0.7,
        use_quantum_metrics: bool = True,
        qpm_test_mode_sma_override: bool = False,
        force_qpm_test_signal_type: Optional[str] = None,
    ) -> None:
        self.sma_short_period = sma_short_period
        self.sma_long_period = sma_long_period
        self.simple_pilot_mode = simple_pilot_mode
        self.simple_rsi_period = simple_rsi_period
        self.simple_rsi_threshold_buy = simple_rsi_threshold_buy
        self.simple_rsi_threshold_sell = simple_rsi_threshold_sell
        self.simple_pilot_confidence = simple_pilot_confidence
        self.use_quantum_metrics = use_quantum_metrics
        self.qpm_test_mode_sma_override = qpm_test_mode_sma_override
        self.force_qpm_test_signal_type = force_qpm_test_signal_type

    def generate(
        self, df: pd.DataFrame, quantum_metrics: Optional[Dict[str, Any]] = None
    ) -> Tuple[str, float]:
        return generate_signal(
            df,
            sma_short_period=self.sma_short_period,
            sma_long_period=self.sma_long_period,
            simple_pilot_mode=self.simple_pilot_mode,
            simple_rsi_period=self.simple_rsi_period,
            simple_rsi_threshold_buy=self.simple_rsi_threshold_buy,
            simple_rsi_threshold_sell=self.simple_rsi_threshold_sell,
            simple_pilot_confidence=self.simple_pilot_confidence,
            use_quantum_metrics=self.use_quantum_metrics,
            quantum_metrics=quantum_metrics,
            qpm_test_mode_sma_override=self.qpm_test_mode_sma_override,
            force_qpm_test_signal_type=self.force_qpm_test_signal_type,
        )


def generate_signal(
    df: pd.DataFrame,
    *,
    sma_short_period: int,
    sma_long_period: int,
    simple_pilot_mode: bool = False,
    simple_rsi_period: int = 14,
    simple_rsi_threshold_buy: int = 30,
    simple_rsi_threshold_sell: int = 70,
    simple_pilot_confidence: float = 0.7,
    use_quantum_metrics: bool = True,
    quantum_metrics: Optional[Dict[str, Any]] = None,
    qpm_test_mode_sma_override: bool = False,
    force_qpm_test_signal_type: Optional[str] = None,
) -> Tuple[str, float]:
    """Generate a trading signal based on price data and metrics."""
    if df.empty:
        logger.warning(
            "DataFrame vazio fornecido para generate_signal. Retornando hold."
        )
        return "hold", 0.5

    if simple_pilot_mode:
        delta = df["close"].diff()
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)
        avg_gain = gain.ewm(
            com=simple_rsi_period - 1, min_periods=simple_rsi_period
        ).mean()
        avg_loss = loss.ewm(
            com=simple_rsi_period - 1, min_periods=simple_rsi_period
        ).mean()
        if avg_loss.iloc[-1] == 0:
            rs = float("inf") if avg_gain.iloc[-1] > 0 else 0
        else:
            rs = avg_gain.iloc[-1] / avg_loss.iloc[-1]
        rsi = 100 - (100 / (1 + rs))
        rsi_value = rsi if pd.notna(rsi) else 50
        logger.debug(
            "Modo Piloto Simples: RSI(%s) = %.2f", simple_rsi_period, rsi_value
        )
        if rsi_value < simple_rsi_threshold_buy:
            return "buy", simple_pilot_confidence
        if rsi_value > simple_rsi_threshold_sell:
            return "sell", simple_pilot_confidence
        return "hold", 0.5

    sma_short = df["close"].rolling(sma_short_period).mean()
    sma_long = df["close"].rolling(sma_long_period).mean()

    current_price = df["close"].iloc[-1]
    signal = "hold"
    base_confidence = 0.5

    if (
        sma_short.iloc[-1] > sma_long.iloc[-1]
        and sma_short.iloc[-2] <= sma_long.iloc[-2]
    ):
        signal = "buy"
        base_confidence = 0.6
    elif (
        sma_short.iloc[-1] < sma_long.iloc[-1]
        and sma_short.iloc[-2] >= sma_long.iloc[-2]
    ):
        signal = "sell"
        base_confidence = 0.6
    else:
        if sma_short.iloc[-1] > sma_long.iloc[-1]:
            distance = (sma_short.iloc[-1] - sma_long.iloc[-1]) / current_price
            if distance > 0.01:
                signal = "buy"
                base_confidence = 0.5 + min(distance * 10, 0.4)
        elif sma_short.iloc[-1] < sma_long.iloc[-1]:
            distance = (sma_long.iloc[-1] - sma_short.iloc[-1]) / current_price
            if distance > 0.01:
                signal = "sell"
                base_confidence = 0.5 + min(distance * 10, 0.4)

    final_confidence = base_confidence
    qm_influence: Dict[str, float] = {}
    if use_quantum_metrics and quantum_metrics:
        coherence = quantum_metrics.get("quantum_coherence", [])
        entropy = quantum_metrics.get("quantum_entropy", [])
        otoc = quantum_metrics.get("otoc", [])
        last_measurement = quantum_metrics.get("last_measurement_counts", {})

        measurement_ratio = 0.5
        if last_measurement:
            count_0 = last_measurement.get("0", 0)
            count_1 = last_measurement.get("1", 0)
            total_count = count_0 + count_1
            if total_count > 0:
                measurement_ratio = count_1 / total_count

        if measurement_ratio > 0.6:
            qm_influence["measurement"] = 0.1
            if signal == "sell":
                qm_influence["measurement"] = -0.05
        elif measurement_ratio < 0.4:
            qm_influence["measurement"] = -0.1
            if signal == "buy":
                qm_influence["measurement"] = -0.05

        if otoc and len(otoc) > 0:
            latest_otoc = otoc[-1]
            if latest_otoc < 0.8:
                qm_influence["otoc"] = -0.15
            elif latest_otoc > 0.95:
                qm_influence["otoc"] = 0.1

        if coherence and len(coherence) > 0:
            latest_coherence = coherence[-1]
            if latest_coherence > 0.8:
                qm_influence["coherence"] = 0.1
            elif latest_coherence < 0.4:
                qm_influence["coherence"] = -0.1

        qm_adjustment = sum(qm_influence.values())
        if base_confidence < 0.55 and qm_adjustment > 0.2 and signal == "hold":
            signal = "buy" if measurement_ratio > 0.6 else "sell"
        final_confidence = min(max(base_confidence + qm_adjustment, 0.1), 0.95)
        logger.debug("Ajuste qu\u00e2ntico aplicado: %s", qm_influence)

    if qpm_test_mode_sma_override and force_qpm_test_signal_type:
        signal = force_qpm_test_signal_type
        final_confidence = 0.75
        logger.debug("QPM Test Mode ativado. Usando sinal for\u00e7ado: %s", signal)

    return signal, final_confidence
