"""Fábrica de Estratégias do QUALIA.

Este módulo centraliza o ciclo de vida das ``TradingStrategy``. Suas funções
principais são:

* ``register_strategy`` – associa um alias a uma classe de estratégia.
* ``get_strategy_class`` – retorna a classe registrada para um alias.
* ``create_strategy`` – instancia e inicializa a estratégia especificada.
* ``get_registered_aliases`` – lista de aliases atualmente registrados.
* ``get_available_strategy_names`` – alias para ``get_registered_aliases``.
* ``is_strategy_registered`` – verifica se um alias está presente.
"""

from typing import Dict, Type, Any, Optional, Callable, List
from ..config.config_manager import ConfigManager
from dataclasses import asdict, is_dataclass
import logging
import threading
from ..utils.logger import get_logger
import inspect

# Importação da interface base para type hinting
# Evitar importação direta de strategy_interface aqui para prevenir ciclos,
# em vez disso, usaremos type hints como strings ou forward references se necessário,
# ou passaremos a classe como argumento para registro.
# from ..strategies.strategy_interface import TradingStrategy

logger = get_logger(__name__)
# # logger.setLevel(logging.DEBUG)  # QualiaLogger doesn't have setLevel method

_strategy_registry: Dict[str, Type["TradingStrategy"]] = {}
logger.debug(
    f"Modulo strategy_factory.py carregado. _strategy_registry inicial: {_strategy_registry}, ID: {id(_strategy_registry)}"
)

# Lock to protect access to _strategy_registry
_registry_lock = threading.Lock()

_strategy_creation_hooks: Dict[str, Callable[..., "TradingStrategy"]] = {}


class StrategyFactory:
    """
    Fábrica para criar instâncias de TradingStrategy.
    Permite o registro de classes de estratégia e sua posterior instanciação
    usando um alias.
    """

    def __init__(self) -> None:
        # Logging the entire registry on initialization produced excessive
        # debug output. We simply note that initialization occurred, using the
        # lowest level available to keep logs concise.
        if hasattr(logger, "trace"):
            logger.trace("StrategyFactory initialized")

    @staticmethod
    def register_strategy(alias: str, strategy_class: Type["TradingStrategy"]) -> None:
        """
        Registra uma classe de estratégia com um alias.

        Args:
            alias: O alias (string) para identificar a estratégia.
            strategy_class: A classe da estratégia a ser registrada.
                          Deve ser uma subclasse de TradingStrategy.

        Raises:
            ValueError: Se o alias já estiver registrado para uma classe genuinamente diferente.
        """
        logger.debug(
            "FACTORY: Tentando registrar '%s' com classe %s. Registry ATUAL: %s, ID: %s",
            alias,
            strategy_class.__name__,
            _strategy_registry,
            id(_strategy_registry),
        )
        with _registry_lock:
            if alias in _strategy_registry:
                existing_class = _strategy_registry[alias]
                if existing_class is strategy_class:
                    logger.debug(
                        "Strategy alias '%s' for class %s re-registered (same class object).",
                        alias,
                        strategy_class.__name__,
                    )
                elif (
                    existing_class.__name__ == strategy_class.__name__
                    and existing_class.__module__.split(".")[-1]
                    == strategy_class.__module__.split(".")[-1]
                    and existing_class.__qualname__ == strategy_class.__qualname__
                ):
                    logger.warning(
                        f"Strategy alias '{alias}' (class {strategy_class.__name__}) "
                        f"re-registered. Original: {existing_class} (from module {existing_class.__module__}), "
                        f"New: {strategy_class} (from module {strategy_class.__module__}). "
                        f"This may indicate a sys.path issue or duplicate import."
                    )
                    # Sobrescrever com a nova instância da classe. Isso assume que, embora sejam objetos de classe diferentes
                    # devido ao carregamento do módulo, são funcionalmente a mesma estratégia do mesmo código-fonte.
                    # A alternativa seria manter a original, mas sobrescrever garante que a "última carregada" prevaleça,
                    # o que pode ser útil em cenários de desenvolvimento com recarregamento.
                else:
                    logger.error(
                        f"Strategy alias '{alias}' is already registered for class "
                        f"{existing_class.__module__}.{existing_class.__qualname__}. "
                        f"Attempt to register it for a DIFFERENT class "
                        f"{strategy_class.__module__}.{strategy_class.__qualname__} failed."
                    )
                    raise ValueError(
                        f"Strategy alias '{alias}' is already registered for a different class."
                    )

            _strategy_registry[alias] = strategy_class
        logger.debug(
            "FACTORY: Estratégia '%s' (%s) registrada com sucesso. Registry AGORA: %s, ID: %s",
            alias,
            strategy_class.__name__,
            _strategy_registry,
            id(_strategy_registry),
        )

    @staticmethod
    def get_strategy_class(alias: str) -> Optional[Type["TradingStrategy"]]:
        """
        Retorna a classe de estratégia registrada para um dado alias.

        Args:
            alias: O alias da estratégia.

        Returns:
            A classe da estratégia, ou None se o alias não for encontrado.
        """
        logger.debug(
            "FACTORY.get_strategy_class: Delegando busca por alias '%s'. Registry state via module func log.",
            alias,
        )
        return get_strategy_class_in_factory(alias)

    @staticmethod
    def create_strategy(
        alias: str,
        params: Optional[Dict[str, Any]] = None,
        context: Optional[Dict[str, Any]] = None,
        config_manager: Optional["ConfigManager"] = None,
    ) -> "TradingStrategy":
        """
        Cria uma instância de uma estratégia registrada, a inicializa e aplica parâmetros.

        Args:
            alias: O alias da estratégia a ser criada.
            params: Dicionário de parâmetros para passar ao construtor da estratégia.
                    Estes são os parâmetros específicos da estratégia.
            context: Dicionário de contexto para passar ao método initialize() da estratégia.
                     O contexto pode incluir recursos compartilhados, configurações globais, etc.
                     Os 'params' também podem ser incluídos aqui se a estratégia os espera no contexto.
            config_manager: Instância opcional de :class:`~qualia.config.config_manager.ConfigManager`.

        Returns:
            Uma instância inicializada da TradingStrategy.

        Raises:
            ValueError: Se o alias da estratégia não for encontrado.
        """
        logger.debug(
            "FACTORY.create_strategy: Tentando criar estratégia para alias '%s'. Registry ID: %s",
            alias,
            id(_strategy_registry),
        )
        if not alias:
            logger.error("Tentativa de criar estratégia com alias vazio.")
            raise ValueError("O alias da estratégia não pode ser vazio.")

        params_desc = None
        if params is None:
            params_desc = "None"
        elif is_dataclass(params):
            params_desc = list(asdict(params).keys())
        elif isinstance(params, dict):
            params_desc = list(params.keys())
        else:
            params_desc = str(type(params))

        logger.debug(
            "StrategyFactory: Tentando criar estratégia '%s' com params: %s e context: %s",
            alias,
            params_desc,
            list(context.keys()) if context else "None",
        )

        if params is not None and is_dataclass(params):
            params_for_strategy = asdict(params)
        else:
            params_for_strategy = params

        context_for_init = context.copy() if context else {}
        if "qualia_config" in context_for_init:
            qcfg = context_for_init["qualia_config"]
            if isinstance(qcfg, ConfigManager):
                qcfg = qcfg.data
            if isinstance(qcfg, dict):
                allowed_keys = [
                    "strategy_config",
                    "qast_config",
                    "ace_config",
                    "risk_profile_settings",
                ]
                context_for_init["qualia_config"] = {
                    key: qcfg.get(key, {}) for key in allowed_keys if key in qcfg
                }

        if context_for_init and isinstance(params_for_strategy, dict):
            if (
                "risk_profile_by_symbol" in context_for_init
                and "symbol" in context_for_init
            ):
                overrides = context_for_init.get("risk_profile_by_symbol", {})
                symbol = context_for_init.get("symbol")
                if isinstance(overrides, dict) and symbol in overrides:
                    context_for_init["risk_profile"] = overrides[symbol]
                    params_for_strategy["risk_profile"] = overrides[symbol]
            if (
                "risk_profile" in context_for_init
                and "risk_profile" not in params_for_strategy
            ):
                params_for_strategy["risk_profile"] = context_for_init["risk_profile"]

        strategy_class = StrategyFactory.get_strategy_class(alias)
        if strategy_class is None:
            logger.error(
                f"Estratégia com alias '{alias}' não encontrada no registro após _get_strategy_class."
            )
            raise ValueError(f"Estratégia '{alias}' não encontrada.")

        try:
            sig = inspect.signature(strategy_class.__init__)
            init_params_spec = sig.parameters

            # Registra a assinatura do construtor para facilitar a depuração
            logger.debug(
                f"StrategyFactory: Assinatura __init__ para {strategy_class.__name__}: {list(init_params_spec.keys())}"
            )

            valid_init_params = {}

            # Preencher argumentos do __init__ baseados no que está disponível
            # Esta lógica tenta ser flexível para diferentes assinaturas de __init__

            # 1. Argumentos explícitos como 'symbol', 'timeframe' (muitas vezes vêm do 'context')
            if context_for_init:
                for p_name in init_params_spec:
                    if p_name == "self":
                        continue
                    if p_name in context_for_init:
                        valid_init_params[p_name] = context_for_init[p_name]
                        logger.debug(
                            f"StrategyFactory: Mapeado '{p_name}' do contexto para __init__."
                        )

            # 2. O argumento 'params' (dicionário de configuração da estratégia)
            if "params" in init_params_spec:
                if params_for_strategy is not None:
                    valid_init_params["params"] = params_for_strategy
                    logger.debug(
                        "StrategyFactory: Mapeado 'params' (config dict) para __init__."
                    )
                # Se 'params' é esperado mas params_for_strategy é None, e não há default, dará erro.
                # Se 'params' não está em valid_init_params mas é obrigatório, dará erro.

            # 3. O argumento 'shared_context' (dicionário de contexto completo)
            if "shared_context" in init_params_spec:
                valid_init_params["shared_context"] = context_for_init
                logger.debug("StrategyFactory: Mapeado 'shared_context' para __init__.")

            if "config_manager" in init_params_spec:
                valid_init_params["config_manager"] = config_manager
                logger.debug("StrategyFactory: Mapeado 'config_manager' para __init__.")

            # Injetar DynamicRiskController se necessário
            if (
                "dynamic_risk_controller" in init_params_spec
                and "dynamic_risk_controller" not in valid_init_params
            ):
                drc_obj = None
                if "dynamic_risk_controller" in context_for_init:
                    drc_obj = context_for_init["dynamic_risk_controller"]
                else:
                    ace_cfg = {}
                    if config_manager is not None:
                        try:
                            ace_cfg = config_manager.get_ace_config()
                        except Exception as exc:  # noqa: BLE001 - best effort
                            logger.debug(
                                "StrategyFactory: falha ao obter ace_config do ConfigManager: %s",
                                exc,
                            )
                    if not ace_cfg and "qualia_config" in context_for_init:
                        qc = context_for_init["qualia_config"]
                        if isinstance(qc, ConfigManager):
                            ace_cfg = qc.get_ace_config()
                        elif isinstance(qc, dict):
                            ace_cfg = qc.get("ace_config", {})

                    if (
                        ace_cfg.get("enable_dynamic_risk_control")
                        and "dynamic_risk_config" in ace_cfg
                    ):
                        risk_profile = context_for_init.get("risk_profile", "balanced")
                        try:
                            from .nova_estrategia_qualia.risk import (
                                get_cached_dynamic_risk_controller,
                            )

                            drc_obj = get_cached_dynamic_risk_controller(
                                ace_cfg["dynamic_risk_config"], risk_profile
                            )
                        except Exception as exc:  # noqa: BLE001 - best effort
                            logger.error(
                                "StrategyFactory: erro ao criar DynamicRiskController: %s",
                                exc,
                            )
                if drc_obj is not None:
                    valid_init_params["dynamic_risk_controller"] = drc_obj
                    logger.debug(
                        "StrategyFactory: DynamicRiskController injetado no __init__."
                    )

            # Garantir que 'symbol' e 'timeframe' sejam passados se a estratégia os espera
            # e eles não foram cobertos pela iteração do contexto acima (caso raro).
            # Isso é mais uma salvaguarda.
            if (
                "symbol" in init_params_spec
                and "symbol" not in valid_init_params
                and context_for_init
                and "symbol" in context_for_init
            ):
                valid_init_params["symbol"] = context_for_init["symbol"]
                logger.debug(
                    "StrategyFactory: Salvaguarda - Mapeado 'symbol' do contexto para __init__."
                )

            if (
                "timeframe" in init_params_spec
                and "timeframe" not in valid_init_params
                and context_for_init
                and "timeframe" in context_for_init
            ):
                valid_init_params["timeframe"] = context_for_init["timeframe"]
                logger.debug(
                    "StrategyFactory: Salvaguarda - Mapeado 'timeframe' do contexto para __init__."
                )

            # Log final dos parâmetros resolvidos para auxiliar investigações de configuração
            logger.debug(
                f"StrategyFactory: Argumentos finais para {strategy_class.__name__}.__init__: {list(valid_init_params.keys())}"
            )
            logger.debug(
                f"StrategyFactory: Valores de valid_init_params: {valid_init_params}"
            )

            strategy_instance = strategy_class(**valid_init_params)

            # Chamada de inicialização explícita se existir e se for configurada para tal
            # A interface TradingStrategy não define mais um 'initialize' obrigatório pós __init__.
            # if hasattr(strategy_instance, 'initialize') and callable(strategy_instance.initialize):
            #     logger.debug(f"Chamando initialize() para {strategy_instance.name}")
            #     # A assinatura de initialize pode variar, adaptar se necessário
            #     # strategy_instance.initialize(context=context) ou similar
            #     strategy_instance.initialize()

            strategy_name = getattr(
                strategy_instance,
                "strategy_name",
                getattr(strategy_instance, "name", "unknown"),
            )
            logger.info(
                f"Estratégia '{alias}' ({strategy_name}) criada e instanciada com sucesso."
            )
            return strategy_instance

        except TypeError as e:
            logger.error(
                f"Erro de tipo ao instanciar ou inicializar '{alias}' ({strategy_class.__name__}) com params {params_for_strategy} e contexto. Verifique os argumentos. Erro: {e}",
                exc_info=True,
            )
            raise ValueError(f"Erro ao instanciar '{alias}': {e}")
        except Exception as e:
            logger.error(
                f"Erro inesperado ao criar estratégia '{alias}': {e}", exc_info=True
            )
            # Não relançar como ValueError genérico para não mascarar o erro original
            raise

    @staticmethod
    def get_registered_aliases() -> List[str]:
        """Retorna uma lista de todos os aliases de estratégias registrados."""
        logger.debug(
            "FACTORY: get_available_strategies_in_factory chamada. Registry: %s",
            _strategy_registry,
        )
        with _registry_lock:
            return list(_strategy_registry.keys())

    @staticmethod
    def is_strategy_registered(alias: str) -> bool:
        """Verifica se um alias de estratégia está registrado."""
        logger.debug(
            "FACTORY: Verificando se '%s' está registrado. Registry: %s",
            alias,
            _strategy_registry,
        )
        with _registry_lock:
            return alias in _strategy_registry

    @staticmethod
    def get_available_strategy_names() -> List[str]:
        """Alias para ``get_registered_aliases`` para compatibilidade externa."""
        logger.debug("FACTORY: get_available_strategy_names chamado")
        return StrategyFactory.get_registered_aliases()


# Para permitir que as estratégias se registrem de seus próprios módulos:


def register_strategy_in_factory(
    alias: str, strategy_class: Type["TradingStrategy"]
) -> None:
    """
    Registra uma classe de estratégia na fábrica sob um alias.
    Esta função é chamada por register_strategy em strategy_interface.py.
    """
    logger.debug(
        "FACTORY: Tentando registrar '%s' com classe %s. Registry ATUAL: %s, ID: %s",
        alias,
        strategy_class.__name__,
        _strategy_registry,
        id(_strategy_registry),
    )
    with _registry_lock:
        if alias in _strategy_registry:
            existing_class = _strategy_registry[alias]
            if existing_class is strategy_class:
                logger.debug(
                    "FACTORY: Alias '%s' já registrado com a mesma classe %s.",
                    alias,
                    strategy_class.__name__,
                )
                return
            if (
                existing_class.__name__ == strategy_class.__name__
                and existing_class.__qualname__ == strategy_class.__qualname__
            ):
                logger.debug(
                    "FACTORY: Alias '%s' já registrado com classe equivalente; mantendo registro existente.",
                    alias,
                )
                return
            logger.warning(
                "FACTORY: Estratégia com alias '%s' já registrada com uma classe diferente (%s). Será sobrescrita por %s.",
                alias,
                existing_class.__name__,
                strategy_class.__name__,
            )
        _strategy_registry[alias] = strategy_class
    logger.debug(
        "FACTORY: Estratégia '%s' (classe %s) registrada com SUCESSO. Registry AGORA: %s, ID: %s",
        alias,
        strategy_class.__name__,
        _strategy_registry,
        id(_strategy_registry),
    )


def get_strategy_class_in_factory(alias: str) -> Optional[Type["TradingStrategy"]]:
    """Obtém uma classe de estratégia registrada pelo seu alias a partir do registro do módulo."""
    logger.debug(
        "FACTORY (função get_strategy_class_in_factory): Buscando por '%s'. Registry: %s, ID: %s",
        alias,
        _strategy_registry,
        id(_strategy_registry),
    )
    with _registry_lock:
        return _strategy_registry.get(alias)


def is_strategy_registered_in_factory(alias: str) -> bool:
    """Retorna ``True`` se o alias estiver presente no registro de estratégias."""
    logger.debug(
        "FACTORY (função is_strategy_registered_in_factory): Checando '%s'. Registry: %s, ID: %s",
        alias,
        _strategy_registry,
        id(_strategy_registry),
    )
    with _registry_lock:
        return alias in _strategy_registry


def get_available_strategies_in_factory() -> List[str]:
    """Retorna uma lista dos aliases de todas as estratégias registradas no módulo."""
    logger.debug(
        "FACTORY (função get_available_strategies_in_factory): Listando aliases. Registry: %s, ID: %s",
        _strategy_registry,
        id(_strategy_registry),
    )
    with _registry_lock:
        return list(_strategy_registry.keys())
