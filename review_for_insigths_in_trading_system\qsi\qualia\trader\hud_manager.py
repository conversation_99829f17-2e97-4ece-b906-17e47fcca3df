"""Helpers for real-time HUD updates."""

from __future__ import annotations

from typing import Any, Dict, Sequence
import numpy as np

from ..visuals.dynamic_logo import DynamicLogoEngine
from ..utils.metric_sanitizer import sanitize_metrics
from ..common_types import SnapshotPayload
from ..utils.logger import get_logger
from datetime import datetime, timezone

try:  # pragma: no cover - optional dependency
    from opentelemetry import trace
except Exception:
    trace = None

from contextlib import nullcontext

logger = get_logger(__name__)


class HUDManager:
    """Manage the :class:`DynamicLogoEngine` and build snapshot payloads."""

    def __init__(self) -> None:
        self.engine = DynamicLogoEngine()
        self._prev_tick_close: float = 0.0

    def calculate_rsi(self, close_history: Sequence[float] | None) -> float:
        """Return the most recent RSI value from ``close_history``.

        Parameters
        ----------
        close_history : Sequence[float] or None
            Sequence of close prices. When fewer than three values are
            provided, a neutral RSI is returned.

        Returns
        -------
        float
            Last RSI value in the series or ``50.0`` if insufficient data.
        """

        if isinstance(close_history, Sequence) and len(close_history) >= 3:
            import pandas as pd

            series = pd.Series(list(close_history), dtype=float)
            delta = series.diff()
            gain = delta.clip(lower=0.0)
            loss = -delta.clip(upper=0.0)
            window = min(14, len(series))
            avg_gain = gain.rolling(window=window, min_periods=window).mean()
            avg_loss = loss.rolling(window=window, min_periods=window).mean()
            rs = avg_gain / avg_loss.replace(0, pd.NA)
            rsi_series = 100.0 - 100.0 / (1.0 + rs)
            rsi_val = rsi_series.iloc[-1]
            return float(rsi_val if pd.notna(rsi_val) else 50.0)
        return 50.0

    @staticmethod
    def compute_color_metrics(
        *, current_close: float, prev_close: float, volume: float, rsi_val: float
    ) -> tuple[float, float]:
        """Compute hue and brightness based on price and RSI movement.

        Parameters
        ----------
        current_close : float
            Current close price used to determine price movement.
        prev_close : float
            Close price from the previous tick for comparison.
        volume : float
            Traded volume for the current tick.
        rsi_val : float
            Relative Strength Index value for the current tick.

        Returns
        -------
        tuple[float, float]
            Pair ``(hue, brightness)`` scaled for the HUD.
        """

        price_diff = current_close - prev_close
        brightness_raw = abs(price_diff) * volume
        brightness = float(np.tanh(brightness_raw))
        hue = float(rsi_val) / 100.0 * 360.0
        return hue, brightness

    @staticmethod
    def normalize_embedding(
        embedding: Sequence[float] | np.ndarray | None,
    ) -> float:
        """Return the L2 norm of ``embedding`` normalized by vector length.

        Parameters
        ----------
        embedding : Sequence[float] or numpy.ndarray or None
            Embedding vector produced by the text encoder.

        Returns
        -------
        float
            Normalized magnitude of the embedding or ``0.0`` when unavailable.
        """

        if isinstance(embedding, (list, np.ndarray)) and len(embedding) > 0:
            vec = np.asarray(embedding, dtype=float)
            return float(np.linalg.norm(vec)) / float(np.sqrt(len(vec)))
        return 0.0

    def build_snapshot_dict(
        self,
        snapshot: Dict[str, Any],
        *,
        hue: float,
        brightness: float,
        embedding_norm: float,
    ) -> Dict[str, Any]:
        """Assemble the sanitized snapshot sent to the dashboard.

        Parameters
        ----------
        snapshot : dict
            Raw metrics captured on each tick.
        hue : float
            Hue computed from price movement and RSI.
        brightness : float
            Color brightness derived from price volatility and volume.
        embedding_norm : float
            Normalized text embedding magnitude for logo animation.

        Returns
        -------
        dict
            Dictionary with selected metrics for publishing.
        """
        raw_timestamp = snapshot.get("timestamp")
        
        # Garante que o timestamp seja sempre uma string no formato ISO
        if hasattr(raw_timestamp, 'isoformat'):
            timestamp_str = raw_timestamp.isoformat()
        elif raw_timestamp:
            timestamp_str = str(raw_timestamp)
        else:
            timestamp_str = datetime.now(tz=timezone.utc).isoformat()

        result = {
            "timestamp": timestamp_str,
            "hue": hue,
            "brightness": brightness,
            "embedding_norm": embedding_norm,
        }

        for key in ("liquidity_buckets", "trend_strength", "delta_entropy"):
            if key in snapshot:
                result[key] = (
                    float(snapshot[key])
                    if key != "liquidity_buckets"
                    else snapshot[key]
                )
        return result

    def on_tick(self, snapshot: Dict[str, Any]) -> Dict[str, Any]:
        span_cm = (
            trace.get_tracer(__name__).start_as_current_span("hud.on_tick")
            if trace
            else nullcontext()
        )
        with span_cm:
            rsi_val = self.calculate_rsi(snapshot.get("close_history"))
            prev_close = self._prev_tick_close or snapshot.get("close", 0.0)
            self._prev_tick_close = snapshot.get("close", prev_close)
            hue, brightness = self.compute_color_metrics(
                current_close=float(snapshot.get("close", 0.0)),
                prev_close=float(prev_close),
                volume=float(snapshot.get("volume", 0.0)),
                rsi_val=rsi_val,
            )
            norm_val = self.normalize_embedding(snapshot.get("ts_embedding", []))
            self.engine.update_audio_color(hue=hue, brightness=brightness)
            self.engine.update_text_embedding(norm_val)
            result = self.build_snapshot_dict(
                snapshot, hue=hue, brightness=brightness, embedding_norm=norm_val
            )
            sanitized = sanitize_metrics(result)
            try:
                from ui.sockets import publish_snapshot
            except Exception:  # pragma: no cover - optional dependency
                publish_snapshot = lambda _payload: None

            publish_snapshot(sanitized)
            return sanitized

    async def display_wallet_status(
        self, trader: "QUALIARealTimeTrader", force_update: bool = False
    ) -> None:
        """Log current wallet metrics.

        Parameters
        ----------
        trader:
            Instance of :class:`QUALIARealTimeTrader` providing wallet info.
        force_update:
            Emit log regardless of ``cycle_count`` modulo 20.
        """

        try:
            total_value = trader.wallet_state.get(
                "available_cash", 0.0
            ) + trader.wallet_state.get("positions_value", 0.0)
            open_positions_count = sum(len(p) for p in trader.open_positions.values())

            if force_update or trader.cycle_count % 20 == 0:
                logger.info(
                    "Wallet Status - Valor total: $%.2f, Posições abertas: %s",
                    total_value,
                    open_positions_count,
                )
        except (RuntimeError, ValueError) as exc:  # pragma: no cover - robustness
            logger.exception(
                "Erro ao exibir status da carteira: %s", exc, exc_info=True
            )
