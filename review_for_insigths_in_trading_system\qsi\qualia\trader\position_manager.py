from __future__ import annotations

import asyncio
from typing import Any, Dict, List
from datetime import datetime
from collections import defaultdict
import ccxt

from ..core.position import OpenPosition
from ..fee_management import calculate_entry_fee
from ..fee_management import calculate_exit_fee, net_pnl
from ..risk.manager import create_risk_manager
from ..utils.logger import get_logger

logger = get_logger(__name__)

BOLD = "\033[1m"
RED = "\033[91m"
GREEN = "\033[92m"
YELLOW = "\033[93m"
ENDC = "\033[0m"


def setup_risk_managers(trader: Any) -> None:
    """Initialize risk managers for all symbols."""

    logger.debug("Inicializando risk managers para os símbolos: %s", trader.symbols)
    existing = dict(trader.risk_managers)
    for symbol in trader.symbols:
        if symbol in existing:
            logger.debug("Usando risk manager existente para %s", symbol)
            trader.risk_managers[symbol] = existing[symbol]
            continue

        profile_params = trader.risk_profile_configs.get(trader.risk_profile, {})
        trader.risk_managers[symbol] = create_risk_manager(
            initial_capital=trader.capital,
            risk_profile=trader.risk_profile,
            custom_risk_per_trade_pct=trader.risk_per_trade_pct,
            custom_max_position_capital_pct=trader.max_position_capital_pct_custom,
            profile_specific_config=profile_params,
        )

    missing = [s for s in trader.symbols if s not in trader.risk_managers]
    if missing:
        raise ValueError(
            f"Risk managers não foram criados para todos os símbolos: {missing}"
        )


async def sync_open_positions(trader: Any) -> None:
    """Synchronize local positions with the exchange state."""

    logger.info("Iniciando sincronização de posições com a exchange...")
    if not trader.exchange:
        logger.warning("Sincronização abortada: nenhuma conexão com a exchange.")
        return

    if (
        hasattr(trader.exchange, "fetch_positions_supported")
        and trader.exchange.fetch_positions_supported is False
    ):
        logger.info(
            "Exchange nao suporta fetch_positions; reconstruindo a partir de ordens abertas."
        )
        try:
            open_orders = await trader.exchange.fetch_open_orders()
        except Exception as exc:  # pragma: no cover - erro logado
            logger.error(
                "Falha ao buscar ordens abertas para reconstruir posições: %s",
                exc,
                exc_info=True,
            )
            return

        reconstructed: Dict[str, List[OpenPosition]] = defaultdict(list)
        for order in open_orders:
            try:
                total = float(order.get("amount") or order.get("size") or 0.0)
                filled = float(order.get("filled") or 0.0)
                remaining = float(order.get("remaining", total - filled))
                if remaining <= 0:
                    continue
                price = float(order.get("price") or order.get("average") or 0.0)
                ts_raw = order.get("timestamp")
                if ts_raw is not None:
                    ts = datetime.fromtimestamp(ts_raw / 1000.0)
                else:
                    ts = datetime.utcnow()
                pos = OpenPosition(
                    symbol=order.get("symbol"),
                    order_id=str(order.get("id")),
                    entry_price=price,
                    size=remaining,
                    side=order.get("side", "buy"),
                    timestamp=ts,
                )
                reconstructed[pos.symbol].append(pos)
            except Exception as exc:  # pragma: no cover - erro logado
                logger.warning(
                    "Nao foi possivel reconstruir posição da ordem %s: %s",
                    order.get("id"),
                    exc,
                    exc_info=True,
                )

        async with trader._pos_lock:
            trader.open_positions = dict(reconstructed)

        if hasattr(trader, "wallet_state") and trader.wallet_state:
            positions_value = sum(
                p.entry_price * p.size
                for positions in trader.open_positions.values()
                for p in positions
            )
            trader.wallet_state["positions_value"] = positions_value
            trader.wallet_state["available_cash"] = (
                trader.wallet_state.get("current_capital", 0.0) - positions_value
            )

        logger.info(
            "Reconstrucao concluida. %s posições abertas.",
            sum(len(p) for p in trader.open_positions.values()),
        )
        return

    try:
        exchange_positions = await asyncio.wait_for(
            trader.exchange.fetch_positions(trader.symbols),
            timeout=10.0
        )
        logger.info(
            "Recebidas %s posições abertas da exchange para reconciliação.",
            len(exchange_positions),
        )
        for pos in exchange_positions:
            logger.debug(
                "  - Posição da Exchange: %s | Lado: %s | Tamanho: %s",
                pos.get("symbol"),
                pos.get("side"),
                pos.get("contracts") or pos.get("size"),
            )

        real_positions_map = {p["symbol"]: p for p in exchange_positions}

        async with trader._pos_lock:
            local_symbols = list(trader.open_positions.keys())

            for symbol in local_symbols:
                if symbol in real_positions_map:
                    real_pos = real_positions_map[symbol]
                    real_size = float(
                        real_pos.get("contracts", 0) or real_pos.get("size", 0)
                    )
                    real_side = "buy" if real_pos.get("side") == "long" else "sell"

                    local_pos_list = trader.open_positions.get(symbol, [])
                    validated_positions_for_symbol: List[OpenPosition] = []
                    for local_pos in local_pos_list:
                        if local_pos.side == real_side:
                            if abs(local_pos.size - real_size) > 1e-9:
                                logger.warning(
                                    "Sincronizando tamanho para %s: Local=%s, Real=%s",
                                    symbol,
                                    local_pos.size,
                                    real_size,
                                )
                                local_pos.size = real_size
                            validated_positions_for_symbol.append(local_pos)
                            logger.info(
                                "Posição local para %s (ID: %s) VALIDADA.",
                                symbol,
                                local_pos.order_id,
                            )
                        else:
                            logger.warning(
                                "Posição FANTASMA para %s (ID: %s) removida. Lado inconsistente (Local: %s, Real: %s).",
                                symbol,
                                local_pos.order_id,
                                local_pos.side,
                                real_side,
                            )
                    if validated_positions_for_symbol:
                        trader.open_positions[symbol] = validated_positions_for_symbol
                    else:
                        del trader.open_positions[symbol]
                else:
                    logger.warning(
                        "Posição FANTASMA para %s removida. Nenhuma posição correspondente encontrada na exchange.",
                        symbol,
                    )
                    del trader.open_positions[symbol]

        final_pos_count = sum(len(p) for p in trader.open_positions.values())
        logger.info(
            "Sincronização de posições concluída. Posições válidas mantidas: %s. Símbolos com posições: %s",
            final_pos_count,
            list(trader.open_positions.keys()),
        )
    except asyncio.TimeoutError:
        logger.warning(
            "Timeout ao buscar posições na exchange. Sincronização de posições abortada."
        )
    except (ccxt.NetworkError, ccxt.ExchangeError) as e:
        logger.error(
            "Erro de rede/exchange durante a sincronização de posições: %s", e, exc_info=True
        )
    except Exception as e:
        logger.error(
            "Erro CRÍTICO durante a sincronização de posições: %s", e, exc_info=True
        )


async def enforce_position_closure_rules(
    trader: Any, symbol: str, current_price: float
) -> None:
    """Close positions based on metacognition directives or limits."""

    active_positions = trader.open_positions.get(symbol, [])

    if (
        not trader.disable_metacognition
        and hasattr(trader, "metacognition_executive")
        and trader.metacognition_executive
    ):
        signal = trader.metacognition_executive.get_last_trade_signal()
        if (
            signal
            and signal.signal_type in {"BUY", "SELL"}
            and getattr(signal, "symbol", symbol) == symbol
        ):
            opposite = "sell" if signal.signal_type == "BUY" else "buy"
            for pos in list(active_positions):
                if pos.side == opposite:
                    await trader._close_position(
                        symbol, pos.order_id, current_price, "metacognition_contrary"
                    )
            active_positions = trader.open_positions.get(symbol, [])

    rm = trader.risk_managers.get(symbol)
    if (
        rm
        and hasattr(rm, "max_open_positions")
        and len(active_positions) > rm.max_open_positions
    ):
        excess = len(active_positions) - rm.max_open_positions
        for pos in list(active_positions)[:excess]:
            await trader._close_position(
                symbol, pos.order_id, current_price, "max_position_limit"
            )
