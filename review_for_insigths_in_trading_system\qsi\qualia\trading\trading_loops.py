"""Rotinas assíncronas utilizadas no trading em tempo real.

Este módulo centraliza os loops de execução empregados pelo
``QUALIARealTimeTrader``. As funções aqui definidas assumem que o objeto
``trader`` disponibiliza todos os métodos utilizados internamente (como
``_update_market_data`` ou ``_analyze_symbols``).  A separação facilita a
manutenção e testes isolados do fluxo principal.
"""

from __future__ import annotations

__all__ = [
    "handle_critical_failure",
    "repeat_step",
    "monitor_positions_once",
    "monitor_positions_loop",
    "main_loop",
    "_reduce_portfolio_exposure",
    "_position_monitoring_loop",
    "_main_loop",
    "run_once",
    "analyze_arbitrage_opportunities",
]

import asyncio
import time
import uuid
from datetime import datetime, timezone
from typing import TYPE_CHECKING, Awaitable, Callable

import pandas as pd
import numpy as np

from .data_utils import is_data_empty, safe_get_last_close, safe_iloc

from ..utils.logger import get_logger
from ..utils.metrics import record_metric
from ..exceptions import CriticalLoopError
from ..strategies.exceptions import InsufficientHistoryError

logger = get_logger(__name__)

if TYPE_CHECKING:  # pragma: no cover - hints only
    from ..qualia_trading_system import QUALIARealTimeTrader


async def display_wallet_status(trader: "QUALIARealTimeTrader") -> None:
    """Wrapper para atualizar o HUD do trader com o status da carteira."""

    await trader.hud_manager.display_wallet_status(trader)


async def handle_critical_failure(
    trader: "QUALIARealTimeTrader", exc: Exception, pause_seconds: float = 30.0
) -> None:
    """Registra falha crítica e pausa a execução.

    Parameters
    ----------
    trader:
        Instância do trader em execução.
    exc:
        Exceção capturada.
    pause_seconds:
        Intervalo de pausa antes de propagar o erro.
    """

    if getattr(trader, "_critical_failure_handled", False):
        logger.debug("Critical failure already handled; skipping duplicate shutdown")
        raise CriticalLoopError(str(exc)) from exc

    trader._critical_failure_handled = True

    error_message = str(exc).strip() or exc.__class__.__name__
    logger.exception("Falha crítica detectada: %s", error_message, exc_info=True)

    if getattr(trader, "shutdown_event", None) is not None:
        trader.shutdown_event.set()

    trader.trading_active = False
    await asyncio.sleep(pause_seconds)
    raise CriticalLoopError(str(exc)) from exc


async def repeat_step(
    step: Callable[[], Awaitable[None]], shutdown_event: asyncio.Event
) -> None:
    """Execute ``step`` enquanto ``shutdown_event`` não estiver sinalizado.

    Parameters
    ----------
    step
        Corrotina executada a cada iteração.
    shutdown_event
        Evento utilizado para interromper o loop.

    Raises
    ------
    asyncio.CancelledError
        Propagada quando a tarefa é cancelada externamente.
    """

    while not shutdown_event.is_set():
        try:
            await step()
            await asyncio.sleep(0)
        except asyncio.CancelledError:
            logger.debug("repeat_step cancelled")
            raise


async def monitor_positions_once(trader: "QUALIARealTimeTrader") -> None:
    """Verificar SL/TP para todas as posições abertas."""

    from ..qualia_trading_system import MAX_ERROR_HISTORY, TickerFetchError

    if not trader.exchange:
        return

    async with trader._pos_lock:
        open_positions_snapshot = [
            (sym, list(pos_list)) for sym, pos_list in trader.open_positions.items()
        ]

    for symbol, positions in open_positions_snapshot:
        if not positions:
            continue

        if trader.fetch_ticker_enabled:
            try:
                ticker = await trader._fetch_ticker(symbol)
            except TickerFetchError as exc:
                original = exc.__cause__
                if original:
                    logger.warning(
                        "Falha ao buscar ticker para %s no monitoramento: %s | Original: %s: %s",
                        symbol,
                        exc,
                        type(original).__name__,
                        original,
                    )
                else:
                    logger.warning(
                        "Falha ao buscar ticker para %s no monitoramento: %s",
                        symbol,
                        exc,
                    )
                trader._ticker_error_count = min(
                    getattr(trader, "_ticker_error_count", 0) + 1,
                    MAX_ERROR_HISTORY,
                )
                await trader._perform_health_check()
                continue
        else:
            df = trader.market_data.get(symbol, {}).get(trader.primary_timeframe)
            ticker = {"last": None}

            # CORREÇÃO YAA: Usar função tipo-segura para verificar dados vazios
            if not is_data_empty(df):
                last_close = safe_get_last_close(df)
                if last_close is not None:
                    ticker["last"] = last_close
                elif isinstance(df, np.ndarray):
                    logger.debug(
                        "numpy.ndarray recebido para %s, mas sem dados de close válidos",
                        symbol,
                    )
                elif not isinstance(df, pd.DataFrame):
                    logger.warning(
                        "Esperado DataFrame para dados de %s; recebido %s",
                        symbol,
                        type(df),
                    )

        current_price = (
            ticker.get("last")
            or ticker.get("close")
            or ticker.get("bid")
            or ticker.get("ask")
        )

        if current_price is None:
            continue

        for pos in list(positions):
            if pos.stop_loss and (
                (pos.side == "buy" and current_price <= pos.stop_loss)
                or (pos.side == "sell" and current_price >= pos.stop_loss)
            ):
                logger.info("STOP LOSS atingido para %s em monitoramento", symbol)
                await trader._close_position(
                    symbol, pos.order_id, current_price, "stop_loss"
                )
                continue

            if pos.take_profit and (
                (pos.side == "buy" and current_price >= pos.take_profit)
                or (pos.side == "sell" and current_price <= pos.take_profit)
            ):
                logger.info("TAKE PROFIT atingido para %s em monitoramento", symbol)
                await trader._close_position(
                    symbol, pos.order_id, current_price, "take_profit"
                )


async def monitor_positions_loop(trader: "QUALIARealTimeTrader") -> None:
    """Loop de verificação periódica de SL/TP para posições abertas."""

    logger.info("Iniciando monitoramento assíncrono de posições")

    try:
        while not trader.shutdown_event.is_set():
            async with trader._pos_lock:
                snapshot = [(s, list(p)) for s, p in trader.open_positions.items() if p]

            too_many_failures = (
                trader._consecutive_ticker_failures >= trader.ticker_failure_limit
            )
            near_triggers = trader._positions_close_to_triggers(snapshot)

            if near_triggers and not too_many_failures:
                await trader._monitor_positions_once()
                trader._monitor_backoff_level = 0
                trader._current_monitor_interval = trader.position_monitor_interval
            else:
                reason = (
                    "falhas consecutivas" if too_many_failures else "sem SL/TP próximo"
                )
                logger.debug("Ignorando monitoramento de posições: %s", reason)
                trader._monitor_backoff_level += 1
                backoff = trader.position_monitor_interval * (
                    2**trader._monitor_backoff_level
                )
                trader._current_monitor_interval = min(
                    backoff, trader.position_monitor_max_interval
                )

            await asyncio.sleep(trader._current_monitor_interval)
    except asyncio.CancelledError:
        logger.info("Monitoramento de posições cancelado")
    except Exception as exc:  # pragma: no cover - unforeseen errors
        await handle_critical_failure(trader, exc)


async def main_loop(trader: "QUALIARealTimeTrader") -> None:
    """Loop principal de trading.

    Executa todas as rotinas de mercado, controle de duração e tarefas
    auxiliares enquanto ``shutdown_event`` não for acionado. Inclui
    monitoramento de posições e agendamento do QAST. Utilize este loop
    em execuções prolongadas. Para uma versão reduzida veja
    :func:`_main_loop`.

    Parameters
    ----------
    trader : QUALIARealTimeTrader
        Instância do trader a ser executada.
    """

    logger.info("Iniciando loop principal de trading no modo: %s", trader.mode)
    trader.trading_active = True

    try:
        if trader.mode in ["live", "paper_trading"] and getattr(
            trader, "exchange", None
        ):
            if not getattr(trader, "_prevent_reconnect", False):
                await trader.health_check()
            else:
                logger.debug(
                    "Health check ignorado no início do loop: _prevent_reconnect ativo"
                )

        if (
            getattr(trader, "exchange", None)
            and getattr(trader, "_position_monitor_task", None) is None
        ):
            trader._position_monitor_task = asyncio.create_task(
                monitor_positions_loop(trader)
            )

        if (
            getattr(trader, "_qast_schedule_task", None) is None
            and getattr(trader, "qast_schedule_interval_minutes", 0) > 0
        ):
            trader._qast_schedule_task = asyncio.create_task(
                trader._qast_scheduler_loop()
            )

        start_time = time.time()
        trader.cycle_count = 0

        while not trader.shutdown_event.is_set():
            trader._previous_cycle_exception = False
            trader.cycle_count += 1
            record_metric("trading.cycle_count", trader.cycle_count)
            cycle_start = time.time()
            cycle_success = False
            trader._cycle_failure_messages = []

            trace_id = uuid.uuid4().hex
            trader.current_cycle_trace_id = trace_id
            logger.info(
                "TraceID: %s - Iniciando ciclo %s",
                trace_id,
                trader.cycle_count,
            )

            if trader.mode in ["live", "paper_trading"] and getattr(
                trader, "exchange", None
            ):
                try:
                    if not getattr(trader, "_prevent_reconnect", False):
                        await trader.health_check()
                    else:
                        logger.debug("Health check ignorado: _prevent_reconnect ativo")
                        await asyncio.sleep(0)
                except Exception as exc:
                    logger.exception(
                        "Falha ao reconectar com a exchange: %s",
                        exc,
                        exc_info=True,
                    )
                    await asyncio.sleep(5)
                    continue

            if (
                trader.duration_seconds
                and (time.time() - start_time) > trader.duration_seconds
            ):
                logger.info(
                    "Atingida duração máxima de %s segundos (%.2f horas). Iniciando desligamento.",
                    trader.duration_seconds,
                    trader.duration_seconds / 3600,
                )
                trader.shutdown_event.set()
                break

            try:
                await trader._update_market_data(trace_id=trace_id)
                try:
                    await trader._analyze_symbols(trace_id=trace_id)
                except InsufficientHistoryError as exc:
                    logger.warning(
                        "Historico insuficiente detectado ao executar estrategia: %s",
                        exc,
                    )
                    symbols_for_fetch = getattr(
                        trader,
                        "symbols",
                        list(getattr(trader, "strategies", {}).keys()),
                    )
                    for sym in symbols_for_fetch:
                        strat = trader.strategies.get(sym)
                        if strat is None:
                            continue
                        df = trader.market_data.get(sym, {}).get(
                            trader.primary_timeframe
                        )
                        have = len(df) if isinstance(df, pd.DataFrame) else 0
                        required = getattr(strat, "required_initial_data_length", 0)
                        missing = max(0, required - have)
                        if missing == 0 and have == 0:
                            missing = 1
                        if missing > 0:
                            logger.info(
                                "Buscando %s candles adicionais para %s@%s",
                                missing,
                                sym,
                                trader.primary_timeframe,
                            )
                            await trader._fetch_ohlcv_history(
                                sym, trader.primary_timeframe, missing
                            )
                    try:
                        await trader._analyze_symbols(trace_id=trace_id)
                    except InsufficientHistoryError as exc2:
                        wait = trader.config.get(
                            "insufficient_history_retry_interval", 60
                        )
                        logger.warning(
                            "Persistencia de historico insuficiente: %s. Aguardando %.1fs",
                            exc2,
                            wait,
                        )
                        await asyncio.sleep(wait)
                        continue
                symbols = getattr(
                    trader,
                    "symbols",
                    list(getattr(trader, "strategies", {}).keys()),
                )
                if symbols:
                    symbol = symbols[0]
                    df = trader.market_data.get(symbol, {}).get(
                        trader.primary_timeframe
                    )
                    # CORREÇÃO YAA: Usar função tipo-segura para verificar dados vazios
                    if not is_data_empty(df) and isinstance(df, pd.DataFrame):
                        snapshot = {
                            "timestamp": df.index[-1].isoformat(),
                            "close": float(df["close"].iloc[-1]),
                            "volume": float(df["volume"].iloc[-1]),
                            "close_history": df["close"].tail(15).tolist(),
                        }
                        trader.hud_manager.on_tick(snapshot)
                cycle_success = True
            except asyncio.TimeoutError as exc:
                logger.warning("Atualização de mercado excedeu o tempo limite: %s", exc)
                if getattr(trader, "statsd", None):
                    trader.statsd.increment("trading.main_loop.market_update_timeout")
                continue
            except Exception as exc:
                logger.exception("Erro durante o ciclo de trading", exc_info=True)
                trader._previous_cycle_exception = True
                if getattr(trader, "shutdown_event", None) is not None:
                    trader.shutdown_event.set()
                raise

            if (
                getattr(trader, "_last_metacognitive_ctx", None)
                and getattr(trader._last_metacognitive_ctx, "trade_directive", None)
                == "REDUCE_EXPOSURE"
                and any(trader.open_positions.values())
            ):
                logger.info(
                    "Diretiva REDUCE_EXPOSURE recebida, mas o QUALIARealTimeTrader não oferece suporte à redução automática de exposição"
                )

            await trader._perform_health_check()
            await trader.hud_manager.display_wallet_status(trader)

            if trader.cycle_count % 5 == 0:
                trader._save_open_positions()

            try:
                trader._adjust_poll_interval(cycle_success)
            except TypeError:  # pragma: no cover - ignore misconfigured callables
                pass
            cycle_duration = time.time() - cycle_start
            record_metric("trading.cycle_duration_ms", cycle_duration * 1000)

            if trader.poll_interval_override is not None:
                interval = trader.current_poll_interval
                sleep_time = max(0.1, interval - cycle_duration)
            else:
                next_poll = trader._calculate_next_poll_time()
                sleep_time = (next_poll - datetime.now(timezone.utc)).total_seconds()
                sleep_time = max(0.1, sleep_time)
                logger.debug(
                    "Sleep time calculated: %.2fs until %s",
                    sleep_time,
                    next_poll.isoformat(),
                )

            if trader.duration_seconds:
                total_elapsed = time.time() - start_time
                remaining_time = trader.duration_seconds - total_elapsed
                if remaining_time <= 0:
                    sleep_time = 0
                else:
                    sleep_time = min(sleep_time, remaining_time)

            if sleep_time > trader.SLEEP_TIME_WARNING_THRESHOLD:
                logger.warning(
                    "Tempo de espera %.2fs excede limite; timeframes ativos: %s",
                    sleep_time,
                    ", ".join(trader.timeframes),
                )

            if trader._cycle_failure_messages:
                summary = "; ".join(trader._cycle_failure_messages)
                logger.error(
                    "Falhas no ciclo %s: %s",
                    trader.cycle_count,
                    summary,
                )

            if len(trader.trade_history) > trader._last_trade_count:
                trader._cycles_without_trades = 0
                trader._last_trade_count = len(trader.trade_history)
                trader._idle_warning_emitted = False
            else:
                trader._cycles_without_trades += 1
                if (
                    trader._cycles_without_trades >= trader.max_idle_cycles
                    and not trader._idle_warning_emitted
                ):
                    logger.warning(
                        "Nenhuma operação executada nos últimos %d ciclos.",
                        trader._cycles_without_trades,
                    )
                    trader._idle_warning_emitted = True
                    if trader.stop_on_idle:
                        logger.warning(
                            "Encerrando execução devido a inatividade prolongada."
                        )
                        trader.shutdown_event.set()
                        break

            await asyncio.sleep(sleep_time)

            if trader.cycle_count % 10 == 0:
                if trader.duration_seconds:
                    elapsed_pct = (
                        (time.time() - start_time) / trader.duration_seconds
                    ) * 100
                    logger.info(
                        "Ciclo de trading %s concluído. Duração: %.2fs, Progresso: %.1f%%, Símbolos: %s, Posições abertas: %s",
                        trader.cycle_count,
                        cycle_duration,
                        elapsed_pct,
                        len(trader.symbols),
                        sum(len(p) for p in trader.open_positions.values()),
                    )
                else:
                    logger.info(
                        "Ciclo de trading %s concluído. Duração: %.2fs, Símbolos: %s, Posições abertas: %s",
                        trader.cycle_count,
                        cycle_duration,
                        len(trader.symbols),
                        sum(len(p) for p in trader.open_positions.values()),
                    )

    except asyncio.CancelledError:
        logger.warning("Loop principal de trading cancelado.")
    except Exception as exc:
        await handle_critical_failure(trader, exc)
    finally:
        trader.trading_active = False
        logger.info("Loop principal de trading encerrado.")

        if getattr(trader, "_position_monitor_task", None):
            trader._position_monitor_task.cancel()
            try:
                await trader._position_monitor_task
            except asyncio.CancelledError:
                pass
            trader._position_monitor_task = None

        if getattr(trader, "_qast_schedule_task", None):
            trader._qast_schedule_task.cancel()
            try:
                await trader._qast_schedule_task
            except asyncio.CancelledError:
                pass
            trader._qast_schedule_task = None

        await trader._handle_open_positions_on_shutdown()
        if not getattr(trader, "preserve_connection", False):
            await trader.close_exchange()

        if trader.mode == "paper_trading" and not getattr(
            trader, "_final_report_emitted", False
        ):
            try:
                logger.info("Gerando relatório final de %s...", trader.mode)
                report = await trader.generate_performance_report(save_to_file=True)
                metrics = report["performance_metrics"]
                total_trades = report["trade_summary"]["total_trades"]
                win_rate = metrics.get("win_rate", 0)

                logger.info("===== RELATÓRIO DE PERFORMANCE =====")
                logger.info("Modo: %s", trader.mode)
                logger.info("Total de trades: %s", total_trades)
                logger.info("Taxa de vitória: %.2f%%", win_rate)
                logger.info(
                    "PnL total: $%.2f (%.2f%%)",
                    metrics.get("total_pnl", 0),
                    metrics.get("total_pnl_pct", 0),
                )
                logger.info(
                    "Drawdown máximo: %.2f%%",
                    metrics.get("max_drawdown_pct", 0),
                )
                logger.info(
                    "Fator de lucro: %.2f",
                    metrics.get("profit_factor", 0),
                )
                logger.info(
                    "Total de taxas pagas: $%.2f",
                    metrics.get("total_fees_paid", 0),
                )
                logger.info("====================================")
                logger.info(
                    "Relatório de %s gerado com sucesso. Trades totais: %s",
                    trader.mode,
                    total_trades,
                )
                trader._final_report_emitted = True
            except Exception as exc:  # pragma: no cover - análise pós execução
                await handle_critical_failure(trader, exc)

        return trader.trade_history


async def _reduce_portfolio_exposure(
    trader: "QUALIARealTimeTrader", reduction_pct: float
) -> None:
    """No-op for backward compatibility after removing exposure reduction."""
    if reduction_pct <= 0:
        return
    logger.info(
        "Mecanismo de redução de exposição não suportado no QUALIARealTimeTrader; diretiva ignorada (%.1f%%)",
        reduction_pct,
    )


async def _main_loop(trader: "QUALIARealTimeTrader") -> None:
    """Versão simplificada do loop principal.

    Executa apenas a atualização de mercado, a análise de símbolos e o
    ``health_check`` periódico, além da meta-estratégia de arbitragem.
    Utilizado por :func:`run_once` e em alguns fluxos internos do
    ``QUALIARealTimeTrader`` quando o monitoramento de posições e o
    controle de duração do :func:`main_loop` não são necessários.

    Parameters
    ----------
    trader : QUALIARealTimeTrader
        Instância do trader a ser executada.
    """
    while not trader.shutdown_event.is_set():
        loop_start = time.time()

        try:
            # YAA: Executar análise de arbitragem como meta-estratégia
            arbitrage_results = await analyze_arbitrage_opportunities(trader)
            if arbitrage_results.get("signal") == "ARBITRAGE":
                logger.info(
                    f"📊 Meta-estratégia de arbitragem ativa: {arbitrage_results}"
                )
                # Aqui pode-se implementar lógica específica para arbitragem
                # Por enquanto, apenas log das oportunidades detectadas

            # Atualizar dados de mercado
            await trader._update_market_data()

            # Analisar símbolos e gerar sinais
            analysis_results = await trader._analyze_symbols()

            # Incluir resultados de arbitragem na análise
            if arbitrage_results:
                analysis_results = analysis_results or {}
                analysis_results["arbitrage"] = arbitrage_results

            # YAA CORREÇÃO: O método _process_analysis_results não existe.
            # O processamento dos resultados da análise já é feito dentro do _analyze_symbols()
            # que chama _process_trading_decision para cada símbolo automaticamente.
            # Apenas log dos resultados se disponíveis:
            if analysis_results:
                symbols_analyzed = len(analysis_results)
                logger.debug(f"📊 Análise concluída para {symbols_analyzed} símbolos")

            # Health check periódico
            await trader._perform_health_check()

        except Exception as e:
            logger.error(f"❌ Erro no loop principal: {e}", exc_info=True)
            await asyncio.sleep(5.0)  # Pausa antes de tentar novamente

        # Calcular tempo de espera até próximo ciclo
        loop_duration = time.time() - loop_start
        record_metric("trading.loop_duration_ms", loop_duration * 1000)
        sleep_time = max(0.1, trader.current_poll_interval - loop_duration)
        await asyncio.sleep(sleep_time)


async def _position_monitoring_loop(trader: "QUALIARealTimeTrader") -> None:
    """Execute ``monitor_positions_loop`` com timeout e métricas."""
    start_time = time.perf_counter()
    try:
        await asyncio.wait_for(
            monitor_positions_loop(trader), timeout=trader.position_monitor_timeout
        )
    except asyncio.TimeoutError:
        if getattr(trader, "statsd", None):
            trader.statsd.increment("position_monitor.timeout")
        raise
    finally:
        if getattr(trader, "statsd", None):
            trader.statsd.timing(
                "position_monitor.latency", (time.perf_counter() - start_time) * 1000
            )


async def run_once(
    trader: "QUALIARealTimeTrader", *, preserve_connection: bool = True
) -> None:
    """Execute a single iteration of the trading loop.

    Parameters
    ----------
    trader
        Trader instance to operate.
    preserve_connection

        When ``True`` (default), keep the exchange connection open after the
        cycle completes. Set to ``False`` to close it at the end of the
        iteration.

    """

    original_event = trader.shutdown_event
    trader.shutdown_event = asyncio.Event()

    if preserve_connection:
        setattr(trader, "preserve_connection", True)

    async def _stop_after_cycle() -> None:
        await asyncio.sleep(0)
        trader.shutdown_event.set()

    stopper = asyncio.create_task(_stop_after_cycle())
    try:
        await _main_loop(trader)
    finally:
        await stopper
        trader.shutdown_event = original_event
        if preserve_connection:
            delattr(trader, "preserve_connection")


async def analyze_arbitrage_opportunities(trader: "QUALIARealTimeTrader") -> dict:
    """
    🌌 Análise de oportunidades de arbitragem dimensional

    Analisa oportunidades de arbitragem cross-exchange quando o
    Multi-Exchange Manager está ativo. Retorna um dicionário com
    métricas e oportunidades identificadas.
    """

    # Se Multi-Exchange Manager não estiver disponível, retornar vazio
    if (
        not hasattr(trader, "multi_exchange_manager")
        or not trader.multi_exchange_manager
    ):
        logger.debug(
            "Multi-Exchange Manager não disponível - pulando análise de arbitragem"
        )
        return {
            "opportunities_found": 0,
            "total_spread": 0.0,
            "exchanges_analyzed": 0,
            "analysis_timestamp": datetime.now(timezone.utc).isoformat(),
        }

    multi_mgr = trader.multi_exchange_manager

    # Verificar se o manager está rodando
    if not multi_mgr.running or not multi_mgr.exchanges:
        logger.debug("Multi-Exchange Manager não está rodando ou sem exchanges")
        return {
            "opportunities_found": 0,
            "total_spread": 0.0,
            "exchanges_analyzed": 0,
            "analysis_timestamp": datetime.now(timezone.utc).isoformat(),
            "status": "manager_inactive",
        }

    try:
        # Contar exchanges ativas
        active_exchanges = [
            name for name, state in multi_mgr.exchange_states.items() if state.connected
        ]

        if len(active_exchanges) < 2:
            logger.debug(
                f"Insuficientes exchanges ativas para arbitragem: {len(active_exchanges)}"
            )
            return {
                "opportunities_found": 0,
                "total_spread": 0.0,
                "exchanges_analyzed": len(active_exchanges),
                "analysis_timestamp": datetime.now(timezone.utc).isoformat(),
                "status": "insufficient_exchanges",
            }

        # Obter oportunidades do Multi-Exchange Manager
        opportunities = getattr(multi_mgr, "opportunities", [])

        # Filtrar oportunidades válidas (spread acima do mínimo)
        min_spread = multi_mgr.min_spread_threshold
        valid_opportunities = [
            opp for opp in opportunities if opp.spread_pct >= min_spread
        ]

        total_spread = sum(opp.spread_pct for opp in valid_opportunities)

        logger.debug(
            f"Análise de arbitragem: {len(valid_opportunities)} oportunidades válidas "
            f"de {len(opportunities)} total em {len(active_exchanges)} exchanges"
        )

        return {
            "opportunities_found": len(valid_opportunities),
            "total_spread": total_spread,
            "exchanges_analyzed": len(active_exchanges),
            "analysis_timestamp": datetime.now(timezone.utc).isoformat(),
            "status": "success",
            "min_spread_threshold": min_spread,
            "active_exchanges": active_exchanges,
        }

    except Exception as e:
        logger.warning(f"Erro na análise de arbitragem: {e}")
        return {
            "opportunities_found": 0,
            "total_spread": 0.0,
            "exchanges_analyzed": 0,
            "analysis_timestamp": datetime.now(timezone.utc).isoformat(),
            "status": "error",
            "error_message": str(e),
        }
