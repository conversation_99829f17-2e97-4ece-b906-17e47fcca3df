"""Blueprint providing QUALIA Farsight routes.

This blueprint exposes:
1. Web page `/farsight` that renders the new Farsight interface.
2. API endpoint `/api/farsight/insights` returning emerging-technology clusters.

Insights are generated on demand via :class:`FarsightEngine` instead of using a
static mock payload.
"""

from __future__ import annotations

from datetime import datetime, timezone
from typing import List, Dict

import asyncio
from flask import Blueprint, jsonify, render_template, request
from functools import lru_cache

from ...analysis.farsight_engine import FarsightEngine

farsight_bp: Blueprint = Blueprint("farsight", __name__)


@farsight_bp.route("/farsight", methods=["GET"])  # type: ignore[misc]
def farsight_page():
    """Render the QUALIA Farsight UI page."""
    return render_template("farsight.html")


@lru_cache(maxsize=16)
def _get_insights(days_back: int, max_results: int) -> List[Dict[str, object]]:
    """Executa o FarsightEngine com parâmetros e devolve insights."""
    engine = FarsightEngine(days_back=days_back, max_results=max_results)
    return engine.run()


@farsight_bp.route("/api/farsight/insights", methods=["GET"])  # type: ignore[misc]
async def farsight_insights():
    """Return insights generated by :class:`FarsightEngine`."""

    # Extrai parâmetros de query string com defaults
    try:
        days_back = int(request.args.get("days_back", 7))
        max_results = int(request.args.get("max_results", 40))
    except ValueError:
        return jsonify({"success": False, "message": "Parâmetros inválidos"}), 400

    loop = asyncio.get_running_loop()
    insights: List[Dict[str, object]] = await loop.run_in_executor(  # type: ignore[misc]
        None, _get_insights, days_back, max_results
    )

    return jsonify(
        {
            "success": True,
            "generated_at": datetime.now(timezone.utc).isoformat(),
            "insights": insights,
        }
    )
