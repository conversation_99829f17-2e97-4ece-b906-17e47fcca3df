"""Aplicativo Dash para monitorar o QUALIA Cortex.

Este módulo implementa um dashboard simplificado usado para exibir
métricas e padrões do QUALIA Cortex. Os dados exibidos são simulados e
servem apenas para ilustrar como o painel pode ser utilizado durante o
desenvolvimento.

Uso:
    1. Garanta que ``dash`` e ``dash-bootstrap-components`` estejam
       instalados.
    2. Execute ``python cortex_dashboard_app.py``.
    3. Acesse ``http://127.0.0.1:8050/`` no navegador para visualizar o
       painel.
"""

import dash
from dash import dcc, html, Input, Output, dash_table
from dash.development.base_component import Component
import dash_bootstrap_components as dbc
import plotly.graph_objects as go
import pandas as pd
import numpy as np
import datetime
from datetime import timezone
import json  # Para exibir CortexPatterns

# Para rodar este aplicativo:
# 1. Certifique-se de que 'dash', 'pandas', 'dash-bootstrap-components' estão instalados.
# 2. Execute este script python.
# 3. Abra seu navegador e vá para http://127.0.0.1:8050/

# Inicializar o aplicativo Dash com um tema Bootstrap
app = dash.Dash(
    __name__, external_stylesheets=[dbc.themes.CYBORG], title="QUALIA Cortex Dashboard"
)

# --- Layout do Dashboard ---
app.layout = dbc.Container(
    fluid=True,
    children=[
        # Store para acionar callbacks ou manter estado se necessário
        dcc.Store(id="dummy-store"),
        dcc.Interval(
            id="interval-component",
            interval=5 * 1000,  # em milissegundos (e.g., 5 segundos)
            n_intervals=0,
        ),
        # Linha do Título
        dbc.Row(
            dbc.Col(
                html.H1(
                    "QUALIA Cortex - Painel de Consciência Quântica",
                    className="text-center text-primary mb-4",
                ),
                width=12,
            )
        ),
        # Linha 1: Métricas de Performance e SelfObserver
        dbc.Row(
            [
                dbc.Col(
                    dbc.Card(
                        [
                            dbc.CardHeader("Latência do Córtex (ms)"),
                            dbc.CardBody([dcc.Graph(id="latency-graph")]),
                        ]
                    ),
                    width=12,
                    lg=4,
                    className="mb-4",
                ),
                dbc.Col(
                    dbc.Card(
                        [
                            dbc.CardHeader("Uso de Memória do Córtex (MB)"),
                            dbc.CardBody([dcc.Graph(id="memory-graph")]),
                        ]
                    ),
                    width=12,
                    lg=4,
                    className="mb-4",
                ),
                dbc.Col(
                    dbc.Card(
                        [
                            dbc.CardHeader("Métricas do SelfObserver"),
                            dbc.CardBody(
                                [
                                    html.H5(
                                        "Delta de Entropia (último ciclo):",
                                        className="card-title",
                                    ),
                                    html.P(
                                        id="entropy-delta-value",
                                        children="N/A",
                                        className="card-text",
                                    ),
                                    html.H5(
                                        "Contagem de Padrões (total):",
                                        className="card-title mt-3",
                                    ),
                                    html.P(
                                        id="cortex-pattern-count",
                                        children="N/A",
                                        className="card-text",
                                    ),
                                ]
                            ),
                        ]
                    ),
                    width=12,
                    lg=4,
                    className="mb-4",
                ),
            ]
        ),
        # Linha 2: Expressão Simbólica e Padrões do Córtex
        dbc.Row(
            [
                dbc.Col(
                    dbc.Card(
                        [
                            dbc.CardHeader("Expressão Simbólica (Auto-Reflexão)"),
                            dbc.CardBody(
                                [
                                    html.H5(
                                        "Expressão Atual (symbolic_expr):",
                                        className="card-title",
                                    ),
                                    dcc.Markdown(
                                        id="symbolic-expr-display",
                                        children="""```
Nenhuma expressão carregada.
```""",
                                        className="mb-3",
                                    ),
                                    html.H5(
                                        "Delta da Expressão (delta_symbolic_expr):",
                                        className="card-title",
                                    ),
                                    dcc.Markdown(
                                        id="delta-symbolic-expr-display",
                                        children="""```
Nenhum delta carregado.
```""",
                                    ),
                                ]
                            ),
                        ]
                    ),
                    width=12,
                    lg=6,
                    className="mb-4",
                ),
                dbc.Col(
                    dbc.Card(
                        [
                            dbc.CardHeader("Padrões do Córtex Detectados Recentemente"),
                            dbc.CardBody(
                                [
                                    html.Div(
                                        id="patterns-display",
                                        children="Nenhum padrão detectado recentemente.",
                                    )
                                ]
                            ),
                        ]
                    ),
                    width=12,
                    lg=6,
                    className="mb-4",
                ),
            ]
        ),
    ],
)

# --- Callbacks para Atualizar Dados (com Mock Data) ---

mock_data_store = {
    "latency_detect_patterns": [],
    "latency_full_cycle": [],
    "memory_detect_patterns": [],
    "memory_full_cycle": [],
    "timestamps": [],
    "entropy_delta": 0.0,
    "pattern_count": 0,
    "symbolic_expr": "S_0 = \\sum_{i} w_i \\cdot f_i(x)",
    "delta_symbolic_expr": "\\Delta S = 0.1 \\cdot \frac{\\partial S}{\\partial w_k}",
    "cortex_patterns": [],
}

MAX_DATA_POINTS = 30


@app.callback(
    [
        Output("latency-graph", "figure"),
        Output("memory-graph", "figure"),
        Output("entropy-delta-value", "children"),
        Output("cortex-pattern-count", "children"),
        Output("symbolic-expr-display", "children"),
        Output("delta-symbolic-expr-display", "children"),
        Output("patterns-display", "children"),
    ],
    [Input("interval-component", "n_intervals")],
)
def update_metrics_and_data(
    n: int,
) -> tuple[go.Figure, go.Figure, str, str, str, str, Component | str]:
    now = datetime.datetime.now(timezone.utc)
    mock_data_store["timestamps"].append(now)

    mock_data_store["latency_detect_patterns"].append(np.random.uniform(80, 250))
    mock_data_store["latency_full_cycle"].append(np.random.uniform(150, 400))

    mock_data_store["memory_detect_patterns"].append(np.random.uniform(1.5, 3.0))
    mock_data_store["memory_full_cycle"].append(np.random.uniform(3.0, 6.0))

    mock_data_store["entropy_delta"] = np.random.normal(0, 0.05)
    mock_data_store["pattern_count"] += np.random.randint(0, 3)

    if n % 5 == 0:
        mock_data_store["symbolic_expr"] = (
            f"S_{n//5} = \\sum w_i \\cdot f_i(x) + {np.random.rand():.2f}"
        )
        mock_data_store["delta_symbolic_expr"] = (
            f"\\Delta S = {np.random.rand()*0.2:.2f} \\cdot \nabla L"
        )

    if np.random.rand() > 0.7:
        new_pattern = {
            "pattern_id": f"P-{now.strftime('%H%M%S')}-{np.random.randint(100,999)}",
            "timestamp": now.isoformat(),
            "complexity_score": np.random.uniform(0.5, 1.0),
            "related_symbols": [
                f"sym_{i}" for i in np.random.choice(10, 3, replace=False)
            ],
            "description": f"Padrão simulado {n}",
        }
        mock_data_store["cortex_patterns"].append(new_pattern)
        if len(mock_data_store["cortex_patterns"]) > 10:
            mock_data_store["cortex_patterns"] = mock_data_store["cortex_patterns"][
                -10:
            ]

    for key in [
        "timestamps",
        "latency_detect_patterns",
        "latency_full_cycle",
        "memory_detect_patterns",
        "memory_full_cycle",
    ]:
        mock_data_store[key] = mock_data_store[key][-MAX_DATA_POINTS:]

    latency_fig = go.Figure()
    latency_fig.add_trace(
        go.Scatter(
            x=mock_data_store["timestamps"],
            y=mock_data_store["latency_detect_patterns"],
            mode="lines+markers",
            name="_detect_patterns",
        )
    )
    latency_fig.add_trace(
        go.Scatter(
            x=mock_data_store["timestamps"],
            y=mock_data_store["latency_full_cycle"],
            mode="lines+markers",
            name="Ciclo Completo",
        )
    )
    latency_fig.update_layout(
        title="Latência (ms)",
        xaxis_title="Tempo",
        yaxis_title="ms",
        template="plotly_dark",
        margin=dict(l=20, r=20, t=40, b=20),
    )

    memory_fig = go.Figure()
    memory_fig.add_trace(
        go.Scatter(
            x=mock_data_store["timestamps"],
            y=mock_data_store["memory_detect_patterns"],
            mode="lines+markers",
            name="_detect_patterns",
        )
    )
    memory_fig.add_trace(
        go.Scatter(
            x=mock_data_store["timestamps"],
            y=mock_data_store["memory_full_cycle"],
            mode="lines+markers",
            name="Ciclo Completo",
        )
    )
    memory_fig.update_layout(
        title="Uso de Memória (MB)",
        xaxis_title="Tempo",
        yaxis_title="MB",
        template="plotly_dark",
        margin=dict(l=20, r=20, t=40, b=20),
    )

    sym_expr_md = f"""```
{mock_data_store['symbolic_expr']}
```"""
    delta_sym_expr_md = f"""```
{mock_data_store['delta_symbolic_expr']}
```"""

    patterns_display_content = "Nenhum padrão detectado recentemente."
    if mock_data_store["cortex_patterns"]:
        patterns_md_list = []
        for p in reversed(mock_data_store["cortex_patterns"]):
            symbols_json = json.dumps(p["related_symbols"])
            patterns_md_list.append(f"**ID:** {p['pattern_id']} ({p['timestamp']})")
            patterns_md_list.append(f"  *Complexidade:* {p['complexity_score']:.3f}")
            patterns_md_list.append(f"  *Símbolos:* {symbols_json}")
            patterns_md_list.append(f"  *Descrição:* {p['description']}")
            patterns_md_list.append("---")
        if patterns_md_list:  # Certifica que a lista não está vazia antes de juntar
            patterns_display_content = dcc.Markdown("\n".join(patterns_md_list))

    return (
        latency_fig,
        memory_fig,
        f"{mock_data_store['entropy_delta']:.4f}",
        f"{mock_data_store['pattern_count']}",
        sym_expr_md,
        delta_sym_expr_md,
        patterns_display_content,
    )


if __name__ == "__main__":
    app.run_server(debug=True, port=8050)
