from __future__ import annotations

"""Painel simples para exibir sinais publicados em tempo real."""

import datetime
from typing import Any

import dash
from dash import html, dcc, Output, Input

from ...memory.event_bus import SimpleEventBus
from ...utils.logger import get_logger

logger = get_logger(__name__)

_event_bus: SimpleEventBus | None = None


def init_app(external_bus: SimpleEventBus) -> dash.Dash:
    """Inicializa o painel de sinais."""
    global _event_bus
    _event_bus = external_bus

    app = dash.Dash(__name__)
    app.layout = html.Div(
        [
            html.H2("Sinais de Estratégia"),
            html.Table(id="signals-table"),
            dcc.Interval(id="interval", interval=1000, n_intervals=0),
        ]
    )

    @app.callback(Output("signals-table", "children"), Input("interval", "n_intervals"))
    def _update(_: int):  # noqa: D401
        if not hasattr(_update, "data"):
            _update.data = []  # type: ignore[attr-defined]
        header = html.Tr([html.Th("Timestamp"), html.Th("Sinal"), html.Th("Confiança")])
        rows = [
            html.Tr([html.Td(ts), html.Td(sig), html.Td(f"{conf:.2f}")])
            for ts, sig, conf in reversed(_update.data[-20:])
        ]
        return [header, *rows]

    def _on_signal(payload: dict[str, Any]) -> None:
        ts = datetime.datetime.fromtimestamp(payload.get("timestamp", 0)).strftime(
            "%H:%M:%S"
        )
        sig = payload.get("signal", "")
        conf = float(payload.get("confidence", 0.0))
        if not hasattr(_update, "data"):
            _update.data = []  # type: ignore[attr-defined]
        _update.data.append((ts, sig, conf))  # type: ignore[attr-defined]
        _update.data = _update.data[-100:]  # type: ignore[attr-defined]

    external_bus.subscribe("strategy.signal", _on_signal)
    logger.info("Painel de sinais inicializado e inscrito em strategy.signal")

    return app
