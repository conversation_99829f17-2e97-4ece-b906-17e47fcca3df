"""Trading interface package."""

from .state import TRADING_STATE, TradingState

# Utilities for development and testing only
from ...market.data_simulators import (
    generate_mock_market_data,
    simulate_positions,
    simulate_trade_history,
    simulate_performance_metrics,
    simulate_risk_metrics,
)
from .routes import (
    trading_bp,
    set_trading_engine,
    set_trading_state,
)

__all__ = [
    "TRADING_STATE",
    "TradingState",
    "trading_bp",
    "set_trading_engine",
    "set_trading_state",
]
