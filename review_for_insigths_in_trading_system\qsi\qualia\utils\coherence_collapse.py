"""Utilities for collapsing actions based on vector coherence."""

from __future__ import annotations

from typing import Sequence, Dict, Any

import numpy as np

from ..signals import SignalType

from .logger import get_logger

logger = get_logger(__name__)

__all__ = ["collapse_by_coherence"]


def collapse_by_coherence(
    external_vector: Sequence[float],
    internal_vector: Sequence[float],
    threshold: float = 0.6,
) -> Dict[str, Any]:
    """Collapse a decision by measuring vector coherence.

    Parameters
    ----------
    external_vector : Sequence[float]
        Intention vector from the external market.
    internal_vector : Sequence[float]
        Intention vector from the social simulation.
    threshold : float, default=0.6
        Minimum coherence to consider the vectors aligned.

    Returns
    -------
    Dict[str, Any]
        Dictionary with ``SignalType`` in ``action`` alongside ``confidence``,
        ``coherence`` value and a boolean ``paradox`` flag.
    """
    if len(external_vector) != len(internal_vector):
        raise ValueError("external_vector and internal_vector must be the same length")

    ext = np.asarray(external_vector, dtype=float)
    intr = np.asarray(internal_vector, dtype=float)

    if ext.size != intr.size:
        raise ValueError("external_vector and internal_vector must be the same size")

    if np.linalg.norm(ext) == 0 or np.linalg.norm(intr) == 0:
        coherence = 0.0
    else:
        ext = ext / np.linalg.norm(ext)
        intr = intr / np.linalg.norm(intr)
        coherence = float(np.dot(ext, intr))

    action = SignalType.HOLD
    confidence = 0.0
    paradox = False

    if coherence >= threshold:
        combined = ext + intr
        idx = int(np.argmax(combined))
        action = [SignalType.BUY, SignalType.SELL, SignalType.HOLD][idx]
        confidence = min(1.0, abs(coherence))
    elif coherence <= -threshold:
        paradox = True

    logger.debug(
        "Coherence collapse: ext=%s intr=%s coh=%.3f action=%s",
        external_vector,
        internal_vector,
        coherence,
        action.value,
    )

    return {
        "action": action,
        "confidence": confidence,
        "coherence": coherence,
        "paradox": paradox,
    }
