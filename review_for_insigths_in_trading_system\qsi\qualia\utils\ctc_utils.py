"""
ctc_utils.py - Utilitários para Closed Timelike Curves (CTC) no QUALIA Framework

Este módulo fornece funções para trabalhar com Curvas Temporais Fechadas (CTCs)
no modelo de Deutsch, incluindo criação de circuitos de interação, cálculo de
pontos fixos, e métricas de pureza.

Os parâmetros ``MAX_ITERATIONS`` e ``CONVERGENCE_THRESHOLD`` são carregados em
tempo de execução via :func:`src.qualia.config.ctc_defaults.load_ctc_defaults`,
permitindo ajuste sem alteração de código.
"""

from typing import Tuple, Union, List
import numpy as np
from ..utils.logger import get_logger
from ..config.ctc_defaults import load_ctc_defaults

logger = get_logger(__name__)

# Verificação de existência do Qiskit
HAS_QISKIT = False

# Parâmetros de configuração -------------------------------------------------
_CTC_DEFAULTS = load_ctc_defaults()
MAX_ITERATIONS = int(_CTC_DEFAULTS.get("max_iterations", 100))
CONVERGENCE_THRESHOLD = float(_CTC_DEFAULTS.get("convergence_threshold", 1e-8))


def is_qiskit_available():
    """
    Verifica se o Qiskit está disponível para uso.

    Returns:
        bool: True se Qiskit estiver disponível, False caso contrário
    """
    return HAS_QISKIT


# Para Qiskit 2.0.0+, a estrutura de importação é diferente
try:
    # Importações para Qiskit 2.0+
    from qiskit import QuantumCircuit, QuantumRegister
    from qiskit.quantum_info import DensityMatrix, Statevector, partial_trace, Operator

    try:
        # Este módulo foi movido em versões mais recentes
        from qiskit.circuit.library import Initialize
    except ImportError:
        try:
            from qiskit.extensions import Initialize
        except ImportError:
            # Algumas versões não possuem Initialize na mesma localização
            Initialize = None
    HAS_QISKIT = True
    logger.info("Qiskit 2.0+ encontrado e importado com sucesso.")
except ImportError as e:
    logger.warning("Erro ao importar Qiskit 2.0+: %s", e)
    # Se falhar, tentar importar da estrutura legada
    try:
        from qiskit.legacy import QuantumCircuit, QuantumRegister
        from qiskit.quantum_info import (
            DensityMatrix,
            Statevector,
            partial_trace,
            Operator,
        )

        try:
            from qiskit.extensions import Initialize
        except ImportError:
            Initialize = None
        HAS_QISKIT = True
        logger.info("Qiskit legado (<2.0) encontrado e importado com sucesso.")
    except ImportError as e:
        # Se ainda falhar, mostrar aviso
        logger.warning(
            "Aviso: Qiskit não encontrado. Funcionalidades CTC requerem qiskit. Erro: %s",
            e,
        )


def create_deutsch_ctc_interaction(
    n_qubits_cr: int, n_qubits_ctc: int, interaction_type: str = "swap"
) -> "QuantumCircuit":
    """
    Cria um circuito de interação para CTC de Deutsch.

    Args:
        n_qubits_cr: Número de qubits do sistema chronology-respecting (CR)
        n_qubits_ctc: Número de qubits do sistema CTC
        interaction_type: Tipo de interação ("swap", "cx", "ccx", "hadamard")

    Returns:
        Circuito quântico com a interação especificada
    """
    if not HAS_QISKIT:
        raise ImportError("Qiskit é necessário para criar circuitos CTC")

    # Criar registros quânticos para CR e CTC
    cr_reg = QuantumRegister(n_qubits_cr, "cr")
    ctc_reg = QuantumRegister(n_qubits_ctc, "ctc")

    # Criar circuito combinado
    circuit = QuantumCircuit(cr_reg, ctc_reg)

    # Adicionar portas baseadas no tipo de interação
    if interaction_type == "cx":
        # Interação CNOT entre os primeiros qubits
        if n_qubits_cr > 0 and n_qubits_ctc > 0:
            circuit.cx(cr_reg[0], ctc_reg[0])

    elif interaction_type == "swap":
        # Interação SWAP entre os primeiros qubits
        if n_qubits_cr > 0 and n_qubits_ctc > 0:
            circuit.swap(cr_reg[0], ctc_reg[0])

    elif interaction_type == "ccx":
        # Interação Toffoli se tivermos qubits suficientes
        if n_qubits_cr >= 2 and n_qubits_ctc > 0:
            circuit.ccx(cr_reg[0], cr_reg[1], ctc_reg[0])

    elif interaction_type == "hadamard":
        # Transformação Hadamard nos qubits CTC
        for i in range(n_qubits_ctc):
            circuit.h(ctc_reg[i])

        # CNOT entre CR e CTC
        if n_qubits_cr > 0 and n_qubits_ctc > 0:
            circuit.cx(cr_reg[0], ctc_reg[0])

    else:
        raise ValueError(f"Tipo de interação desconhecido: {interaction_type}")

    return circuit


def solve_deutsch_ctc(
    rho_CR: Union[np.ndarray, "DensityMatrix", "Statevector"],
    U_circuit: Union[np.ndarray, "QuantumCircuit", "Operator"],
    n_qubits_cr: int,
    n_qubits_ctc: int,
    max_iterations: int = MAX_ITERATIONS,
    threshold: float = CONVERGENCE_THRESHOLD,
) -> Tuple[np.ndarray, int, float]:
    """
    Resolve a equação de consistência de Deutsch CTC.

    Args:
        rho_CR: Estado inicial do sistema chronology-respecting (CR)
        U_circuit: Circuito ou operador U que descreve a interação entre CR e CTC
        n_qubits_cr: Número de qubits do sistema CR
        n_qubits_ctc: Número de qubits do sistema CTC
        max_iterations: Número máximo de iterações para convergência
        threshold: Limiar de diferença para considerar convergência

    Returns:
        Tuple contendo (matriz densidade do ponto fixo CTC, número de iterações, erro final)
    """
    if not HAS_QISKIT:
        raise ImportError("Qiskit é necessário para resolver CTCs")

    # Converter entrada para DensityMatrix se necessário
    if isinstance(rho_CR, Statevector):
        rho_CR = DensityMatrix(rho_CR)
    elif not isinstance(rho_CR, DensityMatrix):
        rho_CR = DensityMatrix(rho_CR)

    # Converter circuito para operador se necessário
    if isinstance(U_circuit, QuantumCircuit):
        # Em versões do Qiskit anteriores à 2.0, ``to_operator`` não estava disponível
        try:
            U = U_circuit.to_operator().data
        except AttributeError:
            # Para versões mais recentes
            from qiskit.quantum_info import Operator

            U = Operator(U_circuit).data
    else:
        U = U_circuit

    # Dimensões dos sistemas
    dim_ctc = 2**n_qubits_ctc

    # Iniciar com estado maximamente mixto para CTC
    rho_CTC = np.eye(dim_ctc) / dim_ctc

    # Iterações de ponto fixo
    for iteration in range(max_iterations):
        # Salvar estado atual para verificar convergência
        rho_CTC_prev = rho_CTC.copy()

        # Criar estado combinado: tensor product entre CR e CTC
        rho_combined = np.kron(rho_CR.data, rho_CTC)

        # Aplicar U
        rho_after_U = U @ rho_combined @ U.conj().T

        # Traço parcial sobre CR: ficar só com estado CTC
        # Em numpy puro:
        rho_CTC = partial_trace(rho_after_U, [i for i in range(n_qubits_cr)])

        # Verificar convergência
        error = np.linalg.norm(rho_CTC - rho_CTC_prev)
        if error < threshold:
            return rho_CTC, iteration + 1, error

    # Se chegou aqui, não convergiu dentro do limite
    return rho_CTC, max_iterations, error


def calc_purity(rho: Union[np.ndarray, "DensityMatrix"]) -> float:
    """
    Calcula a pureza de um estado quântico.

    Args:
        rho: Matriz densidade ou array numpy

    Returns:
        Pureza do estado (Tr(ρ²))
    """
    if not HAS_QISKIT:
        # Implementação em numpy puro
        if isinstance(rho, np.ndarray):
            return float(np.trace(rho @ rho).real)
        else:
            raise TypeError("Sem Qiskit, rho deve ser um array numpy")

    # Usar a implementação do Qiskit se disponível
    if isinstance(rho, DensityMatrix):
        # Método direto em versões recentes do Qiskit
        try:
            return float(rho.purity())
        except AttributeError:
            # Cálculo manual se .purity() não estiver disponível
            return float(np.trace(rho.data @ rho.data).real)
    else:
        # Para arrays numpy
        rho_array = rho
        if not isinstance(rho, np.ndarray):
            rho_array = np.array(rho)
        return float(np.trace(rho_array @ rho_array).real)


def initial_ctc_state(dim_ctc: int, kind: str = "mixed") -> np.ndarray:
    """
    Cria um estado inicial para o sistema CTC.

    Args:
        dim_ctc: Dimensão do espaço de Hilbert do sistema CTC
        kind: Tipo de estado ('mixed', 'pure_random', 'zero')

    Returns:
        Matriz densidade representando o estado inicial
    """
    if kind == "mixed":
        # Estado maximamente mixto
        return np.eye(dim_ctc) / dim_ctc
    elif kind == "pure_random":
        # Estado puro aleatório
        psi = np.random.normal(0, 1, dim_ctc) + 1j * np.random.normal(0, 1, dim_ctc)
        psi = psi / np.linalg.norm(psi)
        return np.outer(psi, psi.conj())
    elif kind == "zero":
        # Estado |0⟩
        state = np.zeros(dim_ctc)
        state[0] = 1.0
        return np.outer(state, state.conj())
    else:
        raise ValueError(f"Tipo de estado desconhecido: {kind}")


def decompose_density_matrix(
    rho: Union[np.ndarray, "DensityMatrix"], threshold: float = 1e-6
) -> List[Tuple[float, np.ndarray]]:
    """
    Decompõe uma matriz densidade em uma mistura de estados puros.

    Args:
        rho: Matriz densidade para decompor
        threshold: Limiar para considerar autovalores

    Returns:
        Lista de tuplas (probabilidade, vetor de estado)
    """
    if isinstance(rho, DensityMatrix):
        rho_array = rho.data
    else:
        rho_array = np.array(rho)

    # Calcular autovalores e autovetores
    eigenvalues, eigenvectors = np.linalg.eigh(rho_array)

    # Ordenar em ordem decrescente
    idx = eigenvalues.argsort()[::-1]
    eigenvalues = eigenvalues[idx]
    eigenvectors = eigenvectors[:, idx]

    # Criar a decomposição
    decomposition = []
    for i, value in enumerate(eigenvalues):
        if value > threshold:
            state = eigenvectors[:, i]
            decomposition.append((float(value.real), state))

    return decomposition


__all__ = [
    "create_deutsch_ctc_interaction",
    "solve_deutsch_ctc",
    "calc_purity",
    "initial_ctc_state",
    "decompose_density_matrix",
    "is_qiskit_available",
]
