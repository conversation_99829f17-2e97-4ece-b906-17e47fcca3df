"""Display utilities for the QUALIA Quantum Dashboard.

Este módulo reúne helpers para formatação e exibição de resultados quânticos.
As funções abaixo centralizam a criação de diagramas de circuitos, a
transformação de vetores de estado e contagens de medições em tabelas e a
exportação de dados em formatos consumíveis por dashboards externos.
"""

from __future__ import annotations

import io
from ..utils.logger import get_logger
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from qiskit import QuantumCircuit
from typing import Any, Dict, Optional

logger = get_logger(__name__)


def get_circuit_drawing(circuit: QuantumCircuit) -> plt.Figure:
    """
    Generate a matplotlib figure of the quantum circuit diagram

    Args:
        circuit: The quantum circuit to visualize

    Returns:
        A matplotlib figure containing the circuit diagram
    """
    try:
        fig = circuit.draw(
            output="mpl",
            style={"backgroundcolor": "#FFFFFF"},
            filename=None,
            scale=0.8,
            fold=15,
        )
        fig.tight_layout()
        return fig
    except Exception as exc:
        logger.error("Falha ao gerar desenho do circuito: %s", exc)
        plt.figure(figsize=(8, 3))
        plt.text(
            0.5,
            0.5,
            f"Circuit visualization failed: {exc}\n\nCircuit information:\n{circuit}",
            ha="center",
            va="center",
            fontsize=10,
            wrap=True,
        )
        plt.axis("off")
        return plt.gcf()


def format_statevector(statevector: np.ndarray) -> pd.DataFrame:
    """Return a formatted view of ``statevector`` suitable for display."""

    try:
        if statevector is None or not isinstance(statevector, np.ndarray):
            logger.warning(
                "Statevector vazio ou inválido fornecido a format_statevector"
            )
            return pd.DataFrame({"Error": ["Invalid statevector data"]})

        # Calculate amplitudes and probabilities
        n_qubits = int(np.log2(len(statevector)))
        states = [format(i, f"0{n_qubits}b") for i in range(len(statevector))]

        amplitudes = statevector
        probabilities = np.abs(statevector) ** 2
        phases = np.angle(statevector)

        # Create dataframe
        df = pd.DataFrame(
            {
                "State": states,
                "Real": amplitudes.real,
                "Imaginary": amplitudes.imag,
                "Amplitude": np.abs(amplitudes),
                "Probability": probabilities,
                "Phase": phases,
            }
        )

        # Sort by probability (descending)
        df = df.sort_values("Probability", ascending=False)

        return df
    except Exception as exc:
        logger.error("Erro ao formatar statevector: %s", exc)
        return pd.DataFrame({"Error": [f"Failed to format statevector: {exc}"]})


def format_counts(
    counts: Dict[str, int], n_qubits: Optional[int] = None
) -> pd.DataFrame:
    """
    Format measurement counts for display

    Args:
        counts: Dictionary of measurement counts
        n_qubits: Number of qubits (optional)

    Returns:
        A pandas DataFrame with formatted counts data
    """
    try:
        if not counts:
            return pd.DataFrame({"Error": ["No counts data available"]})

        if n_qubits is None:
            n_qubits = max(len(state) for state in counts.keys())

        # Calculate probabilities
        total_shots = sum(counts.values())

        state_labels = []
        for state in counts.keys():
            try:
                state_labels.append(format(int(state, 2), f"0{n_qubits}b"))
            except ValueError:
                state_labels.append(str(state))

        df = pd.DataFrame(
            {
                "State": state_labels,
                "Counts": list(counts.values()),
                "Probability": [count / total_shots for count in counts.values()],
            }
        )

        # Sort by counts (descending)
        df = df.sort_values("Counts", ascending=False)

        return df
    except Exception as exc:
        logger.error("Erro ao formatar counts: %s", exc)
        return pd.DataFrame({"Error": [f"Failed to format counts: {exc}"]})


def export_results_to_csv(results: Dict[str, Any]) -> str:
    """
    Export simulation results to CSV format

    Args:
        results: Simulation results dictionary

    Returns:
        CSV data as a string
    """
    logger.debug("Exportando resultados para CSV: %s", list(results.keys()))
    output = io.StringIO()

    # Export counts if available
    if "counts" in results and results["counts"]:
        counts_df = format_counts(results["counts"])
        counts_df.to_csv(output, index=False)
        output.write("\n\n")

    # Export statevector if available
    if "statevector" in results and results["statevector"] is not None:
        statevector_df = format_statevector(results["statevector"])
        statevector_df.to_csv(output, index=False)

    return output.getvalue()


def export_circuit_to_qasm(circuit: QuantumCircuit) -> str:
    """
    Export circuit to OpenQASM 2.0 format

    Args:
        circuit: Quantum circuit to export

    Returns:
        OpenQASM representation as a string
    """
    try:
        return circuit.qasm()
    except Exception as exc:
        logger.error("Falha ao exportar circuito para QASM: %s", exc)
        return f"// Failed to export circuit to QASM: {exc}"
