from __future__ import annotations

"""Feature toggle utilities for QUALIA."""

import os
import importlib
from typing import Dict

_base_toggle = None

# Cache for toggle values
_FEATURE_CACHE: Dict[str, bool] = {}


def feature_toggle(
    name: str, default: bool = False, env_var: str | None = None
) -> bool:
    """Return the boolean state of a feature flag.

    Parameters
    ----------
    name : str
        Identifier of the feature toggle.
    default : bool, optional
        Value returned when the toggle is not defined.
    env_var : str, optional
        Environment variable to read directly. When ``None`` the
        ``QUALIA_FT_<NAME>`` format from ``qualia.config`` is used.
    """

    key = env_var or name.upper()
    if key not in _FEATURE_CACHE:
        global _base_toggle
        if _base_toggle is None:
            _base_toggle = importlib.import_module(
                "qualia.config.feature_flags"
            ).feature_toggle
        if env_var is None:
            _FEATURE_CACHE[key] = _base_toggle(name, default)
        else:
            value = os.getenv(env_var)
            if value is None:
                _FEATURE_CACHE[key] = default
            else:
                _FEATURE_CACHE[key] = value.strip().lower() in {
                    "1",
                    "true",
                    "yes",
                    "on",
                }
    return _FEATURE_CACHE[key]


def clear_feature_cache() -> None:
    """Clear cached feature toggle values."""

    _FEATURE_CACHE.clear()


def dynamic_feature_toggle(
    name: str, default: bool = False, env_var: str | None = None
) -> bool:
    """Return the current state of a feature flag without caching.

    This helper allows toggles to be updated at runtime by reading the
    environment variable each time it is called.

    Parameters
    ----------
    name
        Identifier of the feature toggle.
    default
        Value returned when the toggle is not defined.
    env_var
        Environment variable to read directly. When ``None`` the
        ``QUALIA_FT_<NAME>`` format from ``qualia.config`` is used.
    """

    key = env_var or f"QUALIA_FT_{name.upper()}"
    value = os.getenv(key)
    if value is not None:
        return value.strip().lower() in {"1", "true", "yes", "on"}

    global _base_toggle
    if _base_toggle is None:
        _base_toggle = importlib.import_module(
            "qualia.config.feature_flags"
        ).feature_toggle
    return _base_toggle(name, default)


__all__ = ["feature_toggle", "clear_feature_cache", "dynamic_feature_toggle"]
