"""Hardware acceleration utilities for QUALIA.

This module provides detection of GPU/FPGA availability and helper
functions to compute metrics such as Shannon entropy using the
fastest hardware available.
"""

from __future__ import annotations

import time
from typing import Iterable, Dict, Any

import numpy as np

from .logger import get_logger
from .backend import is_gpu_available as _backend_gpu_available
import os

logger = get_logger(__name__)

try:  # pragma: no cover - optional dependency
    import cupy as cp  # type: ignore

    _cupy_available = True
except Exception:  # pragma: no cover - fallback when cupy missing
    cp = None  # type: ignore
    _cupy_available = False

try:  # pragma: no cover - optional dependency
    import pyopencl as cl  # type: ignore

    _fpga_available = bool(cl.get_platforms())
except Exception:  # pragma: no cover - fallback when pyopencl missing
    cl = None  # type: ignore
    _fpga_available = False


def gpu_available() -> bool:
    """Return ``True`` if GPU acceleration via CuPy is available."""
    if not _cupy_available:
        return False
    try:  # pragma: no cover - simple check
        cp.zeros(1)
        return True
    except Exception:
        return False


def fpga_available() -> bool:
    """Return ``True`` if an OpenCL-compatible device is available."""
    return _fpga_available


def qpu_available() -> bool:
    """Return ``True`` if an IBM Quantum backend is reachable."""

    token = os.getenv("IBMQ_TOKEN")
    if not token:
        return False
    try:
        from qiskit_ibm_provider import IBMProvider

        IBMProvider(token=token)
        return True
    except Exception:
        try:  # pragma: no cover - fallback to legacy API
            from qiskit import IBMQ

            if not IBMQ.active_account():
                IBMQ.enable_account(token)
            IBMQ.get_provider()
            return True
        except Exception:
            return False


def shannon_entropy(
    values: Iterable[float] | np.ndarray,
    *,
    use_gpu: bool | None = None,
) -> float:
    """Calculate Shannon entropy using GPU when available.

    Parameters
    ----------
    values
        Iterable of probability values. They do not need to sum to ``1``;
        the function normalizes them automatically.
    use_gpu
        Force GPU computation when ``True``. When ``None`` the function
        uses :func:`gpu_available` to decide.

    Returns
    -------
    float
        Shannon entropy in bits.
    """
    if use_gpu is None:
        use_gpu = gpu_available()

    if use_gpu and cp is not None:
        xp = cp  # type: ignore
    else:
        xp = np

    arr = xp.asarray(list(values), dtype=float)
    total = xp.sum(arr)
    if total <= 0:
        return 0.0

    probs = arr / total
    probs = probs[probs > 1e-12]
    if probs.size == 0:
        return 0.0

    entropy = -xp.sum(probs * xp.log2(probs))
    if use_gpu and cp is not None and hasattr(entropy, "get"):
        entropy = float(entropy.get())  # type: ignore[attr-defined]
    return float(entropy)


def benchmark_entropy(
    values: Iterable[float] | np.ndarray,
    *,
    repeats: int = 3,
) -> Dict[str, float]:
    """Benchmark entropy calculation on CPU and GPU when possible."""
    results: Dict[str, float] = {}
    values_list = list(values)
    start: float

    # CPU benchmark
    times = []
    for _ in range(repeats):
        start = time.perf_counter()
        shannon_entropy(values_list, use_gpu=False)
        times.append(time.perf_counter() - start)
    results["cpu"] = sum(times) / len(times)

    if gpu_available():
        times = []
        for _ in range(repeats):
            start = time.perf_counter()
            shannon_entropy(values_list, use_gpu=True)
            times.append(time.perf_counter() - start)
        results["gpu"] = sum(times) / len(times)

    return results


__all__ = [
    "gpu_available",
    "fpga_available",
    "qpu_available",
    "shannon_entropy",
    "benchmark_entropy",
]
