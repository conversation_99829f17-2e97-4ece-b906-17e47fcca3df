"""Initialize QUALIA logging only once.

This module exposes :func:`initialize_logging`, a thin wrapper around
``init_logging`` that guarantees the configuration is executed at most one time
even when called concurrently.
"""

from __future__ import annotations

from threading import Lock
from typing import Any

from .logging_config import init_logging

_initialized: bool = False
_lock: Lock = Lock()


def initialize_logging(**kwargs: Any) -> bool:
    """Configure logging once and report if configuration occurred.

    Parameters
    ----------
    **kwargs
        Arguments forwarded to :func:`init_logging` on the first invocation.

    Returns
    -------
    bool
        ``True`` if the logging system was configured during this call,
        ``False`` otherwise.
    """
    global _initialized
    with _lock:
        if not _initialized:
            init_logging(**kwargs)
            _initialized = True
            return True
    return False
