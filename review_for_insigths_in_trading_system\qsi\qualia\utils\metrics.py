"""Compatibilidade para acesso às métricas do QUALIA."""

from __future__ import annotations

from typing import Any, Dict, List, Optional, TypedDict

from ..metrics.metrics_model import QUALIAMetrics, QUALIAMetricsHelper
from ..utils.logger import get_logger
from ..monitoring.metrics import get_collector
from ..config.settings import metrics_enabled

logger = get_logger(__name__)


class QUALIAMetricsTyped(TypedDict, total=False):
    """Representação das métricas em forma de dicionário."""

    page_entropy: List[Optional[float]]
    renyi2: List[Optional[float]]
    mi_bh_rad: List[Optional[float]]
    classical_mutual_information: List[Optional[float]]
    loschmidt_echo: List[Optional[float]]
    otoc: List[Optional[float]]
    eigphase_dist: Optional[List[float]]
    quantum_entropy: List[Optional[float]]
    coefficient_evolution: List[Dict[str, float]]
    ctc_purity: List[Optional[float]]
    informational_mass: List[Optional[float]]
    lambda_gravity: List[Optional[float]]
    build_circuit_ms: List[Optional[float]]
    run_ms: List[Optional[float]]
    build_circuit_count: List[int]
    run_count: List[int]


__all__ = [
    "QUALIAMetrics",
    "QUALIAMetricsHelper",
    "QUALIAMetricsTyped",
    "record_metric",
]


def record_metric(name: str, value: float) -> None:
    """Record a metric when metrics logging is enabled.

    Parameters
    ----------
    name : str
        Metric identifier.
    value : float
        Value associated with the metric.
    """

    if not metrics_enabled:
        return

    try:
        collector = get_collector()
        collector.record_metric(name, float(value))
    except Exception as exc:  # pragma: no cover - defensive
        logger.debug("Failed to record metric %s: %s", name, exc)
