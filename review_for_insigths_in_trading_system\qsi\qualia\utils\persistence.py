"""Persistence and serialization utilities for QUALIA.

This module centralizes helper functions related to JSON serialization
and persistence of complex objects used throughout the project. In
addition to generic helpers for converting objects to JSON-friendly
structures, it provides specific helpers for persisting open trading
positions.
"""

from __future__ import annotations

import json
import os
from pathlib import Path
from dataclasses import asdict, fields, is_dataclass
from datetime import date, datetime
from typing import Any, Dict, List, TYPE_CHECKING
import hmac
import hashlib
import base64

from ..config.settings import get_env
from pydantic import BaseModel

from ..common_types import QuantumSignaturePacket
from .logger import get_logger
import numpy as np

if TYPE_CHECKING:  # pragma: no cover - only for type checkers
    from ..core.position import OpenPosition

logger = get_logger(__name__)


def convert_to_serializable(obj: Any) -> Any:
    """Return a JSON-serializable representation of ``obj``.

    Parameters
    ----------
    obj
        Object to convert.

    Returns
    -------
    Any
        Representation that can be safely passed to :func:`json.dumps`.
    """

    if isinstance(obj, dict):
        return {key: convert_to_serializable(value) for key, value in obj.items()}
    if isinstance(obj, (list, tuple)):
        return [convert_to_serializable(item) for item in obj]
    if isinstance(obj, np.ndarray):
        return obj.tolist()
    if isinstance(obj, np.integer):
        return int(obj)
    if isinstance(obj, np.floating):
        return float(obj)
    if isinstance(obj, (datetime, date)):
        return obj.isoformat()
    if isinstance(obj, complex):
        return {"real": obj.real, "imag": obj.imag}
    if isinstance(obj, BaseModel):
        try:
            data = obj.model_dump()
        except Exception:
            try:
                data = obj.dict()
            except Exception:
                return str(obj)
        return {k: convert_to_serializable(v) for k, v in data.items()}
    if is_dataclass(obj):
        return {k: convert_to_serializable(v) for k, v in asdict(obj).items()}
    if hasattr(obj, "to_dict") and callable(getattr(obj, "to_dict")):
        return convert_to_serializable(obj.to_dict())
    if hasattr(obj, "__dict__"):
        return convert_to_serializable(obj.__dict__)
    try:
        json.dumps(obj)
        return obj
    except (TypeError, OverflowError):
        return str(obj)


def _compute_hmac(payload: Dict[str, Any]) -> str:
    """Return HMAC-SHA256 for ``payload`` using ``QUALIA_SECRET_KEY``."""

    key = get_env("QUALIA_SECRET_KEY")
    if not key:
        raise EnvironmentError("QUALIA_SECRET_KEY not set")
    if key == "dev_secret_key":
        raise EnvironmentError("QUALIA_SECRET_KEY must not be 'dev_secret_key'")
    try:
        decoded = base64.urlsafe_b64decode(key)
    except Exception as exc:  # pragma: no cover - invalid key
        raise EnvironmentError("QUALIA_SECRET_KEY must be base64") from exc

    serialized = json.dumps(payload, sort_keys=True, separators=(",", ":")).encode(
        "utf-8"
    )
    return hmac.new(decoded, serialized, hashlib.sha256).hexdigest()


def to_json(obj: Any, indent: int = 2) -> str:
    """Return a formatted JSON string representing ``obj``.

    Parameters
    ----------
    obj
        Object to serialize.
    indent
        Indentation level used when dumping JSON.

    Returns
    -------
    str
        The JSON representation of ``obj``.
    """

    serializable_obj = convert_to_serializable(obj)
    return json.dumps(serializable_obj, indent=indent)


def save_to_json_file(obj: Any, filepath: str, indent: int = 2) -> None:
    """Serialize ``obj`` to ``filepath`` as JSON.

    Parameters
    ----------
    obj
        Object to persist.
    filepath
        Destination path.
    indent
        Indentation level used in the written file.
    """

    serializable_obj = convert_to_serializable(obj)
    path = Path(filepath)
    with open(path, "w", encoding="utf-8") as f:
        json.dump(serializable_obj, f, indent=indent)
    logger.debug("Saved JSON to %s", path.as_posix())


def load_from_json_file(filepath: str) -> Any:
    """Load JSON data from ``filepath``.

    Parameters
    ----------
    filepath
        JSON file path.

    Returns
    -------
    Any
        Parsed data from the JSON file.

    Raises
    ------
    OSError
        When the file cannot be opened.
    json.JSONDecodeError
        When the file contains invalid JSON.
    """

    path = Path(filepath)
    with open(path, "r", encoding="utf-8") as f:
        data = json.load(f)
    logger.debug("Loaded JSON from %s", path.as_posix())
    return data


def load_json_safe(filepath: str, default: Any) -> Any:
    """Load JSON data returning ``default`` when errors occur."""
    if not os.path.exists(filepath):
        return default
    try:
        return load_from_json_file(filepath)
    except (OSError, json.JSONDecodeError, TypeError, ValueError) as exc:
        logger.error("Failed to load %s: %s", filepath, exc)
        return default


def save_json_safe(obj: Any, filepath: str, indent: int = 2) -> None:
    """Persist ``obj`` to ``filepath`` ignoring write errors."""
    os.makedirs(os.path.dirname(filepath), exist_ok=True)
    try:
        save_to_json_file(obj, filepath, indent)
    except OSError as exc:
        logger.error("Failed to save %s: %s", filepath, exc)


class JSONEncoder(json.JSONEncoder):
    """JSON encoder for QUALIA complex objects."""

    def default(self, o: Any) -> Any:  # type: ignore[override]
        if isinstance(o, np.ndarray):
            return o.tolist()
        if isinstance(o, np.integer):
            return int(o)
        if isinstance(o, np.floating):
            return float(o)
        if isinstance(o, (datetime, date)):
            return o.isoformat()
        if isinstance(o, complex):
            return {"real": o.real, "imag": o.imag}
        if isinstance(o, BaseModel):
            try:
                return o.model_dump()
            except AttributeError:  # pragma: no cover - pydantic < 2
                return o.dict()
        if hasattr(o, "to_dict") and callable(getattr(o, "to_dict")):
            return o.to_dict()
        return super().default(o)


def save_positions_json(
    positions: Dict[str, List["OpenPosition"]], filepath: str
) -> None:
    """Persist open positions to a JSON file.

    Parameters
    ----------
    positions
        Mapping of symbols to open positions.
    filepath
        Target file path.
    """

    path = Path(filepath)
    os.makedirs(path.parent, exist_ok=True)
    positions_data = {
        symbol: [convert_to_serializable(asdict(pos)) for pos in pos_list]
        for symbol, pos_list in positions.items()
    }
    hmac_value = _compute_hmac({"positions": positions_data})
    with open(path, "w", encoding="utf-8") as f:
        json.dump({"positions": positions_data, "hmac": hmac_value}, f, indent=2)
    logger.info(
        "Saved %s open positions to %s",
        sum(len(p) for p in positions.values()),
        path.as_posix(),
    )


def _parse_timestamp(value: str) -> datetime:
    """Return ``datetime`` parsed from string or UNIX timestamp."""

    try:
        return datetime.fromisoformat(value)
    except ValueError:
        return datetime.fromtimestamp(float(value))


def load_positions_json(filepath: str) -> Dict[str, List["OpenPosition"]]:
    """Load open positions from a JSON file.

    Parameters
    ----------
    filepath
        File generated by ``save_positions_json``.

    Returns
    -------
    Dict[str, List[OpenPosition]]
        Mapping of symbols to reconstructed :class:`OpenPosition` instances.
    """
    from ..core.position import OpenPosition

    path = Path(filepath)
    with open(path, "r", encoding="utf-8") as f:
        payload = json.load(f)
    logger.info("Loaded open positions from %s", path.as_posix())

    if "positions" in payload:
        expected = _compute_hmac({"positions": payload.get("positions", {})})
        if payload.get("hmac") != expected:
            logger.error("HMAC inválido em %s", path.as_posix())
            return {}
        raw_data = payload.get("positions", {})
    else:
        raw_data = payload

    field_names = {f.name for f in fields(OpenPosition)}
    positions: Dict[str, List[OpenPosition]] = {}
    for symbol, pos_list in raw_data.items():
        loaded_list: List[OpenPosition] = []
        for data in pos_list:
            if "timestamp" in data:
                data["timestamp"] = _parse_timestamp(data["timestamp"])
            if (
                "captured_quantum_signature_packet" in data
                and data["captured_quantum_signature_packet"]
            ):
                try:
                    data["captured_quantum_signature_packet"] = QuantumSignaturePacket(
                        **data["captured_quantum_signature_packet"]
                    )
                except Exception as exc:
                    logger.warning(
                        (
                            "Falha ao desserializar 'captured_quantum_signature_packet'"
                            " para o símbolo %s com order_id %s: %s"
                        ),
                        symbol,
                        data.get("order_id", "desconhecido"),
                        exc,
                        exc_info=True,
                    )
                    data["captured_quantum_signature_packet"] = None
            filtered = {k: v for k, v in data.items() if k in field_names}
            loaded_list.append(OpenPosition(**filtered))
        positions[symbol] = loaded_list
    return positions
