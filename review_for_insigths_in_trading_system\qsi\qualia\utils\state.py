"""State container for the QUALIA application.

This module defines :class:`QualiaState`, a lightweight structure used
in ``app.py`` to store components such as the consciousness and trading
universe. The class implements a context manager backed by a
:class:`threading.RLock` to support safe concurrent access.
"""

from __future__ import annotations

from dataclasses import dataclass, field
from datetime import datetime, timezone
from threading import RLock
from contextlib import contextmanager
from typing import Any, Dict, List, Optional, Iterator

from .logger import get_logger

logger = get_logger(__name__)


@dataclass
class QualiaState:
    """Container for global mutable state.

    Attributes
    ----------
    consciousness:
        Instance of ``QUALIAConsciousness``.
    symbolic_processor:
        Instance of ``QualiaSymbolicProcessor``.
    universe:
        Instance of ``QUALIAQuantumUniverse``.
    metrics:
        Dictionary of calculated metrics.
    qast_cycles:
        History of executed QAST cycles.
    perception_data:
        Environment perception cache.
    last_update:
        Timestamp of the last state mutation in ISO format.
    use_webgpu:
        Enable WebGPU rendering if supported.
    research_focus:
        Controls forgetting rate in ``HolographicMemory`` queries.
    """

    consciousness: Optional[Any] = None
    symbolic_processor: Optional[Any] = None
    universe: Optional[Any] = None
    metrics: Dict[str, Any] = field(default_factory=dict)
    qast_cycles: List[Any] = field(default_factory=list)
    perception_data: Dict[str, Any] = field(default_factory=dict)
    last_update: Optional[str] = None
    use_webgpu: bool = False
    research_focus: float = 0.0
    _lock: RLock = field(default_factory=RLock, init=False, repr=False)

    def update_timestamp(self) -> None:
        """Update ``last_update`` to the current time."""
        self.last_update = datetime.now(timezone.utc).isoformat()
        logger.debug("QualiaState timestamp updated: %s", self.last_update)

    @contextmanager
    def locked(self) -> Iterator["QualiaState"]:
        """Yield a locked version of this state for safe mutation."""
        with self._lock:
            yield self

    def clear(self) -> None:
        """Reset all stored components and cached data."""
        with self.locked():
            self.consciousness = None
            self.symbolic_processor = None
            self.universe = None
            self.metrics.clear()
            self.qast_cycles.clear()
            self.perception_data.clear()
            self.last_update = None
            self.research_focus = 0.0
        logger.info("QualiaState cleared")

    def to_dict(self) -> Dict[str, Any]:
        """Return a snapshot of the current state as a dictionary."""
        with self.locked():
            return {
                "consciousness": self.consciousness,
                "symbolic_processor": self.symbolic_processor,
                "universe": self.universe,
                "metrics": dict(self.metrics),
                "qast_cycles": list(self.qast_cycles),
                "perception_data": dict(self.perception_data),
                "last_update": self.last_update,
                "use_webgpu": self.use_webgpu,
                "research_focus": self.research_focus,
            }
