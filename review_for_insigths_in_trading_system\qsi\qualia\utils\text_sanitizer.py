from __future__ import annotations

"""Utility for sanitizing text before logging."""

import re

# Patterns that may contain confidential information
_CONFIDENTIAL_PATTERNS = [
    re.compile(r"api_key\s*=\s*\S+", re.IGNORECASE),
    re.compile(r"api_secret\s*=\s*\S+", re.IGNORECASE),
    re.compile(r"token\s*[=:]\s*\S+", re.IGNORECASE),
    re.compile(r"password\s*[=:]\s*\S+", re.IGNORECASE),
    re.compile(r"prompt:.*", re.IGNORECASE | re.DOTALL),
]


def sanitize_text(text: str) -> str:
    """Return *text* with sensitive patterns replaced by ``[REDACTED]``."""
    sanitized = text
    for pattern in _CONFIDENTIAL_PATTERNS:
        sanitized = pattern.sub("[REDACTED]", sanitized)
    return sanitized
