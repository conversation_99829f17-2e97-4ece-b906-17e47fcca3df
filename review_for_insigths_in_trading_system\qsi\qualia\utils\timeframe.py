"""Utility functions for timeframe conversions."""

from __future__ import annotations

import re
from functools import lru_cache
from typing import Any

from .logger import get_logger

import pandas as pd

__all__ = [
    "timeframe_to_minutes",
    "timeframe_to_pandas_freq",
    "timeframe_to_milliseconds",
    "safe_timeframe_to_minutes",
]


def _parse_timeframe(timeframe: str) -> tuple[int, str]:
    """Parse timeframe string returning numeric value and unit."""
    if not isinstance(timeframe, str) or not timeframe:
        raise ValueError("timeframe deve ser uma string não vazia")

    match = re.match(r"^(\d+)\s*([a-zA-Z]+)$", timeframe.strip())
    if not match:
        raise ValueError(f"Formato de timeframe inválido: {timeframe}")

    value = int(match.group(1))
    unit = match.group(2)
    return value, unit


@lru_cache(maxsize=None)
def timeframe_to_minutes(timeframe: str) -> int:
    """Return the length of ``timeframe`` in minutes.

    Parameters
    ----------
    timeframe : str
        Interval no formato ``"15m"`` ou ``"2h"``. As unidades suportadas
        são ``m``/``min`` (minutos), ``h`` (horas), ``d`` (dias), ``w``
        (semanas), ``M`` (30 dias) e ``Y`` (365 dias).

    Returns
    -------
    int
        Número de minutos representado por ``timeframe``.

    Examples
    --------
    >>> timeframe_to_minutes("15m")
    15
    >>> timeframe_to_minutes("1h30m")
    90
    >>> timeframe_to_minutes("1M")
    43200
    >>> timeframe_to_minutes("1Y")
    525600
    """
    try:
        value, unit = _parse_timeframe(timeframe)
    except ValueError:
        # Fall back to pandas for extended formats
        try:
            return int(pd.to_timedelta(timeframe).total_seconds() // 60)
        except (ValueError, TypeError, AttributeError) as exc:
            raise ValueError(f"Formato de timeframe inválido: {timeframe}") from exc

    if unit == "M":
        return value * 30 * 1440

    unit_lower = unit.lower()
    if unit == "Y" or unit_lower == "y":
        return value * 365 * 1440

    multipliers = {
        "m": 1,
        "min": 1,
        "h": 60,
        "d": 1440,
        "w": 10080,
    }

    for key, mult in multipliers.items():
        if unit_lower.startswith(key):
            return value * mult
    raise ValueError(f"Unidade de timeframe desconhecida: {timeframe}")


@lru_cache(maxsize=None)
def timeframe_to_pandas_freq(timeframe: str) -> str:
    """Return a pandas-compatible frequency string for ``timeframe``.

    Parameters
    ----------
    timeframe : str
        Intervalo nos mesmos moldes de ``timeframe_to_minutes``. As unidades
        ``M`` e ``Y`` são equivalentes a ``30D`` e ``365D`` em pandas.

    Returns
    -------
    str
        Frequência compreensível pelo ``pandas``.

    Examples
    --------
    >>> timeframe_to_pandas_freq("1h")
    '1h'
    >>> timeframe_to_pandas_freq("1M")
    '30D'
    >>> timeframe_to_pandas_freq("2Y")
    '730D'
    """
    value, unit = _parse_timeframe(timeframe)

    if unit == "M":
        return f"{value * 30}D"

    unit_lower = unit.lower()
    if unit == "Y" or unit_lower == "y":
        return f"{value * 365}D"
    mapping = {
        "m": "min",
        "min": "min",
        "h": "h",
        "d": "D",
        "w": "W",
    }

    for key, suffix in mapping.items():
        if unit_lower.startswith(key):
            return f"{value}{suffix}"
    raise ValueError(f"Formato de timeframe não suportado: {timeframe}")


@lru_cache(maxsize=None)
def timeframe_to_milliseconds(timeframe: str) -> int:
    """Return timeframe length in milliseconds."""

    return timeframe_to_minutes(timeframe) * 60 * 1000


def safe_timeframe_to_minutes(timeframe: str, default: int = 1) -> int:
    """Return timeframe length in minutes or ``default`` when invalid.

    Parameters
    ----------
    timeframe : str
        Timeframe no formato ``"1m"``, ``"1h"`` etc.
    default : int, optional
        Valor a retornar quando ``timeframe`` for inválido, ``1`` por padrão.

    Returns
    -------
    int
        Número de minutos correspondente ou ``default`` caso haja erro.
    """

    try:
        return timeframe_to_minutes(timeframe)
    except ValueError:
        logger = get_logger(__name__)
        logger.error("Timeframe inválido fornecido: %s", repr(timeframe))
        return default
