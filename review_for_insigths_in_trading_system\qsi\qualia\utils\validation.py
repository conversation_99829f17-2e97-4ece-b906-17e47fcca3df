"""Funções auxiliares de validação numérica."""

from __future__ import annotations

import math
from typing import Any


def validate_positive_float(value: Any, name: str) -> float:
    """Converte *value* para ``float`` garantindo que seja estritamente positivo.

    Parameters
    ----------
    value
        Valor a ser validado.
    name
        Nome do parâmetro para mensagens de erro.

    Returns
    -------
    float
        O valor validado.

    Raises
    ------
    ValueError
        Se o valor não for conversível para ``float`` ou não for positivo.
    """

    try:
        num = float(value)
    except (TypeError, ValueError) as exc:  # pragma: no cover - validado em testes
        raise ValueError(f"{name} deve ser um float positivo") from exc
    if num <= 0 or not math.isfinite(num):
        raise ValueError(f"{name} deve ser um float positivo")
    return num


def validate_non_negative_int(value: Any, name: str) -> int:
    """Converte *value* para ``int`` garantindo que seja não negativo.

    Parameters
    ----------
    value
        Valor a ser validado.
    name
        Nome do parâmetro para mensagens de erro.

    Returns
    -------
    int
        O valor validado.

    Raises
    ------
    ValueError
        Se o valor não for conversível para ``int`` ou for negativo.
    """

    try:
        num = int(value)
    except (TypeError, ValueError) as exc:  # pragma: no cover - validado em testes
        raise ValueError(f"{name} deve ser um inteiro não negativo") from exc
    if num < 0:
        raise ValueError(f"{name} deve ser um inteiro não negativo")
    return num
