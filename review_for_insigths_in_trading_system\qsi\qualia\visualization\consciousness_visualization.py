# coding: utf-8
"""
QUALIA: Quantum Universal Awareness Lattice Interface Architecture
Interface Quantum-Consciente
"""

from dataclasses import dataclass, field
from flask import Blueprint, Flask, jsonify, Response, render_template
import numpy as np
import threading  # Para rodar a visualização em background
import time
from typing import Any, Optional, Dict, Tuple, Union
from werkzeug.serving import BaseWSGIServer, make_server

from ..utils.logger import get_logger
from ..utils.logging_initializer import initialize_logging

# Importações do QUALIA
from ..core.universe import QUALIAQuantumUniverse
from ..core.consciousness import QUALIAConsciousness

# Logger configurado para este módulo
logger = get_logger(__name__)

# Importar módulo de trading (se existir separadamente, senão integrar aqui)
# from ..ui.trading_interface import trading_bp # Já está em app.py


@dataclass
class VisualizationState:
    """Estado compartilhado pelo servidor de visualização."""

    active: bool = False
    data_source: Any | None = None
    last_data: Dict[str, Any] = field(default_factory=dict)
    error: str | None = None


# Blueprint para a visualização
visualization_bp = Blueprint("visualization", __name__, url_prefix="/visualization")


# Instância atual utilizada pelos endpoints
visualization_instance: "QUALIAConsciousnessVisualization | None" = None


def run_visualization_server(
    app_flask: Flask, host: str = "0.0.0.0", port: int = 5001
) -> None:
    """Inicia um servidor Flask utilizando ``werkzeug.make_server``."""

    logger.info(
        "QUALIAConsciousnessVisualization: Iniciando servidor Flask em %s:%s",
        host,
        port,
    )
    try:
        server = make_server(host, port, app_flask)
        if visualization_instance is not None:
            visualization_instance._server = server
        server.serve_forever()
    except Exception as e:
        if visualization_instance is not None:
            visualization_instance.state.error = str(e)
        logger.error("Erro ao iniciar servidor de visualização: %s", e)
        raise


@visualization_bp.route("/")
def visualization_page() -> str:
    """Renderiza a página do dashboard de visualização."""
    return render_template("visualization/index.html")


@visualization_bp.route("/api/dashboard_data")
def get_dashboard_data() -> Union[Response, Tuple[Response, int]]:
    """Endpoint para fornecer dados para o dashboard de visualização."""
    if visualization_instance is None:
        return jsonify({"error": "Visualização não inicializada"}), 503

    state = visualization_instance.state

    if not state.active or state.data_source is None:
        return (
            jsonify(
                {"error": "Visualização não ativa ou fonte de dados não configurada"}
            ),
            503,
        )

    data_to_send: Dict[str, Any] = {}
    source = state.data_source

    try:
        if isinstance(source, QUALIAQuantumUniverse) and hasattr(source, "get_metrics"):
            # Coletar métricas do universo
            uni_metrics = source.get_metrics()  # type: ignore[attr-defined]
            data_to_send["universe_metrics"] = (
                uni_metrics.get_metrics_dict()
                if hasattr(uni_metrics, "get_metrics_dict")
                else uni_metrics
            )
            data_to_send["universe_n_qubits"] = source.n_qubits
            if source.last_statevector:
                # Para serialização, enviar probabilidades e fases (limitado
                # para performance)
                sv_data = source.last_statevector.data
                probabilities = np.abs(sv_data) ** 2
                phases = np.angle(sv_data)
                # Limitar a um número gerenciável de estados para o frontend
                limit_states = min(len(probabilities), 16)
                data_to_send["statevector_probabilities"] = probabilities[
                    :limit_states
                ].tolist()
                data_to_send["statevector_phases"] = phases[:limit_states].tolist()
                basis_states = [
                    format(i, f"0{source.n_qubits}b") for i in range(limit_states)
                ]
                data_to_send["statevector_basis_states"] = basis_states
            else:
                data_to_send["statevector_probabilities"] = []
                data_to_send["statevector_phases"] = []
                data_to_send["statevector_basis_states"] = []

        elif isinstance(source, QUALIAConsciousness):
            # Coletar métricas da consciência
            qast_history = source.get_qast_history()
            metrics_dict = source.get_metrics_dict()
            data_to_send["consciousness_metrics"] = metrics_dict
            data_to_send["qast_cycles_count"] = len(qast_history)
            if qast_history:
                data_to_send["last_qast_cycle"] = qast_history[-1]

        # Adicionar outras métricas agregadas conforme necessário
        # Ex: entropia, coerência já devem estar em universe_metrics

        state.last_data = data_to_send
        return jsonify(data_to_send)

    except Exception as e:
        logger.error("Erro ao coletar dados para visualização: %s", e)
        return jsonify({"error": f"Erro ao coletar dados: {str(e)}"}), 500


class QUALIAConsciousnessVisualization:
    """
    Componente para rodar um servidor Flask em uma thread para servir um dashboard de visualização.
    """

    def __init__(
        self, data_source: Any, host: str = "0.0.0.0", port: int = 5001
    ) -> None:
        """
        Args:
            data_source: A instância do QUALIAQuantumUniverse ou QUALIAConsciousness para monitorar.
            host: Host para o servidor Flask.
            port: Porta para o servidor Flask.
        """
        self.flask_app = Flask(f"qualia_visualization_server_{port}")
        self.flask_app.register_blueprint(visualization_bp)  # Registrar o blueprint
        self.data_source = data_source
        self.host = host
        self.port = port
        self.thread: Optional[threading.Thread] = None
        self._server: Optional[BaseWSGIServer] = None

        self.state = VisualizationState(data_source=self.data_source)
        global visualization_instance
        visualization_instance = self
        logger.debug(
            "QUALIAConsciousnessVisualization configurado para fonte: %s",
            type(data_source),
        )

    def start(self) -> None:
        """Inicia o servidor de visualização em uma nova thread."""
        if self.thread is not None and self.thread.is_alive():
            logger.info("Servidor de visualização já está rodando.")
            return

        self.state.active = True
        self._server = None
        self.thread = threading.Thread(
            target=run_visualization_server,
            args=(self.flask_app, self.host, self.port),
            daemon=True,  # Permite que o programa principal saia mesmo se a thread estiver rodando
        )
        self.thread.start()
        logger.info(
            "QUALIAConsciousnessVisualization: Servidor iniciado na thread %s. Acesse em http://%s:%s/visualization/",
            self.thread.name,
            self.host,
            self.port,
        )

    def stop(self) -> None:
        """Encerra o servidor de visualização de forma controlada."""

        self.state.active = False

        if self._server is None and self.thread is not None:
            for _ in range(5):  # aguardar a inicialização do servidor
                if self._server is not None:
                    break
                time.sleep(0.1)

        if self._server is not None:
            try:
                self._server.shutdown()
            except Exception as exc:  # pragma: no cover - erro inesperado
                logger.error(
                    "QUALIAConsciousnessVisualization: erro ao solicitar shutdown: %s",
                    exc,
                )

        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=5)
            if self.thread.is_alive():
                logger.warning(
                    "QUALIAConsciousnessVisualization: thread de servidor nao finalizou dentro do timeout"
                )
            else:
                logger.info("QUALIAConsciousnessVisualization: servidor finalizado")

        self.thread = None
        self._server = None

    def get_dashboard_url(self) -> str:
        return f"http://localhost:{self.port}/visualization/"


# Exemplo de como integrar no app.py ou script principal:
# (Este código seria parte do seu app.py ou de um orquestrador)

# Supondo que `app` é a instância principal do Flask da sua aplicação QUALIA
# e `QUALIA_STATE` é o dicionário global definido em app.py

# No seu `initialize_qualia_system()` em app.py, você poderia adicionar:
# ... (inicialização de outros componentes)
# QUALIA_STATE["visualization"] = QUALIAConsciousnessVisualization(data_source=QUALIA_STATE["universe"]) # ou QUALIA_STATE["consciousness"]
# QUALIA_STATE["visualization"].start()
# print(f"Dashboard de visualização disponível em:
# {QUALIA_STATE["visualization"].get_dashboard_url()}")

# E no final do seu script, ou em uma rotina de cleanup:
# if QUALIA_STATE.get("visualization"):
#     QUALIA_STATE["visualization"].stop()


# Para que este arquivo seja auto-executável para teste (opcional):
if __name__ == "__main__":
    initialize_logging(log_level="INFO")
    logger.info("Iniciando QUALIAConsciousnessVisualization em modo de teste...")

    # Mock do QUALIAUniverse para teste
    class MockDataSource:
        def __init__(self) -> None:
            self.n_qubits = 3
            self.last_statevector = type(
                "obj",
                (object,),
                {  # Mock Statevector
                    "data": np.array(
                        [1 / np.sqrt(2), 1 / np.sqrt(2)] + [0] * (2**3 - 2)
                    )  # Exemplo
                },
            )()
            self.other_metric = "example"

        def get_metrics(self) -> Dict[str, Any]:
            """Retorna métricas simuladas para o universo quântico."""
            return {"mock_metric": self.other_metric}

    mock_source = MockDataSource()
    viz = QUALIAConsciousnessVisualization(data_source=mock_source)
    viz.start()
    logger.info("Servidor de teste iniciado em %s", viz.get_dashboard_url())
    try:
        input("Pressione Enter para encerrar o servidor de teste...")
    finally:
        viz.stop()
