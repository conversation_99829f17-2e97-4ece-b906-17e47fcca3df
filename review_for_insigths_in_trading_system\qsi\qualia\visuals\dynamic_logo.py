# -*- coding: utf-8 -*-
"""Dynamic Logo Engine (versão simplificada)

Implementação mínima inspirada na *Documentação Completa – Projeto Dynamic Logo*.

Funcionalidades principais:
    • fusion_mode: seleciona mapeamentos multimodais → cor/forma.
    • temporal_blur: suaviza transições usando média móvel exponencial.
    • update_audio_color(hue, brightness): para sinestesia música→cor.
    • update_text_embedding(norm_val): texto→forma (fractalidade simples).
    • render(): devolve `np.ndarray` (H×W×3 BGR) pronto para OpenCV.

NOTA: Esta é uma implementação didática — não cobre todos os efeitos gráficos
avançados mencionados no PDF, mas fornece interface compatível para demos e QA.
"""
from __future__ import annotations

import math
import os
from collections import deque
from typing import Deque, Tuple

import numpy as np

try:
    import cv2  # type: ignore
except ImportError:  # pragma: no cover
    cv2 = None  # render() pode salvar como PNG via Pillow se opencv ausente

# -------------------------------- Utilidades --------------------------------- #


def hsv_to_bgr(h: float, s: float, v: float) -> Tuple[int, int, int]:
    """Converte HSV (0-360, 0-1, 0-1) em BGR 0-255.

    • Com OpenCV: usa cv2.COLOR_HSV2BGR (HSV 0-179 / 0-255 / 0-255).
    • Sem OpenCV: colorsys.hsv_to_rgb (0-1 escala) — garante fidelidade de cor
      em ambientes headless/sem cv2.
    """

    if cv2 is not None:
        h_cv = (h % 360) / 2  # OpenCV aceita 0-179
        s_cv = max(0.0, min(1.0, s)) * 255
        v_cv = max(0.0, min(1.0, v)) * 255
        bgr = cv2.cvtColor(np.uint8([[[h_cv, s_cv, v_cv]]]), cv2.COLOR_HSV2BGR)[0, 0]
        return int(bgr[0]), int(bgr[1]), int(bgr[2])

    # Fallback stdlib
    import colorsys

    r, g, b = colorsys.hsv_to_rgb((h % 360) / 360.0, s, v)
    return int(b * 255), int(g * 255), int(r * 255)


# ---------------------------- DynamicLogoEngine ----------------------------- #


class DynamicLogoEngine:
    def __init__(
        self,
        fusion_mode: str = "audio→color",
        temporal_blur: int = 8,
        canvas_size: int = 256,
        deform_amplitude: float = 0.25,
    ) -> None:
        """Initialize the engine with the desired multimodal mappings.

        Parameters
        ----------
        fusion_mode
            Mapping configuration string using ``source→target`` notation.
        temporal_blur
            Window size for the exponential moving average.
        canvas_size
            Side length of the square canvas in pixels.
        deform_amplitude
            Maximum amplitude for the shape deformation.
        """

        self.fusion_mode = fusion_mode
        self._active_fusions = self._parse_fusion_mode(fusion_mode)
        self.temporal_blur = max(1, temporal_blur)
        self._ema_alpha = 2.0 / (self.temporal_blur + 1)
        self.canvas_size = canvas_size
        self.deform_amplitude = deform_amplitude

        self._yy, self._xx = np.ogrid[:canvas_size, :canvas_size]

        # estado de cor (HSV) e brilho
        self._hue: float = 0.0
        self._brightness: float = 0.0
        self._hue_history: Deque[float] = deque(maxlen=self.temporal_blur)
        self._brightness_history: Deque[float] = deque(maxlen=self.temporal_blur)

        # estado de forma – irregularidade [0,1]
        self._irregularity: float = 0.0
        self._irr_history: Deque[float] = deque(maxlen=self.temporal_blur)
        # cache para máscara
        self._mask_cache: np.ndarray | None = None
        self._irr_last: float = -1.0

    @staticmethod
    def _parse_fusion_mode(mode: str) -> set[tuple[str, str]]:
        """Interpret ``fusion_mode`` string as a set of mapping pairs.

        Parameters
        ----------
        mode
            String describing how metrics influence the logo. Entries are
            separated by commas and use ``source→target`` notation.

        Returns
        -------
        set of tuple[str, str]
            Normalized pairs such as ``("audio", "color")``.
        """

        normalized = mode.replace("->", "→")
        mappings = set()
        for item in normalized.split(","):
            parts = [p.strip() for p in item.split("→")]
            if len(parts) == 2 and all(parts):
                mappings.add((parts[0].lower(), parts[1].lower()))
        return mappings

    # ---------------------- Atualizações multimodais ----------------------- #

    def update_audio_color(self, *, hue: float, brightness: float) -> None:
        """Mapeia componente de áudio para cor.

        hue:   0-360 graus (matiz)
        brightness: 0-1 (RMS ou coerência)
        """
        if ("audio", "color") not in self._active_fusions:
            return
        self._hue_history.append(hue)
        self._brightness_history.append(brightness)
        self._hue = (hue * self._ema_alpha) + (self._hue * (1 - self._ema_alpha))
        self._brightness = (brightness * self._ema_alpha) + (
            self._brightness * (1 - self._ema_alpha)
        )

    def update_text_embedding(self, norm_val: float) -> None:
        """Mapeia norma de embedding textual para irregularidade da forma."""
        if ("text", "shape") not in self._active_fusions:
            return
        self._irr_history.append(norm_val)
        self._irregularity = (norm_val * self._ema_alpha) + (
            self._irregularity * (1 - self._ema_alpha)
        )

    # ----------------------------- Renderização ---------------------------- #

    def _generate_shape_mask(self) -> np.ndarray:
        """Gera máscara binária da forma (círculo deformado).

        A amplitude da deformação é controlada por ``deform_amplitude``.
        """
        size = self.canvas_size
        center = size // 2
        yy, xx = self._yy, self._xx
        radius_base = center * 0.8

        # irregularidade → perturba o raio com harmônicos sin/cos
        irr = self._irregularity
        if self._mask_cache is not None and abs(irr - self._irr_last) < 1e-4:
            return self._mask_cache

        angle = np.arctan2(yy - center, xx - center)
        perturb = 1 + self.deform_amplitude * irr * (
            np.sin(5 * angle) + 0.5 * np.sin(11 * angle + math.pi / 4)
        )
        radius = radius_base * perturb
        mask = ((xx - center) ** 2 + (yy - center) ** 2 <= radius**2).astype(np.uint8)
        # cache
        self._mask_cache = mask
        self._irr_last = irr
        return mask

    def render(self) -> np.ndarray:
        """Retorna frame BGR (uint8)."""
        size = self.canvas_size
        frame = np.zeros((size, size, 3), dtype=np.uint8)

        # cor principal
        b, g, r = hsv_to_bgr(self._hue, 1.0, self._brightness)

        mask = self._generate_shape_mask()
        frame[mask.astype(bool)] = (b, g, r)
        return frame

    # --------------------------- Utilidades I/O ---------------------------- #

    def show(self, window_name: str = "DynamicLogo") -> None:  # pragma: no cover
        """Exibe o frame atual em uma janela do OpenCV, se disponível."""

        if os.getenv("QUALIA_NO_DISPLAY"):
            return

        if cv2 is None:
            raise RuntimeError("OpenCV nao instalado – impossivel mostrar janela.")
        cv2.imshow(window_name, self.render())
        cv2.waitKey(1)

    def save_png(self, path: str | bytes | bytearray) -> None:
        img = self.render()
        if cv2 is not None:
            cv2.imwrite(str(path), img)
        else:
            from PIL import Image

            # converte BGR → RGB manualmente (cv2 inexistente)
            Image.fromarray(img[:, :, ::-1]).save(path)

    # ----------------------------- Reset ----------------------------------- #

    def reset(self) -> None:
        self._hue_history.clear()
        self._brightness_history.clear()
        self._irr_history.clear()
        self._hue = 0.0
        self._brightness = 0.0
        self._irregularity = 0.0
