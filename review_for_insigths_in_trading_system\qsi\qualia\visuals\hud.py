"""HUD para visualização do estado do QUALIA."""

from __future__ import annotations

from datetime import datetime, timezone
from typing import Any, Dict

from .dynamic_logo import DynamicLogoEngine
from ..utils.metric_sanitizer import sanitize_metrics


class QualiaHUD:
    """Heads-Up Display responsável por refletir o estado atual do sistema."""

    def __init__(self, logo_engine: DynamicLogoEngine | None = None) -> None:
        self.logo = logo_engine or DynamicLogoEngine()

    def on_tick(self, metrics: Dict[str, float]) -> Dict[str, Any]:
        """Atualiza o HUD e retorna o estado atual.

        Parameters
        ----------
        metrics
            Dicionário contendo ao menos ``entropy`` e ``coherence``.

        Returns
        -------
        Dict[str, Any]
            Estado serializável com timestamp e informações da logo.
        """
        metrics = sanitize_metrics(metrics)

        entropy = float(metrics.get("entropy", 0.0))
        coherence = float(metrics.get("coherence", 0.0))

        hue = entropy * 360.0
        brightness = max(0.0, min(1.0, coherence))

        embedding_norm = float(metrics.get("embedding_norm", 0.0))
        liquidity_buckets = metrics.get("liquidity_buckets")
        trend_strength = metrics.get("trend_strength")
        delta_entropy = metrics.get("delta_entropy")

        self.logo.update_audio_color(hue=hue, brightness=brightness)
        self.logo.update_text_embedding(embedding_norm)

        state: Dict[str, Any] = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "entropy": entropy,
            "coherence": coherence,
            "hue": hue,
            "brightness": brightness,
            "embedding_norm": embedding_norm,
        }
        if liquidity_buckets is not None:
            state["liquidity_buckets"] = liquidity_buckets
        if trend_strength is not None:
            state["trend_strength"] = float(trend_strength)
        if delta_entropy is not None:
            state["delta_entropy"] = float(delta_entropy)

        return state
