import numpy as np
from qsi.geometry import build_phi_unitary
from qsi.validation import validate_quantum_state
from qsi.utils import is_unitary
from qualia.utils.logger import get_logger

logger = get_logger(__name__)

class GeometricQSIResonator:
    """
    Um ressonador quântico que combina a análise de QSI com uma
    dinâmica geométrica fundamental, garantida por transformações unitárias.
    """
    def __init__(
        self,
        dimensions: int,
        *,
        phi1: float = 1.618,
        phi2: float = 2.618,
        U1: np.ndarray | None = None,
        U2: np.ndarray | None = None,
    ) -> None:
        """
        Inicializa o ressonador, pré-calculando as transformações unitárias.

        Args:
            dimensions: A dimensionalidade do espaço de Hilbert no qual o
                        ressonador irá operar.
        """
        if not isinstance(dimensions, int) or dimensions <= 1:
            raise ValueError("A dimensão deve ser um inteiro maior que 1.")
        
        self.dimensions = dimensions
        logger.info(
            "GeometricQSIResonator inicializado com dimensão %s",
            self.dimensions,
        )

        logger.info("    ...construindo mapas unitários U1 e U2...")
        self.U1 = (
            build_phi_unitary(self.dimensions, phi=phi1)
            if U1 is None
            else np.asarray(U1, dtype=np.complex128)
        )
        self.U2 = (
            build_phi_unitary(self.dimensions, phi=phi2)
            if U2 is None
            else np.asarray(U2, dtype=np.complex128)
        )

        for idx, U in enumerate([self.U1, self.U2], start=1):
            if U.shape != (self.dimensions, self.dimensions):
                raise ValueError(f"U{idx} deve ser {self.dimensions}x{self.dimensions}")
            if not is_unitary(U):
                raise ValueError(f"U{idx} deve ser unitária")

        logger.info("    Mapas unitários construídos e validados.")

    @validate_quantum_state
    def apply_resonance(self, state: np.ndarray) -> np.ndarray:
        """
        Aplica a ressonância combinada QSI-Geometria a um estado quântico.

        O processo segue o paradigma de um circuito quântico, aplicando uma
        sequência de transformações unitárias ao estado de entrada:
        
        |ψ'⟩ = U₂ * U₁ * |ψ⟩

        Isso garante que a norma do estado (probabilidade total) seja
        conservada ao longo do processo de ressonância.

        Args:
            state: O vetor de estado quântico de entrada, normalizado.

        Returns:
            O vetor de estado quântico transformado, ainda normalizado.
        """
        logger.info("    ...aplicando ressonância unitária ao estado...")
        
        # Aplica a primeira transformação unitária
        intermediate_state = self.U1 @ state.T
        
        # Aplica a segunda transformação unitária
        final_state = self.U2 @ intermediate_state
        
        # O decorador @validate_quantum_state já valida a entrada.
        # A matemática garante que a saída também seja normalizada.
        return final_state.T

# Exemplo de uso (pode ser movido para main.py ou testes depois)
if __name__ == '__main__':
    dims = 4
    resonator = GeometricQSIResonator(dimensions=dims)
    
    # Cria um estado quântico aleatório e o normaliza
    random_state = np.random.rand(dims) + 1j * np.random.rand(dims)
    random_state /= np.linalg.norm(random_state)
    
    logger.info(
        "\nEstado inicial (norma=%.2f):\n%s",
        np.linalg.norm(random_state),
        random_state,
    )
    
    # Aplica a ressonância
    final_state = resonator.apply_resonance(random_state)
    
    logger.info(
        "\nEstado final (norma=%.2f):\n%s",
        np.linalg.norm(final_state),
        final_state,
    )
    
    # Verifica se a norma foi conservada
    assert np.isclose(np.linalg.norm(final_state), 1.0)
    logger.info("\n✅ Verificação de conservação de norma bem-sucedida.")
