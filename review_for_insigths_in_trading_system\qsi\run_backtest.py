"""
Backtest Runner for the QUALIA System

This script orchestrates a simplified, end-to-end backtest of the QUALIA
system, focusing on the information flow from market data to trading signals.
"""
import sys
import os
from pathlib import Path

# Garantir que o diretório do repositório esteja no ``sys.path``
REPO_ROOT = Path(__file__).resolve().parent
if str(REPO_ROOT) not in sys.path:
    sys.path.insert(0, str(REPO_ROOT))

# Definir CONFIG do QUALIA para utilizar o YAML local
os.environ.setdefault(
    "QUALIA_STRATEGY_CONFIG",
    str(REPO_ROOT / "run_backtest_config.yaml"),
)

# Configurar o caminho para os cenários de warm-start
os.environ.setdefault(
    "QPM_WARMSTART_CONFIG",
    str(REPO_ROOT / "config" / "warmstart.yaml"),
)

import asyncio
import logging
try:
    import pandas as pd
    import yfinance as yf
except ModuleNotFoundError as exc:  # pragma: no cover - simple dependency check
    missing_pkg = exc.name
    raise SystemExit(
        f"Dependência obrigatória '{missing_pkg}' ausente. "
        "Execute 'pip install -r requirements.txt' antes de rodar o backtest."
    ) from exc

# Configure basic logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Import core QUALIA components
# Note: These imports assume the script is run from a location where
# the 'qualia' package is accessible.
from qualia.core.qast_core import TradingQASTCore
from qualia.consciousness.holographic_universe import HolographicMarketUniverse

# --- Configuration ---
# We will define a minimal configuration dictionary here to initialize the system.
# This avoids relying on external YAML files for this simple backtest.

MINIMAL_CONFIG = {
    "symbols": ["BTC-USD"],
    "timeframes": ["1h"],
    "universe_config": {
        "n_qubits": 4,
        "scr_depth": 5,
    },
    "risk": {
        "max_drawdown": 0.15,
        "max_risk_per_trade": 0.01
    },
    "metacognition": {
        "enabled": False # Keep it simple for now
    }
    # Add other necessary minimal configs as we discover them
}

async def run_backtest():
    """
    Main function to run the backtest.
    """
    logging.info("--- Starting QUALIA Backtest ---")

    # 1. Initialize Core Components
    logging.info("Initializing core components...")
    
    # For this test, we'll use the Holographic Universe as the quantum universe
    holographic_universe = HolographicMarketUniverse()
    
    # The QASTCore is the main brain
    qast_core = TradingQASTCore(
        config=MINIMAL_CONFIG,
        quantum_universe=holographic_universe
    )
    
    # Manually initialize async components if needed
    await qast_core.initialize()
    
    logging.info("Components initialized.")

    # 2. Load Historical Data
    logging.info("Loading historical market data for BTC-USD...")
    # btc_data = yf.download('BTC-USD', start='2023-01-01', end='2023-01-31', interval='1h')
    # A mock dataframe to avoid network calls during testing
    mock_data = {
        'Open': [20000, 20100, 20050, 20200, 20150],
        'High': [20150, 20200, 20100, 20250, 20200],
        'Low': [19950, 20000, 20000, 20150, 20100],
        'Close': [20100, 20050, 20200, 20150, 20180],
        'Volume': [1000, 1200, 800, 1500, 900]
    }
    btc_data = pd.DataFrame(mock_data, index=pd.to_datetime(['2023-01-01 00:00', '2023-01-01 01:00', '2023-01-01 02:00', '2023-01-01 03:00', '2023-01-01 04:00']))

    if btc_data.empty:
        logging.error("Failed to load market data. Aborting.")
        return

    logging.info(f"Loaded {len(btc_data)} data points.")

    # 3. Run the Backtest Loop
    logging.info("Starting backtest loop...")
    for index, row in btc_data.iterrows():
        logging.info(f"--- Processing candle for {index} ---")

        # a. Format market data for the QASTCore
        # The core expects a specific dictionary structure. We'll mock it here.
        market_data_snapshot = {
            "kucoin": { # Mocking exchange name
                "BTC-USD": {
                    "ohlcv": {"1h": pd.DataFrame([row])},
                    "ticker": {
                        "last": row['Close'],
                        "bid": row['Close'] - 1,
                        "ask": row['Close'] + 1,
                        "baseVolume": row['Volume'],
                        "high": row['High'],
                        "low": row['Low'],
                        "percentage": 0.0,  # Mock percentage change
                        "change": 0.0,      # Mock price change
                        "vwap": row['Close'] # Mock VWAP (Volume Weighted Average Price)
                    }
                }
            }
        }

        # b. Run a single processing cycle of the QASTCore
        try:
            cycle_result = await qast_core._process_market_cycle(external_market_data=market_data_snapshot)

            # c. Log the results for this cycle
            if cycle_result and cycle_result.get('qualia_state'):
                qualia_state = cycle_result['qualia_state']
                logging.info(f"  -> QualiaState generated: Coherence={qualia_state.coherence_level:.4f}, Entanglement={qualia_state.quantum_entanglement:.4f}")

                if cycle_result.get('signals'):
                    logging.info(f"  -> Signals generated: {cycle_result['signals']}")
                else:
                    logging.info("  -> No signals generated for this cycle.")
            else:
                logging.warning("  -> QASTCore cycle did not produce a valid result.")

        except Exception as e:
            logging.error(f"An error occurred during a cycle: {e}", exc_info=True)

    logging.info("--- Backtest Loop Finished ---")

    # 4. Shutdown
    await qast_core.shutdown()
    logging.info("--- QUALIA System Shutdown ---")


if __name__ == "__main__":
    # To run the backtest:
    # Ensure you have the necessary dependencies installed:
    # pip install pandas yfinance
    
    # Since this is an async script, run it with asyncio
    try:
        asyncio.run(run_backtest())
    except KeyboardInterrupt:
        logging.info("Backtest interrupted by user.")

