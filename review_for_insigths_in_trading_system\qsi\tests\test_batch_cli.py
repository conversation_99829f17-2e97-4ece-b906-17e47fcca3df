import os
import tempfile
import sys
from unittest import mock

sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))
import batch_cli


def test_batch_cli_runs_evaluate_for_each_repo():
    repos = [
        "https://github.com/example/repo1",
        "https://github.com/example/repo2",
    ]
    with tempfile.TemporaryDirectory() as tmpdir:
        file_path = os.path.join(tmpdir, "repos.txt")
        with open(file_path, "w", encoding="utf-8") as f:
            f.write("\n".join(repos))

        with mock.patch("qsi.system.QSI.evaluate", return_value={"decision": "APROVAR", "metrics": {}}) as mock_eval:
            with mock.patch.object(sys, "argv", ["batch_cli.py", file_path]):
                batch_cli.main()
            assert mock_eval.call_count == len(repos)
