import numpy as np
from functools import wraps
from typing import Any, Callable


def validate_quantum_state(func: Callable) -> Callable:
    """Decorador de validação de estados quânticos.

    Garante que vetores de estado estejam normalizados e com dimensões
    compatíveis com ``self.dimensions``. Suporta estados únicos ou em lote.
    """

    @wraps(func)
    def wrapper(self: Any, state: np.ndarray, *args, **kwargs) -> Any:
        state = np.asarray(state, dtype=np.complex128)

        if state.ndim == 1:
            states = state[None, :]
        else:
            states = state

        if not np.allclose(np.sum(np.abs(states) ** 2, axis=1), 1.0, atol=1e-6):
            raise ValueError("Estado(s) quântico(s) de entrada não normalizado(s).")

        if states.shape[-1] != self.dimensions:
            raise ValueError(
                f"Dimensões de estado inválidas. Esperado: {self.dimensions}, Recebido: {states.shape[-1]}"
            )

        return func(self, state, *args, **kwargs)

    return wrapper
